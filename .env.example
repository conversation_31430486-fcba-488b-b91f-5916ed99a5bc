# M1: Core Infrastructure Foundation - Environment Configuration Template
# Copy this file to .env.local and fill in your local values

# Database Configuration
# Business Database URL (application/business data)
DATABASE_BUSINESS_URL=************************************/oa_business_db

# OA Framework Configuration Database URL (framework config only)
DATABASE_CONFIG_URL=************************************/oaf_config_db

# Node Environment
NODE_ENV=development|staging|production

# Server Configuration
SERVER_PORT=3000
SERVER_HOST=localhost

# Logging Configuration
LOG_LEVEL=debug|info|warn|error

# Security Configuration
JWT_SECRET=your-secret-jwt-key
ENCRYPTION_KEY=your-encryption-key

# Configuration Management
CONFIG_CACHE_TTL_MS=5000
MAX_CONFIG_CACHE_SIZE=1000

# Database Performance Targets (Rule 03 compliance)
DATABASE_QUERY_TIMEOUT_MS=10000
DATABASE_CONNECTION_TIMEOUT_MS=5000

# Health Monitoring
HEALTH_CHECK_INTERVAL_MS=30000
ALERT_THRESHOLD_MEMORY_PERCENT=80
ALERT_THRESHOLD_QUERY_TIME_MS=100
