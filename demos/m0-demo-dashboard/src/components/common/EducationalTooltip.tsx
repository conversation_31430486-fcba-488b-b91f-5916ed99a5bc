/**
 * ============================================================================
 * AI CONTEXT: EducationalTooltip - M0 Educational System Component
 * Purpose: Reusable tooltip component for M0 Foundation educational content
 * Complexity: Moderate - Educational content delivery with accessibility
 * AI Navigation: 4 sections, educational content domain
 * Lines: ~200 / Target limit: 300
 * ============================================================================
 */

import React, { useState } from 'react';
import {
  Tooltip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useMediaQuery,
  useTheme
} from '@mui/material';
import {
  Help as HelpIcon,
  Info as InfoIcon,
  School as LearnIcon,
  Architecture as ArchIcon,
  Security as SecurityIcon,
  Memory as MemoryIcon,
  Speed as PerformanceIcon,
  CheckCircle as FeatureIcon,
  CheckCircle
} from '@mui/icons-material';

// ============================================================================
// SECTION 1: TYPE DEFINITIONS
// AI Context: Educational tooltip interfaces and types
// ============================================================================

export interface IEducationalContent {
  title: string;
  shortDescription: string;
  detailedExplanation: string;
  technicalDetails?: string[];
  enterpriseCapabilities?: string[];
  architecturalSignificance?: string;
  relatedComponents?: string[];
  achievementHighlights?: string[];
  category: 'foundation' | 'security' | 'governance' | 'tracking' | 'integration' | 'performance';
}

export interface IEducationalTooltipProps {
  content: IEducationalContent;
  placement?: 'top' | 'bottom' | 'left' | 'right';
  size?: 'small' | 'medium' | 'large';
  variant?: 'icon' | 'chip' | 'text';
  className?: string;
}

// ============================================================================
// SECTION 2: EDUCATIONAL CONTENT CONSTANTS
// AI Context: M0 Foundation educational content database
// ============================================================================

const getCategoryIcon = (category: IEducationalContent['category']) => {
  switch (category) {
    case 'foundation': return <ArchIcon />;
    case 'security': return <SecurityIcon />;
    case 'governance': return <InfoIcon />;
    case 'tracking': return <PerformanceIcon />;
    case 'integration': return <FeatureIcon />;
    case 'performance': return <MemoryIcon />;
    default: return <HelpIcon />;
  }
};

const getCategoryColor = (category: IEducationalContent['category']): 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success' | 'default' => {
  switch (category) {
    case 'foundation': return 'primary';
    case 'security': return 'error';
    case 'governance': return 'warning';
    case 'tracking': return 'info';
    case 'integration': return 'success';
    case 'performance': return 'secondary';
    default: return 'default';
  }
};

const getCategoryButtonColor = (category: IEducationalContent['category']): 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success' | 'inherit' => {
  switch (category) {
    case 'foundation': return 'primary';
    case 'security': return 'error';
    case 'governance': return 'warning';
    case 'tracking': return 'info';
    case 'integration': return 'success';
    case 'performance': return 'secondary';
    default: return 'inherit';
  }
};

// ============================================================================
// SECTION 3: MAIN EDUCATIONAL TOOLTIP COMPONENT
// AI Context: Primary educational tooltip implementation
// ============================================================================

export default function EducationalTooltip({
  content,
  placement = 'top',
  size = 'medium',
  variant = 'icon',
  className
}: IEducationalTooltipProps) {
  const [dialogOpen, setDialogOpen] = useState(false);
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));

  const handleDialogOpen = () => setDialogOpen(true);
  const handleDialogClose = () => setDialogOpen(false);

  const renderTrigger = () => {
    const iconSize = size === 'small' ? 16 : size === 'large' ? 24 : 20;
    
    switch (variant) {
      case 'chip':
        return (
          <Chip
            icon={getCategoryIcon(content.category)}
            label="Learn More"
            size={size === 'large' ? 'medium' : 'small'}
            color={getCategoryColor(content.category)}
            variant="outlined"
            onClick={handleDialogOpen}
            sx={{ cursor: 'pointer' }}
          />
        );
      case 'text':
        return (
          <Button
            startIcon={getCategoryIcon(content.category)}
            size={size}
            color={getCategoryButtonColor(content.category)}
            onClick={handleDialogOpen}
            sx={{ textTransform: 'none' }}
          >
            Learn More
          </Button>
        );
      default:
        return (
          <IconButton
            size={size}
            color={getCategoryColor(content.category)}
            onClick={handleDialogOpen}
            sx={{
              ml: { xs: 0.25, sm: 0.5 },
              transition: 'all 0.2s ease-in-out',
              p: { xs: 0.25, sm: 0.5 },
              '&:hover': {
                transform: 'scale(1.1)'
              }
            }}
          >
            <HelpIcon sx={{
              fontSize: { xs: iconSize * 0.875, sm: iconSize },
              transition: 'all 0.2s ease-in-out'
            }} />
          </IconButton>
        );
    }
  };

  return (
    <Box className={className} sx={{ display: 'inline-flex', alignItems: 'center' }}>
      <Tooltip title={content.shortDescription} placement={placement}>
        {renderTrigger()}
      </Tooltip>

      <Dialog
        open={dialogOpen}
        onClose={handleDialogClose}
        maxWidth="md"
        fullWidth
        fullScreen={isSmallScreen}
        PaperProps={{
          sx: {
            borderRadius: { xs: 0, sm: 2 },
            maxHeight: { xs: '100vh', sm: '80vh' },
            m: { xs: 0, sm: 2 }
          }
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Box display="flex" alignItems="center" gap={1}>
            {getCategoryIcon(content.category)}
            <Typography variant="h6" component="span">
              {content.title}
            </Typography>
            <Chip
              label={content.category.toUpperCase()}
              size="small"
              color={getCategoryColor(content.category)}
              variant="outlined"
            />
          </Box>
        </DialogTitle>

        <DialogContent dividers>
          <Typography variant="body1" paragraph>
            {content.detailedExplanation}
          </Typography>

          {content.technicalDetails && content.technicalDetails.length > 0 && (
            <>
              <Typography variant="h6" gutterBottom sx={{ mt: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                <LearnIcon /> Technical Implementation Details
              </Typography>
              <List dense>
                {content.technicalDetails.map((detail, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <FeatureIcon color="primary" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={detail} />
                  </ListItem>
                ))}
              </List>
            </>
          )}

          {content.enterpriseCapabilities && content.enterpriseCapabilities.length > 0 && (
            <>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <SecurityIcon /> Enterprise-Grade Capabilities
              </Typography>
              <List dense>
                {content.enterpriseCapabilities.map((capability, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <CheckCircle color="success" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={capability} />
                  </ListItem>
                ))}
              </List>
            </>
          )}

          {content.architecturalSignificance && (
            <>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <ArchIcon /> Architectural Significance
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {content.architecturalSignificance}
              </Typography>
            </>
          )}

          {content.achievementHighlights && content.achievementHighlights.length > 0 && (
            <>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <PerformanceIcon /> Achievement Highlights
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {content.achievementHighlights.map((highlight, index) => (
                  <Chip
                    key={index}
                    label={highlight}
                    size="small"
                    color="success"
                    variant="outlined"
                  />
                ))}
              </Box>
            </>
          )}

          {content.relatedComponents && content.relatedComponents.length > 0 && (
            <>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6" gutterBottom>
                Related M0 Components
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {content.relatedComponents.map((component, index) => (
                  <Chip
                    key={index}
                    label={component}
                    size="small"
                    variant="outlined"
                  />
                ))}
              </Box>
            </>
          )}
        </DialogContent>

        <DialogActions>
          <Button onClick={handleDialogClose} color="primary" variant="contained">
            Got it!
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

// ============================================================================
// SECTION 4: EDUCATIONAL CONTENT HELPER FUNCTIONS
// AI Context: Utility functions for educational content management
// ============================================================================

export const createEducationalContent = (
  title: string,
  shortDescription: string,
  detailedExplanation: string,
  category: IEducationalContent['category'],
  options?: Partial<IEducationalContent>
): IEducationalContent => ({
  title,
  shortDescription,
  detailedExplanation,
  category,
  ...options
});
