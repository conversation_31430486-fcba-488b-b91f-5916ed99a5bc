/**
 * ============================================================================
 * AI CONTEXT: ResponsiveLoading - M0 Demo Responsive Loading Component
 * Purpose: Reusable loading states with responsive design and smooth animations
 * Complexity: Simple - Loading state utility component
 * AI Navigation: 3 sections, loading state domain
 * Lines: ~180 / Target limit: 300
 * ============================================================================
 */

import React from 'react';
import {
  Box,
  CircularProgress,
  LinearProgress,
  Typography,
  Skeleton,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  SxProps,
  Theme
} from '@mui/material';
import { ResponsiveContainer } from './ResponsiveContainer';

// ============================================================================
// SECTION 1: TYPE DEFINITIONS
// AI Context: Loading component interfaces and types
// ============================================================================

export interface ResponsiveLoadingProps {
  /** Loading variant */
  variant?: 'circular' | 'linear' | 'skeleton' | 'card' | 'dashboard';
  /** Loading message */
  message?: string;
  /** Size of the loading indicator */
  size?: 'small' | 'medium' | 'large';
  /** Whether to show the loading message */
  showMessage?: boolean;
  /** Custom height for skeleton/card variants */
  height?: number | string;
  /** Number of skeleton lines */
  lines?: number;
  /** Additional styling */
  sx?: SxProps<Theme>;
}

export interface LoadingSkeletonProps {
  /** Number of skeleton lines */
  lines?: number;
  /** Height of each line */
  lineHeight?: number;
  /** Whether to show avatar skeleton */
  showAvatar?: boolean;
  /** Custom spacing between lines */
  spacing?: number;
}

// ============================================================================
// SECTION 2: RESPONSIVE LOADING COMPONENT
// AI Context: Main loading component with multiple variants
// ============================================================================

export const ResponsiveLoading: React.FC<ResponsiveLoadingProps> = ({
  variant = 'circular',
  message = 'Loading...',
  size = 'medium',
  showMessage = true,
  height = 200,
  lines = 3,
  sx
}) => {
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const isMediumScreen = useMediaQuery(theme.breakpoints.between('sm', 'md'));

  const getSizeValue = (): number => {
    const sizeMap = {
      small: isSmallScreen ? 24 : isMediumScreen ? 32 : 40,
      large: isSmallScreen ? 48 : isMediumScreen ? 56 : 64,
      medium: isSmallScreen ? 32 : isMediumScreen ? 40 : 48
    };
    return sizeMap[size];
  };

  const getFontSize = () => {
    switch (size) {
      case 'small': return { xs: '0.75rem', sm: '0.875rem' };
      case 'large': return { xs: '1rem', sm: '1.125rem', md: '1.25rem' };
      default: return { xs: '0.875rem', sm: '1rem' };
    }
  };

  // Circular loading
  if (variant === 'circular') {
    return (
      <ResponsiveContainer
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: height,
          gap: { xs: 1, sm: 2 },
          ...sx
        }}
      >
        <CircularProgress 
          size={getSizeValue()}
          sx={{
            color: 'primary.main',
            animation: 'spin 1s linear infinite',
            '@keyframes spin': {
              '0%': { transform: 'rotate(0deg)' },
              '100%': { transform: 'rotate(360deg)' }
            }
          }}
        />
        {showMessage && (
          <Typography 
            variant="body2" 
            color="text.secondary"
            sx={{ 
              fontSize: getFontSize(),
              textAlign: 'center',
              animation: 'pulse 1.5s ease-in-out infinite',
              '@keyframes pulse': {
                '0%, 100%': { opacity: 0.7 },
                '50%': { opacity: 1 }
              }
            }}
          >
            {message}
          </Typography>
        )}
      </ResponsiveContainer>
    );
  }

  // Linear loading
  if (variant === 'linear') {
    return (
      <Box sx={{ width: '100%', ...sx }}>
        <LinearProgress 
          sx={{
            height: { xs: 4, sm: 6 },
            borderRadius: 2,
            backgroundColor: 'grey.200',
            '& .MuiLinearProgress-bar': {
              borderRadius: 2
            }
          }}
        />
        {showMessage && (
          <Typography 
            variant="body2" 
            color="text.secondary"
            sx={{ 
              mt: 1,
              fontSize: getFontSize(),
              textAlign: 'center'
            }}
          >
            {message}
          </Typography>
        )}
      </Box>
    );
  }

  // Skeleton loading
  if (variant === 'skeleton') {
    return (
      <LoadingSkeleton 
        lines={lines} 
        lineHeight={size === 'small' ? 16 : size === 'large' ? 24 : 20}
        spacing={size === 'small' ? 0.5 : size === 'large' ? 1.5 : 1}
      />
    );
  }

  // Card loading
  if (variant === 'card') {
    return (
      <Card sx={{ ...sx }}>
        <CardContent sx={{ p: { xs: 2, sm: 2.5, md: 3 } }}>
          <LoadingSkeleton lines={lines} showAvatar />
        </CardContent>
      </Card>
    );
  }

  // Dashboard loading
  if (variant === 'dashboard') {
    return (
      <ResponsiveContainer spacing="normal" sx={sx}>
        <Box sx={{ mb: 3 }}>
          <Skeleton
            variant="text"
            width="60%"
            sx={{ mb: 1, height: { xs: 32, sm: 40, md: 48 } }}
          />
          <Skeleton
            variant="text"
            width="40%"
            sx={{ height: { xs: 16, sm: 20, md: 24 } }}
          />
        </Box>
        
        <Box 
          sx={{
            display: 'grid',
            gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)', md: 'repeat(4, 1fr)' },
            gap: { xs: 2, sm: 2.5, md: 3 },
            mb: 3
          }}
        >
          {Array.from({ length: 4 }).map((_, index) => (
            <Card key={index}>
              <CardContent>
                <Skeleton variant="text" width="70%" height={20} />
                <Skeleton variant="text" width="50%" height={32} sx={{ mt: 1 }} />
              </CardContent>
            </Card>
          ))}
        </Box>
        
        <Card>
          <CardContent>
            <LoadingSkeleton lines={6} />
          </CardContent>
        </Card>
      </ResponsiveContainer>
    );
  }

  return null;
};

// ============================================================================
// SECTION 3: LOADING SKELETON COMPONENT
// AI Context: Skeleton loading component for content placeholders
// ============================================================================

export const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({
  lines = 3,
  lineHeight = 20,
  showAvatar = false,
  spacing = 1
}) => {
  return (
    <Box>
      {showAvatar && (
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Skeleton
            variant="circular"
            sx={{ mr: 2, width: { xs: 32, sm: 40 }, height: { xs: 32, sm: 40 } }}
          />
          <Box sx={{ flex: 1 }}>
            <Skeleton variant="text" width="60%" height={lineHeight} />
            <Skeleton variant="text" width="40%" height={lineHeight * 0.8} />
          </Box>
        </Box>
      )}
      
      {Array.from({ length: lines }).map((_, index) => (
        <Skeleton
          key={index}
          variant="text"
          width={index === lines - 1 ? '80%' : '100%'}
          height={lineHeight}
          sx={{ 
            mb: spacing,
            '&:last-child': { mb: 0 }
          }}
        />
      ))}
    </Box>
  );
};

export default ResponsiveLoading;
