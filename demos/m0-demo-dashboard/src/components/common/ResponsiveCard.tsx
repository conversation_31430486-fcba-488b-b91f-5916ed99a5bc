/**
 * ============================================================================
 * AI CONTEXT: ResponsiveCard - M0 Demo Responsive Card Component
 * Purpose: Reusable responsive card with consistent styling and animations
 * Complexity: Simple - Card layout utility component
 * AI Navigation: 3 sections, responsive design domain
 * Lines: ~200 / Target limit: 300
 * ============================================================================
 */

import React from 'react';
import {
  Card,
  CardContent,
  CardProps,
  Typography,
  Box,
  IconButton,
  Tooltip,
  SxProps,
  Theme
} from '@mui/material';
import { Help as HelpIcon } from '@mui/icons-material';

// ============================================================================
// SECTION 1: TYPE DEFINITIONS
// AI Context: Responsive card interfaces and types
// ============================================================================

export interface ResponsiveCardProps extends Omit<CardProps, 'sx'> {
  /** Card title */
  title?: string;
  /** Card subtitle */
  subtitle?: string;
  /** Icon to display with title */
  icon?: React.ReactNode;
  /** Whether to show hover effects */
  interactive?: boolean;
  /** Card size variant */
  size?: 'compact' | 'normal' | 'large';
  /** Help tooltip content */
  helpContent?: string;
  /** Custom padding */
  padding?: 'compact' | 'normal' | 'spacious';
  /** Additional styling */
  sx?: CardProps['sx'];
}

export interface MetricCardProps extends ResponsiveCardProps {
  /** Main metric value */
  value: string | number;
  /** Metric label */
  label: string;
  /** Value color */
  valueColor?: string;
  /** Metric icon */
  metricIcon?: React.ReactNode;
  /** Whether to show trend indicator */
  showTrend?: boolean;
  /** Trend value (positive/negative percentage) */
  trendValue?: number;
}

// ============================================================================
// SECTION 2: RESPONSIVE CARD COMPONENT
// AI Context: Main responsive card with consistent styling
// ============================================================================

export const ResponsiveCard: React.FC<ResponsiveCardProps> = ({
  title,
  subtitle,
  icon,
  interactive = false,
  size = 'normal',
  helpContent,
  padding = 'normal',
  sx,
  children,
  ...props
}) => {
  const paddingValues = {
    compact: { xs: 1.5, sm: 2, md: 2.5 },
    normal: { xs: 2, sm: 2.5, md: 3 },
    spacious: { xs: 2.5, sm: 3, md: 4 }
  };

  const cardSx = {
    transition: 'all 0.3s ease-in-out',
    ...(interactive && {
      '&:hover': {
        transform: 'translateY(-2px)',
        boxShadow: 3
      }
    }),
    ...sx
  };

  const contentSx = {
    p: paddingValues[padding],
    '&:last-child': {
      pb: paddingValues[padding]
    }
  };

  return (
    <Card sx={cardSx} {...props}>
      <CardContent sx={contentSx}>
        {(title || subtitle) && (
          <Box mb={children ? 2 : 0}>
            {title && (
              <Box display="flex" alignItems="center" gap={1} mb={subtitle ? 0.5 : 0}>
                {icon}
                <Typography 
                  variant={size === 'compact' ? 'h6' : size === 'large' ? 'h4' : 'h5'}
                  component="h2"
                  sx={{ 
                    fontSize: { 
                      xs: size === 'compact' ? '1rem' : size === 'large' ? '1.5rem' : '1.125rem',
                      sm: size === 'compact' ? '1.125rem' : size === 'large' ? '2rem' : '1.25rem',
                      md: size === 'compact' ? '1.25rem' : size === 'large' ? '2.125rem' : '1.5rem'
                    },
                    fontWeight: 'bold'
                  }}
                >
                  {title}
                </Typography>
                {helpContent && (
                  <Tooltip title={helpContent} placement="top">
                    <IconButton size="small" sx={{ ml: 0.5 }}>
                      <HelpIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}
              </Box>
            )}
            {subtitle && (
              <Typography 
                variant="body2" 
                color="text.secondary"
                sx={{ 
                  fontSize: { xs: '0.75rem', sm: '0.875rem' }
                }}
              >
                {subtitle}
              </Typography>
            )}
          </Box>
        )}
        {children}
      </CardContent>
    </Card>
  );
};

// ============================================================================
// SECTION 3: METRIC CARD COMPONENT
// AI Context: Specialized card for displaying metrics with responsive typography
// ============================================================================

export const MetricCard: React.FC<MetricCardProps> = ({
  value,
  label,
  valueColor = 'primary.main',
  metricIcon,
  showTrend = false,
  trendValue = 0,
  ...cardProps
}) => {
  const trendColor = trendValue > 0 ? 'success.main' : trendValue < 0 ? 'error.main' : 'text.secondary';
  const trendSymbol = trendValue > 0 ? '↗' : trendValue < 0 ? '↘' : '→';

  return (
    <ResponsiveCard interactive {...cardProps}>
      <Box display="flex" alignItems="center" justifyContent="space-between">
        <Box>
          <Typography 
            color="textSecondary" 
            gutterBottom
            variant="body2"
            sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
          >
            {label}
          </Typography>
          <Typography 
            variant="h4" 
            sx={{ 
              color: valueColor,
              fontSize: { xs: '1.5rem', sm: '2rem', md: '2.125rem' },
              fontWeight: 'bold'
            }}
          >
            {value}
          </Typography>
          {showTrend && (
            <Typography 
              variant="caption" 
              sx={{ 
                color: trendColor,
                fontSize: { xs: '0.6rem', sm: '0.75rem' },
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
                mt: 0.5
              }}
            >
              <span>{trendSymbol}</span>
              {Math.abs(trendValue)}%
            </Typography>
          )}
        </Box>
        {metricIcon && (
          <Box sx={{ opacity: 0.8 }}>
            {React.cloneElement(metricIcon as React.ReactElement<{ sx?: SxProps<Theme> }>, {
              sx: {
                fontSize: { xs: 32, sm: 36, md: 40 },
                ...((metricIcon as React.ReactElement<{ sx?: SxProps<Theme> }>).props?.sx || {})
              }
            })}
          </Box>
        )}
      </Box>
    </ResponsiveCard>
  );
};

export default ResponsiveCard;
