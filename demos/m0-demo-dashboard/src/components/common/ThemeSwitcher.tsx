'use client';

/**
 * ============================================================================
 * Theme Switcher Component
 * ============================================================================
 * 
 * Purpose: Manual theme switching control for testing and user preference
 * Features:
 * - Toggle between Primary and Secondary themes
 * - Visual indicator of current theme
 * - Smooth theme transitions
 * 
 * Author: AI Assistant (Theme Enhancement)
 * Created: 2025-12-31
 * ============================================================================
 */

import React from 'react';
import { Box, But<PERSON>, <PERSON>, <PERSON>, Typography } from '@mui/material';
import { Palette as PaletteIcon, SwapHoriz as SwapIcon } from '@mui/icons-material';
import { useThemeMode } from '@/theme/ThemeProvider';

export default function ThemeSwitcher() {
  const { themeMode, setThemeMode } = useThemeMode();

  const handleToggle = () => {
    setThemeMode(themeMode === 'primary' ? 'secondary' : 'primary');
  };

  return (
    <Paper
      elevation={2}
      sx={{
        p: 2,
        display: 'flex',
        alignItems: 'center',
        gap: 2,
        borderRadius: 2,
      }}
    >
      <PaletteIcon color="primary" />
      
      <Box sx={{ flex: 1 }}>
        <Typography variant="body2" color="text.secondary">
          Current Theme
        </Typography>
        <Chip
          label={themeMode === 'primary' ? 'Primary (Dark-themed)' : 'Secondary (Crypto Trading)'}
          color={themeMode === 'primary' ? 'primary' : 'secondary'}
          size="small"
          sx={{ mt: 0.5 }}
        />
      </Box>

      <Button
        variant="outlined"
        startIcon={<SwapIcon />}
        onClick={handleToggle}
        size="small"
      >
        Switch Theme
      </Button>
    </Paper>
  );
}

