/**
 * ============================================================================
 * AI CONTEXT: ResponsiveButtonGroup - M0 Demo Responsive Button Component
 * Purpose: Reusable responsive button group with consistent mobile/desktop layouts
 * Complexity: Simple - Button layout utility component
 * AI Navigation: 2 sections, responsive design domain
 * Lines: ~120 / Target limit: 300
 * ============================================================================
 */

import React from 'react';
import { Box, Button, ButtonProps, CircularProgress, SxProps, Theme } from '@mui/material';
import { ResponsiveGrid } from './ResponsiveContainer';

// ============================================================================
// SECTION 1: TYPE DEFINITIONS
// AI Context: Responsive button group interfaces and types
// ============================================================================

export interface ResponsiveButtonConfig extends Omit<ButtonProps, 'children'> {
  /** Button label */
  label: string;
  /** Loading state for this specific button */
  loading?: boolean;
  /** Icon to display */
  icon?: React.ReactNode;
  /** Loading icon override */
  loadingIcon?: React.ReactNode;
}

export interface ResponsiveButtonGroupProps {
  /** Array of button configurations */
  buttons: ResponsiveButtonConfig[];
  /** Layout orientation */
  orientation?: 'horizontal' | 'vertical' | 'responsive';
  /** Button size */
  size?: 'small' | 'medium' | 'large';
  /** Whether buttons should be full width on mobile */
  fullWidthMobile?: boolean;
  /** Custom spacing between buttons */
  spacing?: number | { xs?: number; sm?: number; md?: number };
  /** Maximum buttons per row on different breakpoints */
  maxButtonsPerRow?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
  };
  /** Additional styling */
  sx?: SxProps<Theme>;
}

// ============================================================================
// SECTION 2: RESPONSIVE BUTTON GROUP COMPONENT
// AI Context: Main responsive button group with adaptive layouts
// ============================================================================

export const ResponsiveButtonGroup: React.FC<ResponsiveButtonGroupProps> = ({
  buttons,
  orientation = 'responsive',
  size = 'medium',
  fullWidthMobile = true,
  spacing = { xs: 1.5, sm: 2 },
  maxButtonsPerRow = { xs: 1, sm: 2, md: 4 },
  sx
}) => {
  const renderButton = (buttonConfig: ResponsiveButtonConfig, index: number) => {
    const {
      label,
      loading = false,
      icon,
      loadingIcon,
      startIcon,
      ...buttonProps
    } = buttonConfig;

    const buttonStartIcon = loading 
      ? (loadingIcon || <CircularProgress size={16} color="inherit" />)
      : (startIcon || icon);

    return (
      <Button
        key={index}
        size={size}
        startIcon={buttonStartIcon}
        disabled={loading}
        fullWidth={fullWidthMobile}
        sx={{
          py: { xs: 1, sm: 1.5 },
          fontSize: { xs: '0.75rem', sm: '0.875rem' },
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            transform: 'translateY(-1px)',
            boxShadow: 2
          },
          '&:disabled': {
            transform: 'none'
          }
        }}
        {...buttonProps}
      >
        {label}
      </Button>
    );
  };

  // Responsive grid layout
  if (orientation === 'responsive') {
    return (
      <ResponsiveGrid
        columns={maxButtonsPerRow}
        gap={spacing}
        sx={sx}
      >
        {buttons.map(renderButton)}
      </ResponsiveGrid>
    );
  }

  // Horizontal layout
  if (orientation === 'horizontal') {
    return (
      <Box
        sx={{
          display: 'flex',
          gap: typeof spacing === 'number' ? spacing : spacing,
          flexWrap: 'wrap',
          ...sx
        }}
      >
        {buttons.map(renderButton)}
      </Box>
    );
  }

  // Vertical layout
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: typeof spacing === 'number' ? spacing : spacing,
        ...sx
      }}
    >
      {buttons.map(renderButton)}
    </Box>
  );
};

export default ResponsiveButtonGroup;
