/**
 * Integration Testing Display Component
 * Purpose: Comprehensive integration testing console with real-time monitoring
 * Features: Test results, suite management, system health, performance metrics
 */

'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  CardHeader,
  Chip,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Button,
  IconButton,
  Tooltip,
  Badge,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  PlayArrow as RunIcon,
  Stop as StopIcon,
  Refresh as RefreshIcon,
  CheckCircle as PassedIcon,
  Error as FailedIcon,
  Schedule as PendingIcon,
  PlayCircleOutline as RunningIcon,
  ExpandMore as ExpandMoreIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  BugReport as ErrorIcon,
} from '@mui/icons-material';
import type {
  IIntegrationTestResultsResponse,
  ITestSuitesResponse,
  ISystemHealthResponse,
  IIntegrationTestResult,
  ITestSuite,
  ISystemHealthStatus,
} from '../../types/integration.types';

interface IntegrationTestingDisplayProps {
  testResultsData?: IIntegrationTestResultsResponse;
  testSuitesData?: ITestSuitesResponse;
  systemHealthData?: ISystemHealthResponse;
  loading?: boolean;
  error?: Error | null;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`integration-tabpanel-${index}`}
      aria-labelledby={`integration-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `integration-tab-${index}`,
    'aria-controls': `integration-tabpanel-${index}`,
  };
}

// Status color mapping
const getStatusColor = (status: string): 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success' | 'default' => {
  switch (status) {
    case 'passed':
    case 'healthy':
    case 'active':
      return 'success';
    case 'failed':
    case 'critical':
    case 'offline':
      return 'error';
    case 'running':
    case 'degraded':
    case 'maintenance':
      return 'warning';
    case 'pending':
    case 'inactive':
      return 'info';
    default:
      return 'default';
  }
};

// Status icon mapping
const getStatusIcon = (status: string) => {
  switch (status) {
    case 'passed':
      return <PassedIcon color="success" />;
    case 'failed':
      return <FailedIcon color="error" />;
    case 'running':
      return <RunningIcon color="warning" />;
    case 'pending':
      return <PendingIcon color="info" />;
    default:
      return <InfoIcon />;
  }
};

export default function IntegrationTestingDisplay({
  testResultsData,
  testSuitesData,
  systemHealthData,
  loading = false,
  error = null,
}: IntegrationTestingDisplayProps) {
  const [mounted, setMounted] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [expandedAccordion, setExpandedAccordion] = useState<string | false>(false);

  // Hydration safety
  useEffect(() => {
    setMounted(true);
  }, []);

  // Handle tab changes
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Handle accordion changes
  const handleAccordionChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedAccordion(isExpanded ? panel : false);
  };

  if (!mounted) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={40} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        Failed to load integration testing data: {error.message}
      </Alert>
    );
  }

  return (
    <Box sx={{ width: '100%' }}>
      {/* Header Section */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom sx={{ color: 'primary.main', fontWeight: 'bold' }}>
          🔗 Integration Testing Console
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Comprehensive integration testing and system health monitoring for M0 components
        </Typography>
      </Box>

      {/* Summary Cards */}
      <Box display="flex" flexWrap="wrap" gap={3} sx={{ mb: 3 }}>
        {/* Test Results Summary */}
        <Box sx={{ flex: { xs: '1 1 100%', md: '1 1 calc(33.333% - 16px)' }, minWidth: 0 }}>
          <Card>
            <CardHeader
              title="Test Results"
              titleTypographyProps={{ variant: 'h6' }}
              action={
                <Tooltip title="Refresh test results">
                  <IconButton size="small">
                    <RefreshIcon />
                  </IconButton>
                </Tooltip>
              }
            />
            <CardContent>
              {loading ? (
                <CircularProgress size={24} />
              ) : (
                <Box>
                  <Typography variant="h3" color="primary" gutterBottom>
                    {testResultsData?.summary?.total || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Total Tests
                  </Typography>
                  <Box sx={{ mt: 2 }}>
                    <Box display="flex" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
                      <Typography variant="body2">Success Rate</Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {testResultsData?.summary?.successRate?.toFixed(1) || '0.0'}%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={testResultsData?.summary?.successRate || 0}
                      color="success"
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>
        </Box>

        {/* Test Suites Summary */}
        <Box sx={{ flex: { xs: '1 1 100%', md: '1 1 calc(33.333% - 16px)' }, minWidth: 0 }}>
          <Card>
            <CardHeader
              title="Test Suites"
              titleTypographyProps={{ variant: 'h6' }}
            />
            <CardContent>
              {loading ? (
                <CircularProgress size={24} />
              ) : (
                <Box>
                  <Typography variant="h3" color="secondary" gutterBottom>
                    {testSuitesData?.summary?.active || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Active Suites
                  </Typography>
                  <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    <Chip
                      label={`${testSuitesData?.summary?.total || 0} Total`}
                      size="small"
                      variant="outlined"
                    />
                    <Chip
                      label={`${testSuitesData?.summary?.totalTests || 0} Tests`}
                      size="small"
                      variant="outlined"
                      color="primary"
                    />
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>
        </Box>

        {/* System Health Summary */}
        <Box sx={{ flex: { xs: '1 1 100%', md: '1 1 calc(33.333% - 16px)' }, minWidth: 0 }}>
          <Card>
            <CardHeader
              title="System Health"
              titleTypographyProps={{ variant: 'h6' }}
              action={
                systemHealthData?.summary?.criticalAlerts ? (
                  <Badge badgeContent={systemHealthData.summary.criticalAlerts} color="error">
                    <WarningIcon color="error" />
                  </Badge>
                ) : null
              }
            />
            <CardContent>
              {loading ? (
                <CircularProgress size={24} />
              ) : (
                <Box>
                  <Typography variant="h3" color="success.main" gutterBottom>
                    {systemHealthData?.summary?.overallHealth?.toFixed(1) || '0.0'}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Overall Health
                  </Typography>
                  <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    <Chip
                      label={`${systemHealthData?.summary?.healthy || 0} Healthy`}
                      size="small"
                      color="success"
                      variant="outlined"
                    />
                    <Chip
                      label={`${systemHealthData?.summary?.degraded || 0} Degraded`}
                      size="small"
                      color="warning"
                      variant="outlined"
                    />
                    {(systemHealthData?.summary?.critical || 0) > 0 && (
                      <Chip
                        label={`${systemHealthData?.summary?.critical || 0} Critical`}
                        size="small"
                        color="error"
                        variant="outlined"
                      />
                    )}
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>
        </Box>
      </Box>

      {/* Tabs Section */}
      <Paper elevation={1}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab
            icon={<RunIcon />}
            label="Test Results"
            {...a11yProps(0)}
            sx={{ minHeight: 72 }}
          />
          <Tab
            icon={<InfoIcon />}
            label="Test Suites"
            {...a11yProps(1)}
            sx={{ minHeight: 72 }}
          />
          <Tab
            icon={<WarningIcon />}
            label="System Health"
            {...a11yProps(2)}
            sx={{ minHeight: 72 }}
          />
        </Tabs>

        {/* Tab Panels */}
        <TabPanel value={activeTab} index={0}>
          {/* Test Results Panel */}
          {loading ? (
            <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
              <CircularProgress />
            </Box>
          ) : (
            <Box>
              {testResultsData?.results && testResultsData.results.length > 0 ? (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Status</TableCell>
                        <TableCell>Test Name</TableCell>
                        <TableCell>Suite</TableCell>
                        <TableCell>Category</TableCell>
                        <TableCell>Duration</TableCell>
                        <TableCell>Components</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {testResultsData.results.slice(0, 10).map((result) => (
                        <TableRow key={result.testId}>
                          <TableCell>
                            <Box display="flex" alignItems="center" gap={1}>
                              {getStatusIcon(result.status)}
                              <Chip
                                label={result.status.toUpperCase()}
                                size="small"
                                color={getStatusColor(result.status)}
                                variant="outlined"
                              />
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" fontWeight="medium">
                              {result.testName}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {result.description}
                            </Typography>
                          </TableCell>
                          <TableCell>{result.testSuite}</TableCell>
                          <TableCell>
                            <Chip
                              label={result.category}
                              size="small"
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell>
                            {result.duration > 0 ? `${(result.duration / 1000).toFixed(1)}s` : '-'}
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                              {result.components.slice(0, 2).map((component) => (
                                <Chip
                                  key={component}
                                  label={component}
                                  size="small"
                                  variant="outlined"
                                  sx={{ fontSize: '0.7rem' }}
                                />
                              ))}
                              {result.components.length > 2 && (
                                <Chip
                                  label={`+${result.components.length - 2}`}
                                  size="small"
                                  variant="outlined"
                                  sx={{ fontSize: '0.7rem' }}
                                />
                              )}
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Alert severity="info">
                  No test results available. Run integration tests to see results here.
                </Alert>
              )}
            </Box>
          )}
        </TabPanel>

        <TabPanel value={activeTab} index={1}>
          {/* Test Suites Panel */}
          {loading ? (
            <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
              <CircularProgress />
            </Box>
          ) : (
            <Box>
              {testSuitesData?.suites && testSuitesData.suites.length > 0 ? (
                testSuitesData.suites.map((suite) => (
                  <Accordion
                    key={suite.suiteId}
                    expanded={expandedAccordion === suite.suiteId}
                    onChange={handleAccordionChange(suite.suiteId)}
                  >
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                        <Typography variant="h6">{suite.suiteName}</Typography>
                        <Chip
                          label={suite.status.toUpperCase()}
                          size="small"
                          color={getStatusColor(suite.status)}
                          variant="outlined"
                        />
                        <Box sx={{ ml: 'auto', display: 'flex', gap: 1 }}>
                          <Chip
                            label={`${suite.statistics.totalTests} tests`}
                            size="small"
                            variant="outlined"
                          />
                          <Chip
                            label={`${suite.statistics.successRate.toFixed(1)}% success`}
                            size="small"
                            color="success"
                            variant="outlined"
                          />
                        </Box>
                      </Box>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Typography variant="body2" color="text.secondary" paragraph>
                        {suite.description}
                      </Typography>
                      <Box display="flex" flexWrap="wrap" gap={2}>
                        <Box sx={{ flex: { xs: '1 1 100%', md: '1 1 calc(50% - 8px)' }, minWidth: 0 }}>
                          <Typography variant="subtitle2" gutterBottom>
                            Configuration
                          </Typography>
                          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                            <Typography variant="body2">
                              Timeout: {(suite.configuration.timeout / 1000).toFixed(0)}s
                            </Typography>
                            <Typography variant="body2">
                              Retries: {suite.configuration.retries}
                            </Typography>
                            <Typography variant="body2">
                              Parallel: {suite.configuration.parallel ? 'Yes' : 'No'}
                            </Typography>
                            <Typography variant="body2">
                              Environment: {suite.configuration.environment}
                            </Typography>
                          </Box>
                        </Box>
                        <Box sx={{ flex: { xs: '1 1 100%', md: '1 1 calc(50% - 8px)' }, minWidth: 0 }}>
                          <Typography variant="subtitle2" gutterBottom>
                            Statistics
                          </Typography>
                          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                            <Typography variant="body2">
                              Passed: {suite.statistics.passedTests}
                            </Typography>
                            <Typography variant="body2">
                              Failed: {suite.statistics.failedTests}
                            </Typography>
                            <Typography variant="body2">
                              Avg Execution: {(suite.statistics.averageExecutionTime / 1000).toFixed(1)}s
                            </Typography>
                            <Typography variant="body2">
                              Last Run: {suite.lastRun ? new Date(suite.lastRun).toLocaleString() : 'Never'}
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                    </AccordionDetails>
                  </Accordion>
                ))
              ) : (
                <Alert severity="info">
                  No test suites configured. Configure test suites to manage integration testing.
                </Alert>
              )}
            </Box>
          )}
        </TabPanel>

        <TabPanel value={activeTab} index={2}>
          {/* System Health Panel */}
          {loading ? (
            <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
              <CircularProgress />
            </Box>
          ) : (
            <Box>
              {systemHealthData?.systems && systemHealthData.systems.length > 0 ? (
                <Box display="flex" flexWrap="wrap" gap={2}>
                  {systemHealthData.systems.map((system) => (
                    <Box key={system.systemId} sx={{ flex: { xs: '1 1 100%', md: '1 1 calc(50% - 8px)', lg: '1 1 calc(33.333% - 11px)' }, minWidth: 0 }}>
                      <Card>
                        <CardHeader
                          title={system.systemName}
                          titleTypographyProps={{ variant: 'h6' }}
                          action={
                            <Chip
                              label={system.status.toUpperCase()}
                              size="small"
                              color={getStatusColor(system.status)}
                              variant="outlined"
                            />
                          }
                        />
                        <CardContent>
                          <Box sx={{ mb: 2 }}>
                            <Box display="flex" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
                              <Typography variant="body2">Health Score</Typography>
                              <Typography variant="body2" fontWeight="bold">
                                {system.overallHealth.toFixed(1)}%
                              </Typography>
                            </Box>
                            <LinearProgress
                              variant="determinate"
                              value={system.overallHealth}
                              color={system.overallHealth >= 90 ? 'success' : system.overallHealth >= 70 ? 'warning' : 'error'}
                              sx={{ height: 8, borderRadius: 4 }}
                            />
                          </Box>
                          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                            <Typography variant="body2">
                              Components: {system.components.length}
                            </Typography>
                            <Typography variant="body2">
                              Uptime: {system.uptime.toFixed(2)}%
                            </Typography>
                            <Typography variant="body2">
                              Version: {system.version}
                            </Typography>
                            {system.alerts.length > 0 && (
                              <Box sx={{ mt: 1 }}>
                                <Chip
                                  label={`${system.alerts.length} alerts`}
                                  size="small"
                                  color="warning"
                                  variant="outlined"
                                />
                              </Box>
                            )}
                          </Box>
                        </CardContent>
                      </Card>
                    </Box>
                  ))}
                </Box>
              ) : (
                <Alert severity="info">
                  No system health data available. Check system monitoring configuration.
                </Alert>
              )}
            </Box>
          )}
        </TabPanel>
      </Paper>
    </Box>
  );
}
