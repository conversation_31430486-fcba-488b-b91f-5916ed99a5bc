/**
 * ============================================================================
 * M0.1 Task Completion Chart Component
 * ============================================================================
 * 
 * Purpose: Visualize M0.1 task completion by category
 * Features: Bar chart showing 50/50 tasks across 8 categories
 * 
 * Categories:
 * - Memory Safety (8 tasks)
 * - Performance (7 tasks)
 * - Testing (8 tasks)
 * - Documentation (6 tasks)
 * - Architecture (7 tasks)
 * - Integration (6 tasks)
 * - Quality (4 tasks)
 * - Compliance (4 tasks)
 * 
 * Author: AI Assistant (Phase 2 Day 3 Implementation)
 * Created: 2025-12-31
 * ============================================================================
 */

'use client';

import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip
} from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell
} from 'recharts';

interface TaskCompletionChartProps {
  categories: Array<{
    name: string;
    completed: number;
    total: number;
    percentage: number;
  }>;
  totalTasks: number;
  completedTasks: number;
}

export default function TaskCompletionChart({ categories, totalTasks, completedTasks }: TaskCompletionChartProps) {
  // Prepare chart data
  const chartData = categories.map(cat => ({
    name: cat.name.replace(/([A-Z])/g, ' $1').trim(),
    completed: cat.completed,
    total: cat.total,
    percentage: cat.percentage
  }));

  // Color palette for bars
  const colors = [
    '#4caf50', // Green
    '#2196f3', // Blue
    '#ff9800', // Orange
    '#9c27b0', // Purple
    '#f44336', // Red
    '#00bcd4', // Cyan
    '#ff5722', // Deep Orange
    '#3f51b5'  // Indigo
  ];

  return (
    <Card elevation={2} sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ mb: 3 }}>
          <Typography variant="h5" component="h2" sx={{ fontWeight: 'bold', mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
            📈 Task Completion by Category
            <Chip 
              label={`${completedTasks}/${totalTasks} Tasks`} 
              color="success" 
              size="small" 
              sx={{ ml: 1 }} 
            />
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Breakdown of completed tasks across all M0.1 categories
          </Typography>
        </Box>

        {/* Bar Chart */}
        <Box sx={{ width: '100%', height: 400 }}>
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={chartData}
              margin={{ top: 20, right: 30, left: 20, bottom: 80 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
              <XAxis 
                dataKey="name" 
                angle={-45}
                textAnchor="end"
                height={100}
                tick={{ fontSize: 12 }}
              />
              <YAxis 
                label={{ value: 'Tasks', angle: -90, position: 'insideLeft' }}
                tick={{ fontSize: 12 }}
              />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: 'rgba(255, 255, 255, 0.95)', 
                  border: '1px solid #ccc',
                  borderRadius: '8px',
                  padding: '12px'
                }}
                formatter={(value: number, name: string) => {
                  if (name === 'completed') return [value, 'Completed'];
                  if (name === 'total') return [value, 'Total'];
                  return [value, name];
                }}
              />
              <Legend 
                wrapperStyle={{ paddingTop: '20px' }}
                iconType="circle"
              />
              <Bar 
                dataKey="completed" 
                name="Completed Tasks"
                radius={[8, 8, 0, 0]}
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </Box>

        {/* Summary Statistics */}
        <Box sx={{ mt: 3, p: 2, backgroundColor: 'success.light', borderRadius: 2 }}>
          <Typography variant="body2" sx={{ fontWeight: 600, color: 'success.dark', textAlign: 'center' }}>
            ✅ All {totalTasks} tasks completed across {categories.length} categories (100% completion rate)
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
}

