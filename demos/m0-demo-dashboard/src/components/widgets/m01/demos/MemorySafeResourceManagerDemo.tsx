/**
 * ============================================================================
 * MemorySafeResourceManagerEnhanced Demo
 * ============================================================================
 * 
 * Purpose: Interactive demonstration of MemorySafeResourceManagerEnhanced
 * Features: Resource allocation, memory monitoring, performance metrics
 * 
 * Demonstrates:
 * - Memory-safe resource management
 * - Automatic cleanup and garbage collection
 * - Performance monitoring with resilient timing
 * - Resource pooling and reuse
 * 
 * Author: AI Assistant (Phase 3 Implementation)
 * Created: 2025-12-31
 * ============================================================================
 */

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Paper,
  LinearProgress
} from '@mui/material';
import DemoFramework, { DemoMetric, DemoStatus } from './DemoFramework';

interface ResourceStats {
  allocated: number;
  freed: number;
  active: number;
  poolSize: number;
  memoryUsage: number;
  avgAllocationTime: number;
  operations: number;
}

export default function MemorySafeResourceManagerDemo() {
  const [status, setStatus] = useState<DemoStatus>({
    running: false,
    status: 'idle',
    message: 'Click Start to begin resource management simulation'
  });

  const [stats, setStats] = useState<ResourceStats>({
    allocated: 0,
    freed: 0,
    active: 0,
    poolSize: 100,
    memoryUsage: 0,
    avgAllocationTime: 0,
    operations: 0
  });

  const [intervalId, setIntervalId] = useState<NodeJS.Timeout | null>(null);

  // Simulate resource allocation and deallocation
  const simulateResourceOperations = useCallback(() => {
    setStats(prev => {
      const shouldAllocate = Math.random() > 0.4;
      const newAllocated = shouldAllocate ? prev.allocated + 1 : prev.allocated;
      const newFreed = !shouldAllocate && prev.active > 0 ? prev.freed + 1 : prev.freed;
      const newActive = newAllocated - newFreed;
      const newMemoryUsage = Math.min((newActive / prev.poolSize) * 100, 100);
      const newOperations = prev.operations + 1;
      const newAvgTime = 2 + Math.random() * 3; // 2-5ms

      return {
        allocated: newAllocated,
        freed: newFreed,
        active: newActive,
        poolSize: prev.poolSize,
        memoryUsage: newMemoryUsage,
        avgAllocationTime: newAvgTime,
        operations: newOperations
      };
    });
  }, []);

  const handleStart = () => {
    setStatus({
      running: true,
      status: 'running',
      message: 'Simulating resource allocation and deallocation...'
    });

    const id = setInterval(simulateResourceOperations, 500);
    setIntervalId(id);
  };

  const handleStop = () => {
    if (intervalId) {
      clearInterval(intervalId);
      setIntervalId(null);
    }

    setStatus({
      running: false,
      status: 'stopped',
      message: 'Simulation stopped'
    });
  };

  const handleReset = () => {
    if (intervalId) {
      clearInterval(intervalId);
      setIntervalId(null);
    }

    setStats({
      allocated: 0,
      freed: 0,
      active: 0,
      poolSize: 100,
      memoryUsage: 0,
      avgAllocationTime: 0,
      operations: 0
    });

    setStatus({
      running: false,
      status: 'idle',
      message: 'Demo reset. Click Start to begin.'
    });
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [intervalId]);

  const metrics: DemoMetric[] = [
    {
      label: 'Resources Allocated',
      value: stats.allocated,
      color: '#4caf50'
    },
    {
      label: 'Resources Freed',
      value: stats.freed,
      color: '#2196f3'
    },
    {
      label: 'Active Resources',
      value: stats.active,
      color: '#ff9800'
    },
    {
      label: 'Avg Allocation Time',
      value: stats.avgAllocationTime.toFixed(2),
      unit: 'ms',
      color: '#9c27b0'
    }
  ];

  const codeExample = `// MemorySafeResourceManagerEnhanced Usage Example
import { MemorySafeResourceManagerEnhanced } from '@oa/modules';

// Initialize the manager
const manager = new MemorySafeResourceManagerEnhanced({
  poolSize: 100,
  autoCleanup: true,
  performanceMonitoring: true
});

// Allocate a resource
const resource = await manager.allocate('my-resource');

// Use the resource
await resource.execute();

// Resource is automatically freed when no longer needed
// Manager handles cleanup and memory safety
`;

  return (
    <DemoFramework
      title="MemorySafeResourceManagerEnhanced"
      description="Memory-safe resource management with automatic cleanup and performance monitoring"
      status={status}
      metrics={metrics}
      onStart={handleStart}
      onStop={handleStop}
      onReset={handleReset}
      codeExample={codeExample}
    >
      {/* Resource Pool Visualization */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                Memory Pool Status
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Memory Usage</Typography>
                  <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                    {stats.memoryUsage.toFixed(1)}%
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={stats.memoryUsage}
                  sx={{
                    height: 10,
                    borderRadius: 5,
                    backgroundColor: 'rgba(0,0,0,0.1)',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: stats.memoryUsage > 80 ? '#f44336' : stats.memoryUsage > 50 ? '#ff9800' : '#4caf50'
                    }
                  }}
                />
              </Box>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="caption" color="text.secondary">Pool Size</Typography>
                    <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#2196f3' }}>
                      {stats.poolSize}
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="caption" color="text.secondary">Available</Typography>
                    <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#4caf50' }}>
                      {stats.poolSize - stats.active}
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                Operation Statistics
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="caption" color="text.secondary">Total Operations</Typography>
                    <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#9c27b0' }}>
                      {stats.operations}
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="caption" color="text.secondary">Success Rate</Typography>
                    <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#4caf50' }}>
                      100%
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={12}>
                  <Paper variant="outlined" sx={{ p: 2 }}>
                    <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                      Performance
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      ✅ Memory-safe operations
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      ✅ Automatic cleanup enabled
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      ✅ Resilient timing: {stats.avgAllocationTime.toFixed(2)}ms avg
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </DemoFramework>
  );
}

