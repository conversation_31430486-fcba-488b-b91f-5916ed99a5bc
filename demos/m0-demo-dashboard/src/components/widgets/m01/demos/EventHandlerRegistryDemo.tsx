/**
 * ============================================================================
 * EventHandlerRegistryEnhanced Demo
 * ============================================================================
 * 
 * Purpose: Interactive demonstration of EventHandlerRegistryEnhanced
 * Features: Event handling, priority queuing, handler lifecycle management
 * 
 * Author: AI Assistant (Phase 3 Implementation)
 * Created: 2025-12-31
 * ============================================================================
 */

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, Typography, Paper, Chip, Box } from '@mui/material';
import DemoFramework, { DemoMetric, DemoStatus } from './DemoFramework';

interface EventStats {
  eventsProcessed: number;
  handlersRegistered: number;
  queueSize: number;
  avgProcessingTime: number;
  highPriorityEvents: number;
  lowPriorityEvents: number;
}

export default function EventHandlerRegistryDemo() {
  const [status, setStatus] = useState<DemoStatus>({
    running: false,
    status: 'idle',
    message: 'Click Start to begin event processing simulation'
  });

  const [stats, setStats] = useState<EventStats>({
    eventsProcessed: 0,
    handlersRegistered: 5,
    queueSize: 0,
    avgProcessingTime: 0,
    highPriorityEvents: 0,
    lowPriorityEvents: 0
  });

  const [intervalId, setIntervalId] = useState<NodeJS.Timeout | null>(null);

  const simulateEventProcessing = useCallback(() => {
    setStats(prev => {
      const isHighPriority = Math.random() > 0.6;
      return {
        ...prev,
        eventsProcessed: prev.eventsProcessed + 1,
        queueSize: Math.max(0, prev.queueSize + Math.floor(Math.random() * 3) - 1),
        avgProcessingTime: 1 + Math.random() * 2,
        highPriorityEvents: isHighPriority ? prev.highPriorityEvents + 1 : prev.highPriorityEvents,
        lowPriorityEvents: !isHighPriority ? prev.lowPriorityEvents + 1 : prev.lowPriorityEvents
      };
    });
  }, []);

  const handleStart = () => {
    setStatus({ running: true, status: 'running', message: 'Processing events with priority queuing...' });
    const id = setInterval(simulateEventProcessing, 600);
    setIntervalId(id);
  };

  const handleStop = () => {
    if (intervalId) clearInterval(intervalId);
    setStatus({ running: false, status: 'stopped', message: 'Event processing stopped' });
  };

  const handleReset = () => {
    if (intervalId) clearInterval(intervalId);
    setStats({ eventsProcessed: 0, handlersRegistered: 5, queueSize: 0, avgProcessingTime: 0, highPriorityEvents: 0, lowPriorityEvents: 0 });
    setStatus({ running: false, status: 'idle', message: 'Demo reset. Click Start to begin.' });
  };

  useEffect(() => () => { if (intervalId) clearInterval(intervalId); }, [intervalId]);

  const metrics: DemoMetric[] = [
    { label: 'Events Processed', value: stats.eventsProcessed, color: '#4caf50' },
    { label: 'Queue Size', value: stats.queueSize, color: '#ff9800' },
    { label: 'Handlers Registered', value: stats.handlersRegistered, color: '#2196f3' },
    { label: 'Avg Processing Time', value: stats.avgProcessingTime.toFixed(2), unit: 'ms', color: '#9c27b0' }
  ];

  const codeExample = `// EventHandlerRegistryEnhanced Usage
import { EventHandlerRegistryEnhanced } from '@oa/modules';

const registry = new EventHandlerRegistryEnhanced();

// Register high-priority handler
registry.register('user-action', async (event) => {
  await processUserAction(event);
}, { priority: 'high' });

// Emit event
await registry.emit('user-action', { userId: 123 });`;

  return (
    <DemoFramework
      title="EventHandlerRegistryEnhanced"
      description="Advanced event handling with priority queuing and metrics"
      status={status}
      metrics={metrics}
      onStart={handleStart}
      onStop={handleStop}
      onReset={handleReset}
      codeExample={codeExample}
    >
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: { xs: '1fr', md: 'repeat(2, 1fr)' },
          gap: 3
        }}
      >
        <Box>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>Event Priority Distribution</Typography>
              <Box
                sx={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(2, 1fr)',
                  gap: 2
                }}
              >
                <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                  <Chip label="High Priority" color="error" size="small" sx={{ mb: 1 }} />
                  <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#f44336' }}>
                    {stats.highPriorityEvents}
                  </Typography>
                </Paper>
                <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                  <Chip label="Low Priority" color="default" size="small" sx={{ mb: 1 }} />
                  <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#9e9e9e' }}>
                    {stats.lowPriorityEvents}
                  </Typography>
                </Paper>
              </Box>
            </CardContent>
          </Card>
        </Box>
        <Box>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>Handler Status</Typography>
              <Paper variant="outlined" sx={{ p: 2 }}>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>✅ Priority queuing enabled</Typography>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>✅ Error recovery active</Typography>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>✅ Performance: {stats.avgProcessingTime.toFixed(2)}ms avg</Typography>
              </Paper>
            </CardContent>
          </Card>
        </Box>
      </Box>
    </DemoFramework>
  );
}

