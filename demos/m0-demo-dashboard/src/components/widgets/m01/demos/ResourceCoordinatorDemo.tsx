/**
 * ============================================================================
 * ResourceCoordinatorEnhanced Demo
 * ============================================================================
 * 
 * Purpose: Interactive demonstration of ResourceCoordinatorEnhanced
 * Features: Centralized resource coordination, monitoring, optimization
 * 
 * Author: AI Assistant (Phase 3 Implementation)
 * Created: 2025-12-31
 * ============================================================================
 */

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Card, CardContent, Typography, Paper, Chip, Grid } from '@mui/material';
import DemoFramework, { DemoMetric, DemoStatus } from './DemoFramework';

interface CoordinatorStats {
  managedResources: number;
  activeCoordinations: number;
  optimizationEvents: number;
  resourceConflicts: number;
  avgCoordinationTime: number;
  systemEfficiency: number;
}

export default function ResourceCoordinatorDemo() {
  const [status, setStatus] = useState<DemoStatus>({
    running: false,
    status: 'idle',
    message: 'Click Start to begin resource coordination simulation'
  });

  const [stats, setStats] = useState<CoordinatorStats>({
    managedResources: 12,
    activeCoordinations: 0,
    optimizationEvents: 0,
    resourceConflicts: 0,
    avgCoordinationTime: 0,
    systemEfficiency: 100
  });

  const [intervalId, setIntervalId] = useState<NodeJS.Timeout | null>(null);

  const simulateCoordination = useCallback(() => {
    setStats(prev => {
      const newCoordinations = Math.max(0, Math.floor(Math.random() * 8));
      const conflict = Math.random() > 0.9 ? 1 : 0;
      const optimization = Math.random() > 0.7 ? 1 : 0;
      const efficiency = Math.max(85, 100 - (prev.resourceConflicts * 2));

      return {
        ...prev,
        activeCoordinations: newCoordinations,
        optimizationEvents: prev.optimizationEvents + optimization,
        resourceConflicts: prev.resourceConflicts + conflict,
        avgCoordinationTime: 1.5 + Math.random() * 2.5,
        systemEfficiency: efficiency
      };
    });
  }, []);

  const handleStart = () => {
    setStatus({ running: true, status: 'running', message: 'Coordinating resources with monitoring...' });
    const id = setInterval(simulateCoordination, 600);
    setIntervalId(id);
  };

  const handleStop = () => {
    if (intervalId) clearInterval(intervalId);
    setStatus({ running: false, status: 'stopped', message: 'Resource coordination stopped' });
  };

  const handleReset = () => {
    if (intervalId) clearInterval(intervalId);
    setStats({ managedResources: 12, activeCoordinations: 0, optimizationEvents: 0, resourceConflicts: 0, avgCoordinationTime: 0, systemEfficiency: 100 });
    setStatus({ running: false, status: 'idle', message: 'Demo reset. Click Start to begin.' });
  };

  useEffect(() => () => { if (intervalId) clearInterval(intervalId); }, [intervalId]);

  const metrics: DemoMetric[] = [
    { label: 'Managed Resources', value: stats.managedResources, color: '#4caf50' },
    { label: 'Active Coordinations', value: stats.activeCoordinations, color: '#2196f3' },
    { label: 'System Efficiency', value: `${stats.systemEfficiency.toFixed(0)}%`, color: '#ff9800' },
    { label: 'Avg Coordination Time', value: stats.avgCoordinationTime.toFixed(2), unit: 'ms', color: '#9c27b0' }
  ];

  const codeExample = `// ResourceCoordinatorEnhanced Usage
import { ResourceCoordinatorEnhanced } from '@oa/modules';

const coordinator = new ResourceCoordinatorEnhanced({
  monitoring: true,
  optimization: true
});

// Register resources
coordinator.register('database', dbResource);
coordinator.register('cache', cacheResource);

// Coordinate resource access
await coordinator.coordinate('database', async (db) => {
  await db.query('SELECT * FROM users');
});`;

  return (
    <DemoFramework
      title="ResourceCoordinatorEnhanced"
      description="Centralized resource coordination with monitoring"
      status={status}
      metrics={metrics}
      onStart={handleStart}
      onStop={handleStop}
      onReset={handleReset}
      codeExample={codeExample}
    >
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>Coordination Status</Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="caption" color="text.secondary">Optimization Events</Typography>
                    <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#4caf50' }}>
                      {stats.optimizationEvents}
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="caption" color="text.secondary">Resource Conflicts</Typography>
                    <Typography variant="h5" sx={{ fontWeight: 'bold', color: stats.resourceConflicts > 0 ? '#f44336' : '#4caf50' }}>
                      {stats.resourceConflicts}
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>System Health</Typography>
              <Box sx={{ mb: 2 }}>
                <Chip 
                  label={stats.systemEfficiency >= 95 ? 'Excellent' : stats.systemEfficiency >= 90 ? 'Good' : 'Fair'} 
                  color={stats.systemEfficiency >= 95 ? 'success' : stats.systemEfficiency >= 90 ? 'primary' : 'warning'}
                  sx={{ mb: 2 }}
                />
              </Box>
              <Paper variant="outlined" sx={{ p: 2 }}>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>✅ Centralized coordination</Typography>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>✅ Conflict resolution active</Typography>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>✅ Performance: {stats.avgCoordinationTime.toFixed(2)}ms avg</Typography>
              </Paper>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </DemoFramework>
  );
}

