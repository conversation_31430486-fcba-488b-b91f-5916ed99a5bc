/**
 * ============================================================================
 * TimerCoordinationServiceEnhanced Demo
 * ============================================================================
 * 
 * Purpose: Interactive demonstration of TimerCoordinationServiceEnhanced
 * Features: Timer coordination, resilient execution, performance monitoring
 * 
 * Author: AI Assistant (Phase 3 Implementation)
 * Created: 2025-12-31
 * ============================================================================
 */

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, Typography, Paper, Box, Grid } from '@mui/material';
import DemoFramework, { DemoMetric, DemoStatus } from './DemoFramework';

interface TimerStats {
  activeTimers: number;
  completedTimers: number;
  totalExecutions: number;
  avgExecutionTime: number;
  missedDeadlines: number;
  coordinatedTimers: number;
}

export default function TimerCoordinationServiceDemo() {
  const [status, setStatus] = useState<DemoStatus>({
    running: false,
    status: 'idle',
    message: 'Click Start to begin timer coordination simulation'
  });

  const [stats, setStats] = useState<TimerStats>({
    activeTimers: 0,
    completedTimers: 0,
    totalExecutions: 0,
    avgExecutionTime: 0,
    missedDeadlines: 0,
    coordinatedTimers: 3
  });

  const [intervalId, setIntervalId] = useState<NodeJS.Timeout | null>(null);

  const simulateTimerExecution = useCallback(() => {
    setStats(prev => ({
      ...prev,
      activeTimers: Math.min(prev.coordinatedTimers, prev.activeTimers + Math.floor(Math.random() * 2)),
      completedTimers: prev.completedTimers + Math.floor(Math.random() * 2),
      totalExecutions: prev.totalExecutions + 1,
      avgExecutionTime: 0.5 + Math.random() * 1.5,
      missedDeadlines: Math.random() > 0.95 ? prev.missedDeadlines + 1 : prev.missedDeadlines
    }));
  }, []);

  const handleStart = () => {
    setStatus({ running: true, status: 'running', message: 'Coordinating timers with resilient execution...' });
    const id = setInterval(simulateTimerExecution, 700);
    setIntervalId(id);
  };

  const handleStop = () => {
    if (intervalId) clearInterval(intervalId);
    setStatus({ running: false, status: 'stopped', message: 'Timer coordination stopped' });
  };

  const handleReset = () => {
    if (intervalId) clearInterval(intervalId);
    setStats({ activeTimers: 0, completedTimers: 0, totalExecutions: 0, avgExecutionTime: 0, missedDeadlines: 0, coordinatedTimers: 3 });
    setStatus({ running: false, status: 'idle', message: 'Demo reset. Click Start to begin.' });
  };

  useEffect(() => () => { if (intervalId) clearInterval(intervalId); }, [intervalId]);

  const metrics: DemoMetric[] = [
    { label: 'Active Timers', value: stats.activeTimers, color: '#4caf50' },
    { label: 'Completed Timers', value: stats.completedTimers, color: '#2196f3' },
    { label: 'Total Executions', value: stats.totalExecutions, color: '#ff9800' },
    { label: 'Avg Execution Time', value: stats.avgExecutionTime.toFixed(2), unit: 'ms', color: '#9c27b0' }
  ];

  const codeExample = `// TimerCoordinationServiceEnhanced Usage
import { TimerCoordinationServiceEnhanced } from '@oa/modules';

const service = new TimerCoordinationServiceEnhanced();

// Schedule coordinated timer
const timerId = service.schedule({
  interval: 1000,
  callback: async () => {
    await performTask();
  },
  resilient: true
});

// Timers are automatically coordinated
// Missed deadlines are tracked and recovered`;

  return (
    <DemoFramework
      title="TimerCoordinationServiceEnhanced"
      description="Coordinated timer management with resilient execution"
      status={status}
      metrics={metrics}
      onStart={handleStart}
      onStop={handleStop}
      onReset={handleReset}
      codeExample={codeExample}
    >
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>Timer Coordination</Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="caption" color="text.secondary">Coordinated Timers</Typography>
                    <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#2196f3' }}>
                      {stats.coordinatedTimers}
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="caption" color="text.secondary">Missed Deadlines</Typography>
                    <Typography variant="h5" sx={{ fontWeight: 'bold', color: stats.missedDeadlines > 0 ? '#f44336' : '#4caf50' }}>
                      {stats.missedDeadlines}
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>Performance Status</Typography>
              <Paper variant="outlined" sx={{ p: 2 }}>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>✅ Resilient execution enabled</Typography>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>✅ Deadline monitoring active</Typography>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>✅ Performance: {stats.avgExecutionTime.toFixed(2)}ms avg</Typography>
              </Paper>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </DemoFramework>
  );
}

