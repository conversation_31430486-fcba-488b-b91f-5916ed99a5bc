/**
 * ============================================================================
 * M0.1 Components Gallery Dashboard
 * ============================================================================
 * 
 * Purpose: Main dashboard for interactive component demonstrations
 * Features: Component selection, live demos, real-time metrics, code examples
 * 
 * Components:
 * 1. MemorySafeResourceManagerEnhanced
 * 2. EventHandlerRegistryEnhanced
 * 3. TimerCoordinationServiceEnhanced
 * 4. AtomicCircularBufferEnhanced
 * 5. MemoryPoolManagerEnhanced
 * 6. ResourceCoordinatorEnhanced
 * 
 * Author: AI Assistant (Phase 3 Implementation)
 * Created: 2025-12-31
 * ============================================================================
 */

'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Chip,
  Alert,
  Divider
} from '@mui/material';
import {
  Memory as MemoryIcon,
  Event as EventIcon,
  Timer as TimerIcon,
  Storage as StorageIcon,
  Pool as PoolIcon,
  Hub as HubIcon
} from '@mui/icons-material';
import MemorySafeResourceManagerDemo from '../widgets/m01/demos/MemorySafeResourceManagerDemo';
import EventHandlerRegistryDemo from '../widgets/m01/demos/EventHandlerRegistryDemo';
import TimerCoordinationServiceDemo from '../widgets/m01/demos/TimerCoordinationServiceDemo';
import AtomicCircularBufferDemo from '../widgets/m01/demos/AtomicCircularBufferDemo';
import MemoryPoolManagerDemo from '../widgets/m01/demos/MemoryPoolManagerDemo';
import ResourceCoordinatorDemo from '../widgets/m01/demos/ResourceCoordinatorDemo';

interface ComponentTab {
  id: string;
  name: string;
  shortName: string;
  icon: React.ReactElement;
  description: string;
  color: string;
}

const componentTabs: ComponentTab[] = [
  {
    id: 'memory-manager',
    name: 'MemorySafeResourceManagerEnhanced',
    shortName: 'Memory Manager',
    icon: <MemoryIcon />,
    description: 'Memory-safe resource management with resilient timing',
    color: '#4caf50'
  },
  {
    id: 'event-registry',
    name: 'EventHandlerRegistryEnhanced',
    shortName: 'Event Registry',
    icon: <EventIcon />,
    description: 'Advanced event handling with priority queuing',
    color: '#2196f3'
  },
  {
    id: 'timer-service',
    name: 'TimerCoordinationServiceEnhanced',
    shortName: 'Timer Service',
    icon: <TimerIcon />,
    description: 'Coordinated timer management with resilient execution',
    color: '#ff9800'
  },
  {
    id: 'circular-buffer',
    name: 'AtomicCircularBufferEnhanced',
    shortName: 'Circular Buffer',
    icon: <StorageIcon />,
    description: 'High-performance circular buffer with atomic operations',
    color: '#9c27b0'
  },
  {
    id: 'pool-manager',
    name: 'MemoryPoolManagerEnhanced',
    shortName: 'Pool Manager',
    icon: <PoolIcon />,
    description: 'Optimized memory pool management with analytics',
    color: '#f44336'
  },
  {
    id: 'resource-coordinator',
    name: 'ResourceCoordinatorEnhanced',
    shortName: 'Resource Coordinator',
    icon: <HubIcon />,
    description: 'Centralized resource coordination with monitoring',
    color: '#00bcd4'
  }
];

export default function ComponentsGallery() {
  const [selectedTab, setSelectedTab] = useState(0);
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue);
  };

  if (!mounted) {
    return null;
  }

  const currentComponent = componentTabs[selectedTab];

  return (
    <Box>
      {/* Page Header */}
      <Paper 
        elevation={0} 
        sx={{ 
          p: 3, 
          mb: 3, 
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', 
          color: 'white' 
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>
          <Box>
            <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold', mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
              🔧 Enhanced Components Gallery
            </Typography>
            <Typography variant="body1" sx={{ opacity: 0.95 }}>
              Interactive demonstrations of 6 production-ready enhanced components
            </Typography>
          </Box>
          <Box sx={{ textAlign: 'right' }}>
            <Chip 
              label="6 Components" 
              sx={{ 
                backgroundColor: 'rgba(255,255,255,0.2)', 
                color: 'white',
                fontWeight: 'bold',
                mb: 1
              }} 
            />
            <Typography variant="body2" sx={{ opacity: 0.9 }}>
              Live Interactive Demos
            </Typography>
          </Box>
        </Box>
      </Paper>

      {/* Component Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={selectedTab}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            '& .MuiTab-root': {
              minHeight: 72,
              textTransform: 'none'
            }
          }}
        >
          {componentTabs.map((component, index) => (
            <Tab
              key={component.id}
              icon={component.icon}
              iconPosition="start"
              label={
                <Box sx={{ textAlign: 'left', ml: 1 }}>
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    {component.shortName}
                  </Typography>
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                    {component.description.split(' ').slice(0, 4).join(' ')}...
                  </Typography>
                </Box>
              }
              sx={{
                borderBottom: selectedTab === index ? 3 : 0,
                borderColor: component.color,
                '&.Mui-selected': {
                  color: component.color
                }
              }}
            />
          ))}
        </Tabs>
      </Paper>

      {/* Current Component Info */}
      <Alert
        severity="info"
        icon={currentComponent.icon}
        sx={{ mb: 3 }}
      >
        <Typography variant="body2" sx={{ fontWeight: 600 }}>
          {currentComponent.name}
        </Typography>
        <Typography variant="caption">
          {currentComponent.description}
        </Typography>
      </Alert>

      <Divider sx={{ mb: 3 }} />

      {/* Component Demo Content */}
      <Box>
        {selectedTab === 0 && <MemorySafeResourceManagerDemo />}
        {selectedTab === 1 && <EventHandlerRegistryDemo />}
        {selectedTab === 2 && <TimerCoordinationServiceDemo />}
        {selectedTab === 3 && <AtomicCircularBufferDemo />}
        {selectedTab === 4 && <MemoryPoolManagerDemo />}
        {selectedTab === 5 && <ResourceCoordinatorDemo />}
      </Box>
    </Box>
  );
}

