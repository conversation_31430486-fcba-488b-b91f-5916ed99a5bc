/**
 * ============================================================================
 * M0.1 Enhanced Components Gallery Page
 * ============================================================================
 *
 * Purpose: Interactive showcase of 6 enhanced M0.1 components with live demos
 * Features: Component selection, interactive demos, real-time metrics, code examples
 *
 * Phase 3 Implementation (Days 5-7)
 * Author: AI Assistant
 * Created: 2025-12-31
 * ============================================================================
 */

import { Container, Box } from '@mui/material';
import ComponentsGallery from '../../components/dashboards/ComponentsGallery';

export default function M01ComponentsPage() {
  return (
    <Container maxWidth="xl">
      <Box sx={{ py: 3 }}>
        <ComponentsGallery />
      </Box>
    </Container>
  );
}

