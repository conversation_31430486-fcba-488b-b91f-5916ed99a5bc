/**
 * Main Dashboard Overview
 * Purpose: Central hub showing M0 milestone achievement and navigation to all dashboards
 * Features: High-level metrics, quick access to all dashboard sections
 */

import Link from 'next/link';

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            🏠 M0 Governance & Tracking Control Center
          </h1>
          <p className="text-xl text-gray-600">
            Comprehensive demo dashboard showcasing M0 milestone completion and capabilities
          </p>
        </div>

        {/* M0 Achievement Banner */}
        <div className="bg-gradient-to-r from-green-500 to-blue-600 rounded-lg shadow-lg p-8 mb-8 text-white">
          <div className="text-center">
            <h2 className="text-3xl font-bold mb-4">🎉 M0 MILESTONE ACHIEVED</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold">95+</div>
                <div className="text-sm opacity-90">Components Delivered</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">31,545+</div>
                <div className="text-sm opacity-90">Lines of Code</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">129%</div>
                <div className="text-sm opacity-90">Completion Rate</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">0</div>
                <div className="text-sm opacity-90">TypeScript Errors</div>
              </div>
            </div>
          </div>
        </div>

        {/* Dashboard Navigation Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <Link href="/security" className="group">
            <div className="bg-gradient-to-br from-slate-700 to-slate-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-all group-hover:from-slate-600 group-hover:to-slate-700">
              <div className="flex items-center mb-4">
                <div className="text-3xl mr-3">🛡️</div>
                <h3 className="text-xl font-semibold text-white">Security & Memory Safety</h3>
              </div>
              <p className="text-gray-300 mb-4">
                Real-time memory monitoring across 22+ tracking services with attack prevention
              </p>
              <div className="flex justify-between text-sm">
                <span className="text-blue-400 font-medium">Protected Services: 22+</span>
                <span className="text-green-400 font-medium">Memory Maps: 48+</span>
              </div>
            </div>
          </Link>

          <Link href="/governance" className="group">
            <div className="bg-gradient-to-br from-slate-700 to-slate-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-all group-hover:from-slate-600 group-hover:to-slate-700">
              <div className="flex items-center mb-4">
                <div className="text-3xl mr-3">📊</div>
                <h3 className="text-xl font-semibold text-white">Governance Control</h3>
              </div>
              <p className="text-gray-300 mb-4">
                Live governance rule validation across 61+ components with authority chain management
              </p>
              <div className="flex justify-between text-sm">
                <span className="text-blue-400 font-medium">Components: 61+</span>
                <span className="text-green-400 font-medium">Compliance: 122%</span>
              </div>
            </div>
          </Link>

          <Link href="/tracking" className="group">
            <div className="bg-gradient-to-br from-slate-700 to-slate-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-all group-hover:from-slate-600 group-hover:to-slate-700">
              <div className="flex items-center mb-4">
                <div className="text-3xl mr-3">📈</div>
                <h3 className="text-xl font-semibold text-white">Real-Time Tracking</h3>
              </div>
              <p className="text-gray-300 mb-4">
                Enhanced implementation monitoring across 95+ M0 components (137.5% completion)
              </p>
              <div className="flex justify-between text-sm">
                <span className="text-blue-400 font-medium">Components: 33+</span>
                <span className="text-green-400 font-medium">Enhanced: 137.5%</span>
              </div>
            </div>
          </Link>

          <Link href="/integration" className="group">
            <div className="bg-gradient-to-br from-slate-700 to-slate-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-all group-hover:from-slate-600 group-hover:to-slate-700">
              <div className="flex items-center mb-4">
                <div className="text-3xl mr-3">🔗</div>
                <h3 className="text-xl font-semibold text-white">Integration Testing</h3>
              </div>
              <p className="text-gray-300 mb-4">
                Component integration validation and foundation readiness for future milestones
              </p>
              <div className="flex justify-between text-sm">
                <span className="text-blue-400 font-medium">Integrations: 284</span>
                <span className="text-green-400 font-medium">Health: 100%</span>
              </div>
            </div>
          </Link>

          <Link href="/foundation" className="group">
            <div className="bg-gradient-to-br from-slate-700 to-slate-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-all group-hover:from-slate-600 group-hover:to-slate-700">
              <div className="flex items-center mb-4">
                <div className="text-3xl mr-3">🎯</div>
                <h3 className="text-xl font-semibold text-white">Foundation Overview</h3>
              </div>
              <p className="text-gray-300 mb-4">
                Complete M0 milestone achievement and architectural foundation for OA Framework
              </p>
              <div className="flex justify-between text-sm">
                <span className="text-blue-400 font-medium">Quality: Enterprise</span>
                <span className="text-green-400 font-medium">Ready: M1, M2+</span>
              </div>
            </div>
          </Link>

          {/* Demo Information Card */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <div className="text-3xl mr-3">ℹ️</div>
              <h3 className="text-xl font-semibold text-blue-800">Demo Information</h3>
            </div>
            <p className="text-blue-700 mb-4">
              This is a comprehensive integration testing dashboard showcasing M0 capabilities with mock data
            </p>
            <div className="text-sm text-blue-600">
              <div>• Integration Testing Tool</div>
              <div>• Stakeholder Validation Platform</div>
              <div>• Visual M0 Component Demonstration</div>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">M0 Component Breakdown</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">61+</div>
              <div className="text-gray-600">Governance Components</div>
              <div className="text-sm text-gray-500 mt-1">Rule validation, authority chains, compliance</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">33+</div>
              <div className="text-gray-600">Tracking Components</div>
              <div className="text-sm text-gray-500 mt-1">Real-time monitoring, session tracking, analytics</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">14+</div>
              <div className="text-gray-600">Memory Safety Components</div>
              <div className="text-sm text-gray-500 mt-1">Memory protection, attack prevention, boundaries</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
