'use client';

import React, { useState, useEffect } from 'react';
import <PERSON> from 'next/link';
import { usePathname } from 'next/navigation';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  Box,
  Container,
  Menu,
  MenuItem,
  Badge,
  Chip
} from '@mui/material';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import StarIcon from '@mui/icons-material/Star';

// M0 Base Navigation Items
const navigationItems = [
  { href: '/', label: 'Overview', icon: '🏠' },
  { href: '/security', label: 'Security', icon: '🛡️' },
  { href: '/governance', label: 'Governance', icon: '📊' },
  { href: '/tracking', label: 'Tracking', icon: '📈' },
  { href: '/integration', label: 'Integration', icon: '🔗' },
  { href: '/foundation', label: 'Foundation', icon: '🎯' },
];

// M0.1 Enhanced Navigation Items (Dropdown Menu)
const m01NavigationItems = [
  {
    href: '/m01-overview',
    label: 'M0.1 Overview',
    icon: '📊',
    isNew: true,
    isFeatured: true,
    description: '50 tasks, 100% complete'
  },
  {
    href: '/m01-components',
    label: 'Enhanced Components',
    icon: '🔧',
    isNew: true,
    isFeatured: true,
    description: '6 enhanced components'
  },
  {
    href: '/m01-metrics',
    label: 'Performance Metrics',
    icon: '📈',
    isNew: true,
    description: '<6ms average performance'
  },
  {
    href: '/m01-features',
    label: 'Enterprise Features',
    icon: '⭐',
    isNew: true,
    description: 'Advanced capabilities'
  },
  {
    href: '/m01-comparison',
    label: 'M0 vs M0.1 Comparison',
    icon: '⚖️',
    isNew: true,
    description: 'Side-by-side comparison'
  },
];

export default function Navigation() {
  const pathname = usePathname();
  const [mounted, setMounted] = useState(false);
  const [m01MenuAnchor, setM01MenuAnchor] = useState<null | HTMLElement>(null);
  const m01MenuOpen = Boolean(m01MenuAnchor);

  // Prevent hydration mismatch by only rendering after mount
  useEffect(() => {
    setMounted(true);
  }, []);

  // M0.1 Dropdown Menu Handlers
  const handleM01MenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setM01MenuAnchor(event.currentTarget);
  };

  const handleM01MenuClose = () => {
    setM01MenuAnchor(null);
  };

  // Check if current path is in M0.1 section
  const isM01Active = pathname?.startsWith('/m01-') || false;

  if (!mounted) {
    // Return a simple placeholder during SSR to prevent hydration mismatch
    return (
      <nav style={{ height: '64px', backgroundColor: '#fff', borderBottom: '1px solid #e0e0e0' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 16px', height: '64px', display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <h1 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#1f2937', margin: 0 }}>
            M0 Control Center
          </h1>
          <div style={{ display: 'flex', gap: '16px' }}>
            {navigationItems.map((item) => (
              <span key={item.href} style={{ padding: '8px 12px', color: '#6b7280' }}>
                {item.icon} {item.label}
              </span>
            ))}
            <span style={{ padding: '8px 12px', color: '#6b7280', fontWeight: '600' }}>
              ⭐ M0.1 Enhancements
            </span>
          </div>
          <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>
            <div style={{ fontWeight: '600' }}>M0 Status</div>
            <div style={{ fontSize: '0.75rem', color: '#059669' }}>129% Complete</div>
          </div>
        </div>
      </nav>
    );
  }

  return (
    <AppBar
      position="static"
      elevation={1}
      sx={{
        backgroundColor: 'white',
        color: 'text.primary',
        borderBottom: 1,
        borderColor: 'divider'
      }}
    >
      <Container maxWidth="xl">
        <Toolbar sx={{ justifyContent: 'space-between', minHeight: '64px !important' }}>
          <Typography
            variant="h6"
            component="h1"
            sx={{
              fontWeight: 'bold',
              color: 'text.primary'
            }}
          >
            M0 Control Center
          </Typography>

          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            {navigationItems.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Button
                  key={item.href}
                  component={Link}
                  href={item.href}
                  variant={isActive ? 'contained' : 'text'}
                  color={isActive ? 'primary' : 'inherit'}
                  startIcon={<span>{item.icon}</span>}
                  sx={{
                    minWidth: 'auto',
                    px: 2,
                    py: 1,
                    textTransform: 'none',
                    fontWeight: isActive ? 600 : 400,
                    color: isActive ? 'primary.contrastText' : 'text.primary',
                    '&:hover': {
                      backgroundColor: isActive ? 'primary.dark' : 'action.hover'
                    }
                  }}
                >
                  {item.label}
                </Button>
              );
            })}

            {/* M0.1 Enhancements Dropdown Button */}
            <Badge
              badgeContent="NEW"
              color="error"
              sx={{
                '& .MuiBadge-badge': {
                  fontSize: '0.625rem',
                  height: '16px',
                  minWidth: '16px',
                  padding: '0 4px',
                  fontWeight: 700
                }
              }}
            >
              <Button
                variant={isM01Active ? 'contained' : 'text'}
                color={isM01Active ? 'primary' : 'inherit'}
                startIcon={<StarIcon sx={{ color: isM01Active ? 'inherit' : '#FFD700' }} />}
                endIcon={<ArrowDropDownIcon />}
                onClick={handleM01MenuOpen}
                aria-controls={m01MenuOpen ? 'm01-menu' : undefined}
                aria-haspopup="true"
                aria-expanded={m01MenuOpen ? 'true' : undefined}
                sx={{
                  minWidth: 'auto',
                  px: 2,
                  py: 1,
                  textTransform: 'none',
                  fontWeight: isM01Active ? 600 : 500,
                  color: isM01Active ? 'primary.contrastText' : 'text.primary',
                  '&:hover': {
                    backgroundColor: isM01Active ? 'primary.dark' : 'action.hover'
                  }
                }}
              >
                M0.1 Enhancements
              </Button>
            </Badge>

            {/* M0.1 Dropdown Menu */}
            <Menu
              id="m01-menu"
              anchorEl={m01MenuAnchor}
              open={m01MenuOpen}
              onClose={handleM01MenuClose}
              MenuListProps={{
                'aria-labelledby': 'm01-button',
              }}
              sx={{
                '& .MuiPaper-root': {
                  minWidth: '280px',
                  mt: 1,
                  boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                }
              }}
            >
              {m01NavigationItems.map((item) => {
                const isActive = pathname === item.href;
                return (
                  <MenuItem
                    key={item.href}
                    component={Link}
                    href={item.href}
                    onClick={handleM01MenuClose}
                    selected={isActive}
                    sx={{
                      py: 1.5,
                      px: 2,
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'flex-start',
                      gap: 0.5,
                      '&:hover': {
                        backgroundColor: 'action.hover'
                      },
                      '&.Mui-selected': {
                        backgroundColor: 'primary.light',
                        '&:hover': {
                          backgroundColor: 'primary.light'
                        }
                      }
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                      <span style={{ fontSize: '1.2rem' }}>{item.icon}</span>
                      <Typography variant="body2" sx={{ fontWeight: isActive ? 600 : 500, flex: 1 }}>
                        {item.label}
                      </Typography>
                      {item.isNew && (
                        <Chip
                          label="NEW"
                          size="small"
                          color="error"
                          sx={{
                            height: '18px',
                            fontSize: '0.625rem',
                            fontWeight: 700
                          }}
                        />
                      )}
                      {item.isFeatured && (
                        <StarIcon sx={{ fontSize: '1rem', color: '#FFD700' }} />
                      )}
                    </Box>
                    {item.description && (
                      <Typography variant="caption" sx={{ color: 'text.secondary', pl: 4 }}>
                        {item.description}
                      </Typography>
                    )}
                  </MenuItem>
                );
              })}

              {/* Menu Footer with Completion Status */}
              <Box
                sx={{
                  px: 2,
                  py: 1.5,
                  borderTop: 1,
                  borderColor: 'divider',
                  backgroundColor: 'action.hover'
                }}
              >
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.secondary' }}>
                  M0.1 Milestone Status
                </Typography>
                <Typography variant="caption" sx={{ display: 'block', color: 'success.main', fontWeight: 600 }}>
                  ✅ 100% Complete (50/50 tasks)
                </Typography>
              </Box>
            </Menu>
          </Box>

          <Box sx={{ textAlign: 'right' }}>
            <Typography variant="body2" sx={{ fontWeight: 600, color: 'text.primary' }}>
              M0 Status
            </Typography>
            <Typography variant="caption" sx={{ color: 'success.main' }}>
              129% Complete
            </Typography>
          </Box>
        </Toolbar>
      </Container>
    </AppBar>
  );
}
