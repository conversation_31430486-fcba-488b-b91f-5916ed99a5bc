# M0 Demo Dashboard - Theme Quick Reference

## 🎨 Quick Start

### Import Theme Hook
```typescript
import { useTheme } from '@mui/material/styles';
import { useThemeMode } from '@/theme/ThemeProvider';
```

### Use Theme in Component
```typescript
function MyComponent() {
  const theme = useTheme();
  
  return (
    <Box sx={{ backgroundColor: theme.palette.background.default }}>
      <Typography color="text.primary">Hello World</Typography>
    </Box>
  );
}
```

### Switch Theme Manually
```typescript
function MyComponent() {
  const { themeMode, setThemeMode } = useThemeMode();
  
  return (
    <Button onClick={() => setThemeMode('secondary')}>
      Switch to Secondary Theme
    </Button>
  );
}
```

---

## 🎯 Theme Routes

| Route Pattern | Theme | Palette |
|---------------|-------|---------|
| `/` | Primary | Dark-themed Dashboards |
| `/components` | Primary | Dark-themed Dashboards |
| `/overview` | Primary | Dark-themed Dashboards |
| `/security` | Secondary | Crypto Trading |
| `/governance` | Secondary | Crypto Trading |
| `/tracking` | Secondary | Crypto Trading |
| `/integration` | Secondary | Crypto Trading |
| `/foundation` | Secondary | Crypto Trading |

---

## 🎨 Color Reference

### Primary Theme Colors
```typescript
theme.palette.primary.main      // #565c61 (Medium Gray-Blue)
theme.palette.primary.light     // #7b888a (Light Gray-Blue)
theme.palette.primary.dark      // #393e43 (Dark Gray-Blue)
theme.palette.background.default // #2d3038 (Dark Background)
theme.palette.background.paper  // #393e43 (Card Background)
theme.palette.text.primary      // #ffffff (White)
theme.palette.text.secondary    // #b0b8ba (Light Gray)
```

### Secondary Theme Colors
```typescript
theme.palette.primary.main      // #564b5d (Medium Purple-Gray)
theme.palette.primary.light     // #997180 (Mauve-Purple)
theme.palette.primary.dark      // #332e42 (Dark Purple-Gray)
theme.palette.background.default // #1c192c (Dark Purple-Blue)
theme.palette.background.paper  // #332e42 (Card Background)
theme.palette.text.primary      // #ffffff (White)
theme.palette.text.secondary    // #b0a8b5 (Light Purple-Gray)
```

---

## 📦 Common Patterns

### Card with Theme Colors
```typescript
<Card sx={{ 
  backgroundColor: theme.palette.background.paper,
  color: theme.palette.text.primary 
}}>
  <CardContent>
    <Typography variant="h6" color="text.primary">
      Title
    </Typography>
    <Typography variant="body2" color="text.secondary">
      Description
    </Typography>
  </CardContent>
</Card>
```

### Button with Theme Colors
```typescript
<Button 
  variant="contained" 
  color="primary"
  sx={{ 
    backgroundColor: theme.palette.primary.main,
    '&:hover': {
      backgroundColor: theme.palette.primary.dark
    }
  }}
>
  Click Me
</Button>
```

### Chip with Theme Colors
```typescript
<Chip 
  label="Status" 
  color="primary"
  sx={{ 
    backgroundColor: theme.palette.primary.light,
    color: theme.palette.text.primary
  }}
/>
```

---

## 🔧 Domain-Specific Colors

```typescript
import { M0_COLORS } from '@/theme/theme';

M0_COLORS.governance    // Governance-related components
M0_COLORS.tracking      // Tracking-related components
M0_COLORS.security      // Security-related components
M0_COLORS.integration   // Integration-related components
M0_COLORS.foundation    // Foundation-related components
```

---

## 🎭 Theme Context API

### Get Current Theme Mode
```typescript
const { themeMode } = useThemeMode();
// Returns: 'primary' | 'secondary'
```

### Set Theme Mode
```typescript
const { setThemeMode } = useThemeMode();
setThemeMode('primary');   // Switch to Primary theme
setThemeMode('secondary'); // Switch to Secondary theme
```

### Get Current Theme Object
```typescript
const { currentTheme } = useThemeMode();
// Returns: Theme object
```

---

## 📱 Responsive Styling with Theme

```typescript
<Box sx={{
  backgroundColor: theme.palette.background.default,
  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(2),
  },
  [theme.breakpoints.up('md')]: {
    padding: theme.spacing(4),
  },
}}>
  Content
</Box>
```

---

## 🎨 Custom Component Styling

### Using sx Prop
```typescript
<Box sx={{
  bgcolor: 'background.default',
  color: 'text.primary',
  p: 2,
  borderRadius: 2,
  border: 1,
  borderColor: 'primary.main',
}}>
  Content
</Box>
```

### Using styled Components
```typescript
import { styled } from '@mui/material/styles';

const StyledCard = styled(Card)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
  color: theme.palette.text.primary,
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
}));
```

---

## ⚡ Performance Tips

1. **Use Theme Hook Once**: Call `useTheme()` once at component top
2. **Memoize Theme-Dependent Values**: Use `useMemo` for computed values
3. **Avoid Inline Functions**: Define handlers outside render
4. **Use Theme Tokens**: Prefer `theme.palette.primary.main` over hardcoded colors

---

## 🐛 Troubleshooting

### Theme Not Updating
```typescript
// ❌ Wrong
const theme = createPrimaryTheme();

// ✅ Correct
const theme = useTheme();
```

### Theme Context Not Available
```typescript
// Ensure component is wrapped in ThemeProvider
// Check that you're using 'use client' directive in Next.js
```

### Colors Not Matching
```typescript
// Verify you're using theme tokens, not hardcoded colors
// ❌ Wrong
<Box sx={{ backgroundColor: '#565c61' }}>

// ✅ Correct
<Box sx={{ backgroundColor: 'primary.main' }}>
```

---

## 📚 Additional Resources

- **Full Documentation**: See `THEME-SYSTEM.md`
- **Color Comparison**: See `THEME-COLOR-COMPARISON.md`
- **Implementation Summary**: See `THEME-IMPLEMENTATION-SUMMARY.md`
- **Octet Design Palettes**: 
  - [Dark-themed](https://octet.design/colors/palette/dark-themed-color-palette-**********/)
  - [Crypto Trading](https://octet.design/colors/palette/crypto-trading-color-palette-**********/)

---

**Last Updated**: 2025-12-31  
**Version**: 1.0.0

