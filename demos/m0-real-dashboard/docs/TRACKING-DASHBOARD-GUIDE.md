# 📘 TRACKING DASHBOARD - USER GUIDE

**Authority**: President & CEO, E<PERSON><PERSON>. Consultancy  
**Version**: 1.0.0  
**Last Updated**: 2025-10-22  

---

## 📋 TABLE OF CONTENTS

1. [Overview](#overview)
2. [Getting Started](#getting-started)
3. [Dashboard Components](#dashboard-components)
4. [Operations Guide](#operations-guide)
5. [Alert Management](#alert-management)
6. [Troubleshooting](#troubleshooting)
7. [FAQ](#faq)

---

## 🎯 OVERVIEW

The **Tracking Dashboard** provides comprehensive real-time monitoring and analytics for tracking components in the M0 Real Dashboard system. It offers visibility into session analytics, component health, event timelines, and operational capabilities.

### **Key Features**

- **Real-time Monitoring** - 30-second auto-refresh with manual refresh option
- **Session Analytics** - Track active sessions, events, and processing rates
- **Component Health** - Monitor health scores and status of tracking components
- **Event Timeline** - View chronological event history
- **Operations** - Execute tracking operations (session analysis, health checks, event timeline)
- **Alert Management** - Auto-generated alerts with severity-based notifications
- **Responsive Design** - Works on mobile, tablet, and desktop devices

### **Access**

Navigate to `/tracking-dashboard` to access the Tracking Dashboard.

---

## 🚀 GETTING STARTED

### **Dashboard Layout**

The dashboard is organized into two main columns:

**Left Column (Main Content)**
- Tracking Overview Panel
- Session Analytics Chart
- Component Health Monitor
- Event Timeline Chart
- Component Status Grid

**Right Column (Operations & Alerts)**
- Tracking Operations Panel
- Operation Results Display
- Alert History Panel

### **Auto-Refresh**

The dashboard automatically refreshes every 30 seconds to display the latest data. You can also manually refresh using the **Refresh** button in the header.

### **Alert Notifications**

Unacknowledged alerts appear at the top of the dashboard. Click the bell icon in the header to toggle alert visibility. The badge shows the count of unacknowledged alerts.

---

## 📊 DASHBOARD COMPONENTS

### **1. Tracking Overview Panel**

**Purpose**: Displays high-level metrics and health breakdown.

**Metrics Displayed**:
- **Total Components**: Total number of tracking components (33)
- **Healthy Components**: Components with status "healthy"
- **Error Components**: Components with status "error"
- **Health Percentage**: Overall health percentage with progress bar

**Additional Metrics**:
- **Active Sessions**: Current number of active sessions
- **Total Events**: Total events processed
- **Avg Response Time**: Average response time in milliseconds
- **Processing Rate**: Data processing rate (events/second)
- **Last Activity**: Timestamp of last activity

**Color Coding**:
- 🟢 Green: Healthy (≥80% health)
- 🟡 Yellow: Warning (60-79% health)
- 🔴 Red: Error (<60% health)

---

### **2. Session Analytics Chart**

**Purpose**: Visualizes session trends over the last 60 minutes.

**Chart Features**:
- **Time Range**: Last 60 minutes (12 data points, 5-minute intervals)
- **Data Series**:
  - 🔵 Blue Line: Active Sessions
  - 🟢 Green Line: Events (per 5min)
  - 🟡 Yellow Line: Processing Rate

**Metrics Summary**:
- Active Sessions count
- Total Events count (formatted with commas)
- Processing Rate (events/second)

**Interaction**:
- Hover over data points to see exact values
- Legend shows/hides data series

---

### **3. Component Health Monitor**

**Purpose**: Displays top 10 tracking components sorted by health score.

**Information Displayed**:
- Component name and ID
- Health score (percentage with progress bar)
- Tracking type badge (session, analytics, orchestration, etc.)
- Response time in milliseconds
- Component metrics (operations, errors, success rate)

**Summary Statistics**:
- Average health across all components
- Count of healthy components
- Count of components with issues

**Color Coding**:
- 🟢 Green: Health ≥80%
- 🟡 Yellow: Health 60-79%
- 🔴 Red: Health <60%

---

### **4. Event Timeline Chart**

**Purpose**: Shows chronological timeline of tracking events.

**Features**:
- Scrollable event list (most recent first)
- Event type icons (error, warning, info, activity)
- Severity-based color coding
- Component information (ID and name)
- Relative timestamps (e.g., "2 minutes ago")

**Filtering**:
- Filter by severity (All, Critical, Warning, Info)
- Filter buttons at the top of the timeline

**Event Types**:
- 🔴 Error: Critical errors
- 🟡 Warning: Warning events
- 🔵 Info: Informational events
- 🟢 Activity: Normal activity

---

### **5. Component Status Grid**

**Purpose**: Displays all tracking components in a filterable grid.

**Grid Features**:
- Responsive grid layout (1-3 columns based on screen size)
- Component cards with health scores
- Status indicators (healthy, warning, error)
- Tracking type badges

**Filtering Options**:
- **By Type**: All, Session, Analytics, Orchestration, Progress, Data Management
- **By Status**: All, Healthy, Warning, Error

**Component Card Information**:
- Component name and ID
- Health score with progress bar
- Status indicator
- Tracking type badge
- Last update timestamp

---

## ⚙️ OPERATIONS GUIDE

### **Available Operations**

The Tracking Operations Panel provides three operations:

#### **1. Session Analysis**

**Purpose**: Analyze active sessions and session metrics.

**How to Use**:
1. Click **Run Analysis** button
2. Wait for operation to complete (typically 1-2 seconds)
3. View results in Operation Results Display

**Results Include**:
- Active session count
- Session metrics
- Analysis timestamp
- Operation duration

---

#### **2. Component Health Check**

**Purpose**: Check health status of all tracking components.

**How to Use**:
1. Click **Run Check** button
2. Wait for operation to complete
3. View health check results

**Results Include**:
- Component health scores
- Status summary
- Health check timestamp
- Operation duration

---

#### **3. Event Timeline**

**Purpose**: Generate timeline of recent tracking events.

**How to Use**:
1. Click **Generate Timeline** button
2. Wait for operation to complete
3. View event timeline results

**Results Include**:
- Event list
- Event timestamps
- Event types and severities
- Operation duration

---

### **Operation Status Indicators**

- **Idle**: No operation running (gray)
- **Running**: Operation in progress (blue, animated spinner)
- **Success**: Operation completed successfully (green checkmark)
- **Error**: Operation failed (red X)

### **Operation Results Display**

**Features**:
- Success/error status indicator
- Operation name and timestamp
- Duration in milliseconds
- Collapsible JSON data viewer
- Copy-to-clipboard functionality (future enhancement)

---

## 🔔 ALERT MANAGEMENT

### **Alert Types**

Alerts are automatically generated based on tracking metrics:

1. **Session Alerts**
   - High session count (>50 warning, >100 critical)
   - Low session count (<10 warning)

2. **Health Alerts**
   - Low health percentage (<80% warning, <60% critical)
   - Error components detected (>2 warning, >5 critical)

3. **Event Alerts**
   - High event rate
   - Event processing delays

4. **General Alerts**
   - System-wide issues
   - Configuration problems

### **Alert Severity Levels**

- 🔴 **Critical**: Immediate attention required
- 🟡 **Warning**: Attention recommended
- 🔵 **Info**: Informational only

### **Alert Notifications**

**Toast-Style Notifications**:
- Appear at the top of the dashboard
- Show up to 3 unacknowledged alerts
- Auto-dismiss after acknowledgment
- Click **Acknowledge** to mark as read
- Click **Dismiss** to remove from view

### **Alert History Panel**

**Features**:
- Scrollable alert history
- Filter by severity (All, Critical, Warning, Info)
- Filter by type (All, Session, Health, Event, General)
- Show/hide acknowledged alerts toggle
- Clear individual alerts
- Clear all alerts button

**Alert Information**:
- Severity and type badges
- Alert message
- Component ID and name (if applicable)
- Timestamp (relative time)
- Acknowledged status

---

## 🔧 TROUBLESHOOTING

### **Common Issues**

#### **Dashboard Not Loading**

**Symptoms**: Blank screen or loading spinner indefinitely

**Solutions**:
1. Check network connection
2. Verify API endpoint is accessible (`/api/m0-tracking`)
3. Check browser console for errors
4. Try manual refresh

---

#### **Data Not Updating**

**Symptoms**: Stale data, auto-refresh not working

**Solutions**:
1. Click manual **Refresh** button
2. Check auto-refresh is enabled (default: 30 seconds)
3. Verify API is responding
4. Check browser console for errors

---

#### **Charts Not Rendering**

**Symptoms**: Empty chart areas or missing visualizations

**Solutions**:
1. Ensure data is loaded (check for error messages)
2. Try resizing browser window
3. Clear browser cache
4. Check for JavaScript errors in console

---

#### **Operations Failing**

**Symptoms**: Operations return error status

**Solutions**:
1. Check API endpoint is accessible
2. Verify operation parameters are correct
3. Check server logs for errors
4. Try operation again after a few seconds

---

#### **Alerts Not Appearing**

**Symptoms**: No alerts shown despite issues

**Solutions**:
1. Check alert toggle is enabled (bell icon)
2. Verify alert thresholds are configured
3. Check if alerts are acknowledged
4. Review alert history panel

---

## ❓ FAQ

### **General Questions**

**Q: How often does the dashboard refresh?**  
A: The dashboard auto-refreshes every 30 seconds. You can also manually refresh using the Refresh button.

**Q: Can I change the refresh interval?**  
A: Currently, the refresh interval is fixed at 30 seconds. Future versions may allow customization.

**Q: How many components are tracked?**  
A: The system tracks 33 tracking components across 5 categories (session, analytics, orchestration, progress, data-management).

---

### **Component Questions**

**Q: What does the health score represent?**  
A: Health score is a percentage (0-100%) indicating component health based on error rates, response times, and operational metrics.

**Q: Why are some components showing "warning" status?**  
A: Warning status indicates health score between 60-79% or elevated error rates. Review component details for specifics.

**Q: How is the "Top 10" in Component Health Monitor determined?**  
A: Components are sorted by health score (highest first), and the top 10 are displayed.

---

### **Operations Questions**

**Q: How long do operations take to complete?**  
A: Most operations complete in 1-3 seconds. Complex operations may take up to 5 seconds.

**Q: Can I run multiple operations simultaneously?**  
A: No, only one operation can run at a time. Wait for the current operation to complete before starting another.

**Q: What happens if an operation fails?**  
A: The operation status will show "error" and the Operation Results Display will show error details.

---

### **Alert Questions**

**Q: How are alerts generated?**  
A: Alerts are automatically generated based on metric thresholds (e.g., high session count, low health percentage).

**Q: What's the difference between "Acknowledge" and "Dismiss"?**  
A: "Acknowledge" marks the alert as read but keeps it in history. "Dismiss" removes it from view entirely.

**Q: Can I customize alert thresholds?**  
A: Currently, thresholds are predefined. Future versions may allow customization.

---

### **Technical Questions**

**Q: What browsers are supported?**  
A: Modern browsers (Chrome, Firefox, Safari, Edge) with JavaScript enabled.

**Q: Is the dashboard mobile-friendly?**  
A: Yes, the dashboard is fully responsive and works on mobile, tablet, and desktop devices.

**Q: Can I export data from the dashboard?**  
A: Export functionality is planned for future versions. Currently, you can view and copy JSON data from operation results.

---

## 📞 SUPPORT

For additional support or to report issues:

- **Documentation**: Review this guide and `TRACKING-DASHBOARD-COMPLETE.md`
- **Technical Issues**: Check browser console for errors
- **Feature Requests**: Submit via project issue tracker

---

**Authority**: President & CEO, E.Z. Consultancy  
**Version**: 1.0.0  
**Last Updated**: 2025-10-22  
**Status**: Production Ready ✅

