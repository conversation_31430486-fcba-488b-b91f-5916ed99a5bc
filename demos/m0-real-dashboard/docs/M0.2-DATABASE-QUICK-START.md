# M0.2 Query Optimization - Real Database Quick Start

**Quick guide to using the Query Optimization Dashboard with real PostgreSQL database**

---

## 🚀 **QUICK START**

### **1. Verify Database Connection**
```bash
cd demos/m0-real-dashboard
node scripts/test-db-connection.js
```

**Expected Output**:
```
✅ Connection successful!
✅ Database Version: PostgreSQL 17.5
✅ All tests passed!
```

### **2. Start Development Server**
```bash
npm run dev
```

### **3. Open Dashboard**
```
http://localhost:3000/m02-query-optimization
```

---

## 📊 **TESTING WITH REAL QUERIES**

### **Sample Queries to Try**

#### **1. Simple SELECT**
```sql
SELECT * FROM pg_tables WHERE schemaname = 'public';
```
**Expected**: Low complexity, sequential scan

#### **2. System Catalog Query**
```sql
SELECT 
  schemaname, 
  tablename, 
  tableowner 
FROM pg_tables 
WHERE schemaname NOT IN ('pg_catalog', 'information_schema')
ORDER BY tablename;
```
**Expected**: Medium complexity, possible index usage

#### **3. Database Statistics**
```sql
SELECT 
  datname, 
  numbackends, 
  xact_commit, 
  xact_rollback 
FROM pg_stat_database 
WHERE datname = current_database();
```
**Expected**: Low complexity, system catalog access

#### **4. Active Connections**
```sql
SELECT 
  pid, 
  usename, 
  application_name, 
  state, 
  query 
FROM pg_stat_activity 
WHERE state = 'active';
```
**Expected**: Sequential scan, real-time data

---

## 🔧 **CONFIGURATION**

### **Environment Variables** (`.env.local`)

```env
# Database Connection
DATABASE_URL=postgresql://postgres:paas123@localhost:5432/postgres
POSTGRES_USER=postgres
POSTGRES_PASSWORD=paas123
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=postgres

# Enable Real Database
NEXT_PUBLIC_ENABLE_REAL_DB=true
```

### **Toggle Between Real and Mock Data**

**Use Real Database**:
```env
NEXT_PUBLIC_ENABLE_REAL_DB=true
```

**Use Mock Data**:
```env
NEXT_PUBLIC_ENABLE_REAL_DB=false
```

---

## 📡 **API ENDPOINTS**

### **1. Query Optimization**
```bash
curl -X POST http://localhost:3000/api/m02/query-optimization \
  -H "Content-Type: application/json" \
  -d '{"query": "SELECT * FROM pg_tables LIMIT 10"}'
```

### **2. Database Status**
```bash
curl http://localhost:3000/api/m02/db-status
```

---

## 🎯 **WHAT YOU'LL SEE**

### **Real Execution Plans**
- Actual PostgreSQL optimizer decisions
- Real cost estimates from database
- True row count estimates
- Actual node types (Seq Scan, Index Scan, etc.)

### **Real Performance Metrics**
- Execution time in milliseconds
- Planning time
- Total query cost
- Estimated rows

### **Real Recommendations**
- Based on actual execution plan analysis
- Detects sequential scans
- Suggests indexes for optimization
- Identifies high-cost operations

---

## 🐛 **TROUBLESHOOTING**

### **Connection Failed**
```
❌ Database connection failed
```

**Solutions**:
1. Check PostgreSQL is running: `sudo systemctl status postgresql`
2. Verify credentials in `.env.local`
3. Test connection: `node scripts/test-db-connection.js`
4. Check firewall settings

### **Query Timeout**
```
❌ Query optimization failed: timeout
```

**Solutions**:
1. Increase timeout in `.env.local`: `QUERY_TIMEOUT=60000`
2. Simplify the query
3. Add WHERE clause to limit results

### **Fallback to Mock Data**
```
⚠️ Using mock data (database unavailable)
```

**This is normal**: The system automatically falls back to mock data if the database is unavailable.

---

## 📚 **USEFUL POSTGRESQL QUERIES**

### **List All Tables**
```sql
SELECT tablename FROM pg_tables WHERE schemaname = 'public';
```

### **Table Size**
```sql
SELECT 
  schemaname,
  tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) AS size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

### **Index Information**
```sql
SELECT 
  schemaname,
  tablename,
  indexname,
  indexdef
FROM pg_indexes
WHERE schemaname = 'public';
```

---

## ✅ **VERIFICATION CHECKLIST**

- [ ] Database connection test passes
- [ ] Development server starts without errors
- [ ] Dashboard loads at `/m02-query-optimization`
- [ ] Sample queries execute successfully
- [ ] Execution plans display correctly
- [ ] Recommendations appear based on query
- [ ] Performance metrics show real data

---

## 🎓 **LEARNING RESOURCES**

### **PostgreSQL EXPLAIN**
- [PostgreSQL EXPLAIN Documentation](https://www.postgresql.org/docs/17/sql-explain.html)
- [Understanding EXPLAIN Output](https://www.postgresql.org/docs/17/using-explain.html)

### **Query Optimization**
- [PostgreSQL Performance Tips](https://www.postgresql.org/docs/17/performance-tips.html)
- [Index Types](https://www.postgresql.org/docs/17/indexes-types.html)

---

**Ready to optimize queries!** 🚀

Try the sample queries above and see real PostgreSQL execution plans in action!

