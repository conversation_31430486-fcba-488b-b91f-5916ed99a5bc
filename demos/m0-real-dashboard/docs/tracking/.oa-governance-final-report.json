{"timestamp": "2025-10-22T13:22:23.011Z", "summary": {"totalEvents": 0, "totalViolations": 0, "resolvedViolations": 0, "finalComplianceScore": 100, "governanceHealth": "excellent"}, "metrics": {"totalEvents": 0, "totalViolations": 0, "resolvedViolations": 0, "averageResolutionTime": 0, "complianceEfficiency": 100, "governanceHealth": "excellent", "avgComplianceScore": 100, "healthScore": 100, "complianceTrend": "stable", "eventsByType": {}, "resolutionEfficiency": 100}, "compliance": {"currentScore": 100, "targetScore": 80, "trendData": [], "lastAuditDate": "2025-10-22T13:22:22.919Z", "nextAuditDue": "2025-10-23T13:22:22.919Z", "previousViolationCount": 0, "isActive": true, "lastComplianceCheck": 0, "thresholds": {"minimumScore": 80, "warningThreshold": 70, "criticalThreshold": 50}}, "authority": {"validationsPerformed": 0, "validationsPassed": 0, "validationsFailed": 0, "averageResponseTime": 0, "lastValidation": "", "totalValidations": 0, "avgResponseTime": 0}, "activeViolations": []}