# Vision UI Theme - Quick Reference Card

**For**: OA Framework M0.2 Dashboard Development  
**Theme File**: `src/styles/vision-ui-theme.ts`

---

## 🎨 **IMPORT**

```typescript
import { visionCard, visionColors, visionBackground, visionStatCard, visionGradientText, visionButton } from '@/styles/vision-ui-theme';
```

---

## 🎯 **QUICK USAGE**

### **1. Background**

```tsx
<Box sx={{ ...visionBackground, p: 4 }}>
  {/* Your content */}
</Box>
```

**Note**: `visionBackground` is an object, not a function. Use it without parentheses.

---

### **2. Card**

```tsx
<Paper sx={{ ...visionCard(), p: 4 }}>
  {/* Card content */}
</Paper>
```

**With Glow**:
```tsx
<Paper sx={{ ...visionCard(visionColors.shadowBlue), p: 4 }}>
  {/* Card content with blue glow */}
</Paper>
```

---

### **3. Stat Card**

```tsx
<Card sx={{
  ...visionStatCard(
    'linear-gradient(127.09deg, rgba(0, 117, 255, 0.2) 19.41%, rgba(121, 40, 202, 0.2) 76.65%)',
    visionColors.shadowBlue
  ),
}}>
  <Speed sx={{ color: visionColors.blue, fontSize: 32 }} />
  <Typography variant="h3" sx={{ color: 'white', fontWeight: 'bold' }}>
    1,234
  </Typography>
  <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
    Total Items
  </Typography>
</Card>
```

---

### **4. Gradient Text**

```tsx
<Typography variant="h3" sx={{
  ...visionGradientText('linear-gradient(90deg, #0075FF 0%, #01B574 100%)')
}}>
  Gradient Title
</Typography>
```

**Note**: `visionGradientText` requires a gradient parameter. Common gradients:
- Blue → Cyan: `'linear-gradient(90deg, #0075FF 0%, #01B574 100%)'`
- Purple → Pink: `'linear-gradient(90deg, #7928CA 0%, #E31A89 100%)'`
- Blue → Purple: `'linear-gradient(90deg, #0075FF 0%, #7928CA 100%)'`

---

### **5. Button**

```tsx
<Button
  sx={{
    ...visionButton(
      'linear-gradient(90deg, #0075FF 0%, #7928CA 100%)',
      visionColors.shadowBlue
    ),
    px: 4,
    py: 1.5,
  }}
>
  Click Me
</Button>
```

---

### **6. Input Field**

```tsx
<TextField
  fullWidth
  sx={{
    ...visionInput,
    '& .MuiOutlinedInput-root': {
      ...visionInput['& .MuiOutlinedInput-root'],
    },
  }}
/>
```

---

## 🎨 **COLOR PALETTE**

```typescript
visionColors.blue      // #0075FF - Primary
visionColors.purple    // #7928CA - Secondary
visionColors.cyan      // #01B574 - Success
visionColors.pink      // #E31A89 - Warning
visionColors.orange    // #F49342 - Alert
```

---

## 💫 **SHADOWS**

```typescript
visionColors.shadowBlue    // Blue glow
visionColors.shadowPurple  // Purple glow
visionColors.shadowCyan    // Cyan glow
visionColors.shadowPink    // Pink glow
visionColors.shadowOrange  // Orange glow
```

---

## 🎯 **COMMON GRADIENTS**

### **Blue → Purple**
```typescript
'linear-gradient(90deg, #0075FF 0%, #7928CA 100%)'
```

### **Cyan → Blue**
```typescript
'linear-gradient(90deg, #01B574 0%, #0075FF 100%)'
```

### **Purple → Pink**
```typescript
'linear-gradient(90deg, #7928CA 0%, #E31A89 100%)'
```

### **Pink → Orange**
```typescript
'linear-gradient(90deg, #E31A89 0%, #F49342 100%)'
```

---

## 📐 **SPACING**

```typescript
borderRadius: '20px'  // Cards
borderRadius: '15px'  // Buttons, inputs
padding: 4            // Card padding (32px)
gap: 1.5              // Icon spacing
```

---

## 🎨 **TYPOGRAPHY**

### **Header**
```tsx
<Typography 
  variant="h3" 
  sx={{ 
    ...visionGradientText(),
    fontWeight: 'bold',
  }}
>
  Title
</Typography>
```

### **Body Text**
```tsx
<Typography 
  variant="body1" 
  sx={{ 
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: 1.6,
  }}
>
  Content
</Typography>
```

### **Label**
```tsx
<Typography 
  variant="body2" 
  sx={{ 
    color: 'rgba(255, 255, 255, 0.7)',
  }}
>
  Label
</Typography>
```

---

## 🎯 **HOVER EFFECTS**

### **Lift**
```tsx
'&:hover': {
  transform: 'translateY(-5px)',
  boxShadow: '0px 15px 35px rgba(0, 0, 0, 0.7)',
}
```

### **Slide**
```tsx
'&:hover': {
  transform: 'translateX(5px)',
}
```

### **Glow Intensify**
```tsx
'&:hover': {
  boxShadow: visionColors.shadowBlue,
}
```

---

## 📊 **EXAMPLE: Complete Component**

```tsx
import { visionCard, visionColors, visionButton } from '@/styles/vision-ui-theme';

export function MyComponent() {
  return (
    <Paper sx={{ ...visionCard(), p: 4 }}>
      <Typography 
        variant="h5" 
        sx={{ 
          color: 'white', 
          fontWeight: 'bold',
          mb: 3,
        }}
      >
        My Component
      </Typography>
      
      <Button
        sx={{
          ...visionButton(
            'linear-gradient(90deg, #0075FF 0%, #7928CA 100%)',
            visionColors.shadowBlue
          ),
        }}
      >
        Action
      </Button>
    </Paper>
  );
}
```

---

## ✅ **CHECKLIST**

- [ ] Import vision-ui-theme
- [ ] Apply visionBackground to page
- [ ] Use visionCard for containers
- [ ] Apply gradient text to headers
- [ ] Use colorful stat cards
- [ ] Style buttons with gradients
- [ ] Add hover effects
- [ ] Use white text with opacity for readability
- [ ] Apply consistent spacing (20px radius, 4 padding)

---

**Quick Start**: Copy examples above and customize colors/gradients as needed!

