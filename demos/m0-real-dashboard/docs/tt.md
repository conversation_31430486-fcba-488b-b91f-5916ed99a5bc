graph TB
    subgraph "User Interface Layer"
        Page["/m02-query-optimization<br/>Next.js Page<br/>27 lines"]
        Dashboard[QueryOptimizationDashboard<br/>Main Container<br/>193 lines]
    end
    
    subgraph "Component Layer"
        Input[QueryInputPanel<br/>SQL Input + Highlighting<br/>164 lines]
        Plan[ExecutionPlanVisualization<br/>Tree Visualization<br/>161 lines]
        Recs[OptimizationRecommendations<br/>Suggestions Display<br/>191 lines]
        Perf[PerformanceComparison<br/>Before/After Metrics<br/>209 lines]
    end
    
    subgraph "API Layer"
        API[query-optimization-api.ts<br/>Backend Integration<br/>185 lines]
    end
    
    subgraph "External Libraries"
        Flow[@xyflow/react<br/>Tree Visualization]
        Syntax[react-syntax-highlighter<br/>SQL Highlighting]
        Charts[recharts<br/>Performance Charts]
        MUI[@mui/material<br/>UI Components]
    end
    
    subgraph "Backend Service"
        Backend[Query Optimization Engine<br/>ENH-TSK-08.SUB-08.1.IMP-05<br/>✅ Complete]
    end
    
    Page --> Dashboard
    Dashboard --> Input
    Dashboard --> Plan
    Dashboard --> Recs
    Dashboard --> Perf
    Dashboard --> API
    
    Input --> Syntax
    Input --> MUI
    Plan --> Flow
    Plan --> MUI
    Recs --> Syntax
    Recs --> MUI
    Perf --> Charts
    Perf --> MUI
    
    API -.-> Backend
    
    style Page fill:#60a5fa,stroke:#3b82f6,stroke-width:2px,color:#000
    style Dashboard fill:#34d399,stroke:#10b981,stroke-width:3px,color:#000
    style Input fill:#a78bfa,stroke:#7c3aed,stroke-width:2px,color:#000
    style Plan fill:#a78bfa,stroke:#7c3aed,stroke-width:2px,color:#000
    style Recs fill:#a78bfa,stroke:#7c3aed,stroke-width:2px,color:#000
    style Perf fill:#a78bfa,stroke:#7c3aed,stroke-width:2px,color:#000
    style API fill:#f59e0b,stroke:#d97706,stroke-width:2px,color:#000
    style Flow fill:#e5e7eb,stroke:#9ca3af,stroke-width:1px,color:#000
    style Syntax fill:#e5e7eb,stroke:#9ca3af,stroke-width:1px,color:#000
    style Charts fill:#e5e7eb,stroke:#9ca3af,stroke-width:1px,color:#000
    style MUI fill:#e5e7eb,stroke:#9ca3af,stroke-width:1px,color:#000
    style Backend fill:#22c55e,stroke:#16a34a,stroke-width:2px,color:#000
