# 🚀 M0 Real Dashboard - API Quick Reference

**Quick reference guide for developers using the M0 Real Dashboard API**

---

## 📋 **AVAILABLE ENDPOINTS**

| Endpoint | Purpose | Components | Methods |
|----------|---------|------------|---------|
| `/api/m0-components` | All components | 136 total | GET, POST |
| `/api/m0-governance` | Governance data | 60+ | GET, POST |
| `/api/m0-tracking` | Tracking data | 40+ | GET, POST |
| `/api/m0-security` | Security data | 20+ | GET, POST |
| `/api/m0-integration` | Integration data | 8 | GET, POST |

---

## 🔍 **QUERY PARAMETERS** (All GET Endpoints)

### **Filtering**
```bash
?status=healthy              # Filter by status: healthy | warning | error
?minHealth=80                # Minimum health score (0-100)
?maxHealth=100               # Maximum health score (0-100)
?search=memory               # Search in component name/description
```

### **Sorting**
```bash
?sortBy=health               # Sort by: name | health | status
?order=desc                  # Order: asc | desc
```

### **Pagination**
```bash
?page=1                      # Page number (default: 1)
?limit=20                    # Items per page (default: 50, max: 100)
```

### **Combined Example**
```bash
GET /api/m0-governance?status=healthy&minHealth=90&sortBy=health&order=desc&page=1&limit=20
```

---

## 📊 **RESPONSE FORMAT** (All Endpoints)

### **Success Response**
```json
{
  "success": true,
  "data": {
    "total[Category]Components": 60,
    "healthyComponents": 58,
    "errorComponents": 2,
    "components": [
      {
        "id": "component-id",
        "name": "Component Name",
        "status": "healthy",
        "health": 95,
        "[category]Type": "specific-type",
        "lastCheck": "2025-10-21T10:30:00Z"
      }
    ],
    "metrics": {
      // Category-specific metrics
    },
    "pagination": {
      "page": 1,
      "limit": 20,
      "totalPages": 3,
      "totalCount": 60
    }
  },
  "timestamp": "2025-10-21T10:30:00.000Z"
}
```

### **Error Response**
```json
{
  "success": false,
  "error": "Error message",
  "timestamp": "2025-10-21T10:30:00.000Z"
}
```

---

## 🎯 **GOVERNANCE API** - `/api/m0-governance`

### **GET Request**
```bash
# Get all healthy governance components
curl "http://localhost:3000/api/m0-governance?status=healthy"

# Get components with high compliance scores
curl "http://localhost:3000/api/m0-governance?minHealth=90&sortBy=health&order=desc"

# Search for specific governance components
curl "http://localhost:3000/api/m0-governance?search=compliance&limit=10"
```

### **Response Metrics**
```json
{
  "metrics": {
    "complianceScore": 95,
    "ruleCount": 150,
    "violationCount": 5,
    "frameworksActive": 3
  }
}
```

### **Component Types**
- `rule-engine` - Rule processing engines
- `compliance` - Compliance tracking systems
- `framework` - Governance frameworks
- `analytics` - Governance analytics
- `reporting` - Governance reporting

### **POST Operations**
```bash
# Run compliance check
curl -X POST "http://localhost:3000/api/m0-governance" \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "compliance-check",
    "componentId": "governance-framework-001",
    "parameters": {}
  }'

# Validate rules
curl -X POST "http://localhost:3000/api/m0-governance" \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "rule-validation",
    "componentId": "rule-engine-001",
    "parameters": {}
  }'

# Run framework audit
curl -X POST "http://localhost:3000/api/m0-governance" \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "framework-audit",
    "componentId": "framework-001",
    "parameters": {}
  }'
```

---

## 📈 **TRACKING API** - `/api/m0-tracking`

### **GET Request**
```bash
# Get all tracking components
curl "http://localhost:3000/api/m0-tracking"

# Get session tracking components
curl "http://localhost:3000/api/m0-tracking?search=session"

# Get high-performance tracking components
curl "http://localhost:3000/api/m0-tracking?minHealth=95&sortBy=health"
```

### **Response Metrics**
```json
{
  "metrics": {
    "activeSessions": 42,
    "totalEvents": 15000,
    "averageResponseTime": 25,
    "dataProcessingRate": 1000
  }
}
```

### **Component Types**
- `session` - Session tracking
- `analytics` - Analytics tracking
- `orchestration` - Orchestration tracking
- `progress` - Progress tracking
- `data-management` - Data management tracking

### **POST Operations**
```bash
# Analyze sessions
curl -X POST "http://localhost:3000/api/m0-tracking" \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "session-analysis",
    "componentId": "session-tracker-001",
    "parameters": {}
  }'

# Process data
curl -X POST "http://localhost:3000/api/m0-tracking" \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "data-processing",
    "componentId": "data-processor-001",
    "parameters": {}
  }'

# Check orchestration status
curl -X POST "http://localhost:3000/api/m0-tracking" \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "orchestration-status",
    "componentId": "orchestrator-001",
    "parameters": {}
  }'
```

---

## 🔒 **SECURITY API** - `/api/m0-security`

### **GET Request**
```bash
# Get all security components
curl "http://localhost:3000/api/m0-security"

# Get memory management components
curl "http://localhost:3000/api/m0-security?search=memory"

# Get components with high threat levels
curl "http://localhost:3000/api/m0-security?minHealth=80"
```

### **Response Metrics**
```json
{
  "metrics": {
    "memoryUsage": 65.5,
    "bufferUtilization": 45.2,
    "threatLevel": "low",
    "activeProtections": 12
  }
}
```

### **Component Types**
- `memory-management` - Memory management systems
- `buffer-protection` - Buffer protection mechanisms
- `event-handling` - Event handling security
- `environment-control` - Environment control systems

### **Threat Levels**
- `low` - Normal operation, no threats detected
- `medium` - Minor issues detected, monitoring required
- `high` - Critical issues detected, immediate action required

### **POST Operations**
```bash
# Run memory scan
curl -X POST "http://localhost:3000/api/m0-security" \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "memory-scan",
    "componentId": "memory-manager-001",
    "parameters": {}
  }'

# Analyze buffers
curl -X POST "http://localhost:3000/api/m0-security" \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "buffer-analysis",
    "componentId": "buffer-manager-001",
    "parameters": {}
  }'

# Run security audit
curl -X POST "http://localhost:3000/api/m0-security" \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "security-audit",
    "componentId": "security-system-001",
    "parameters": {}
  }'
```

---

## 🔗 **INTEGRATION API** - `/api/m0-integration`

### **GET Request**
```bash
# Get all integration components
curl "http://localhost:3000/api/m0-integration"

# Get bridge components
curl "http://localhost:3000/api/m0-integration?search=bridge"

# Get healthy integration components
curl "http://localhost:3000/api/m0-integration?status=healthy"
```

### **Response Metrics**
```json
{
  "metrics": {
    "activeBridges": 5,
    "messagesThroughput": 500,
    "integrationHealth": 98,
    "crossComponentCalls": 1200
  }
}
```

### **Component Types**
- `bridge` - Integration bridges
- `coordinator` - Integration coordinators
- `monitor` - Integration monitors
- `validator` - Integration validators

### **POST Operations**
```bash
# Test bridge
curl -X POST "http://localhost:3000/api/m0-integration" \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "bridge-test",
    "componentId": "bridge-001",
    "parameters": {}
  }'

# Check coordination
curl -X POST "http://localhost:3000/api/m0-integration" \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "coordination-check",
    "componentId": "coordinator-001",
    "parameters": {}
  }'

# Check integration health
curl -X POST "http://localhost:3000/api/m0-integration" \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "integration-health",
    "componentId": "monitor-001",
    "parameters": {}
  }'
```

---

## 💻 **JAVASCRIPT/TYPESCRIPT USAGE**

### **Using Fetch API**
```typescript
// GET request with query parameters
async function getGovernanceComponents() {
  const params = new URLSearchParams({
    status: 'healthy',
    minHealth: '90',
    sortBy: 'health',
    order: 'desc',
    page: '1',
    limit: '20'
  });
  
  const response = await fetch(`/api/m0-governance?${params}`);
  const data = await response.json();
  
  if (data.success) {
    console.log('Components:', data.data.components);
    console.log('Metrics:', data.data.metrics);
  } else {
    console.error('Error:', data.error);
  }
}

// POST request
async function runComplianceCheck(componentId: string) {
  const response = await fetch('/api/m0-governance', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      operation: 'compliance-check',
      componentId,
      parameters: {}
    })
  });
  
  const data = await response.json();
  return data;
}
```

### **Using React Hook**
```typescript
import { useState, useEffect } from 'react';

function useGovernanceData() {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    async function fetchData() {
      try {
        const response = await fetch('/api/m0-governance?status=healthy');
        const result = await response.json();
        
        if (result.success) {
          setData(result.data);
        } else {
          setError(result.error);
        }
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }
    
    fetchData();
  }, []);
  
  return { data, loading, error };
}
```

---

## 🎨 **COMMON PATTERNS**

### **Pattern 1: Get All Healthy Components**
```bash
curl "/api/m0-[category]?status=healthy"
```

### **Pattern 2: Search and Filter**
```bash
curl "/api/m0-[category]?search=keyword&minHealth=80&limit=10"
```

### **Pattern 3: Paginated Results**
```bash
curl "/api/m0-[category]?page=1&limit=20&sortBy=name&order=asc"
```

### **Pattern 4: High-Performance Components**
```bash
curl "/api/m0-[category]?minHealth=95&sortBy=health&order=desc"
```

### **Pattern 5: Error Components Only**
```bash
curl "/api/m0-[category]?status=error"
```

---

## 🔧 **TROUBLESHOOTING**

### **Issue: Empty Results**
**Solution**: Check if components exist in that category
```bash
# Get total count without filters
curl "/api/m0-[category]"
```

### **Issue: Invalid Query Parameters**
**Solution**: Verify parameter names and values
```bash
# Valid status values: healthy, warning, error
# Valid sortBy values: name, health, status
# Valid order values: asc, desc
```

### **Issue: POST Operation Fails**
**Solution**: Verify operation name and component ID
```bash
# Check valid operations in API documentation
# Ensure componentId exists in the category
```

---

## 📚 **ADDITIONAL RESOURCES**

- **Full Documentation**: `PRIORITY-2-API-ROUTES-COMPLETE.md`
- **Implementation Summary**: `API-IMPLEMENTATION-SUMMARY.md`
- **Test Suite**: `__tests__/integration/api-routes.test.ts`
- **Source Code**: `src/app/api/m0-[category]/route.ts`

---

**Last Updated**: 2025-10-21  
**Version**: 1.0.0  
**Status**: Production Ready

