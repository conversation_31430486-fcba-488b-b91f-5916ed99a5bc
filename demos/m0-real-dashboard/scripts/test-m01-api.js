#!/usr/bin/env node

/**
 * ============================================================================
 * M0.1 API Routes Test Script
 * ============================================================================
 * 
 * Quick verification script to test M0.1 API routes functionality.
 * Tests all 4 API routes and validates responses.
 * 
 * Usage: node scripts/test-m01-api.js
 * 
 * Author: AI Assistant (Phase 1 Day 1 Implementation)
 * Created: 2025-12-31
 * ============================================================================
 */

const BASE_URL = 'http://localhost:3000';

const API_ROUTES = [
  {
    name: 'Overview',
    path: '/api/m01-enhancements/overview',
    method: 'GET',
    expectedKeys: ['success', 'data', 'timestamp']
  },
  {
    name: 'Components',
    path: '/api/m01-enhancements/components',
    method: 'GET',
    expectedKeys: ['success', 'data', 'timestamp']
  },
  {
    name: 'Metrics',
    path: '/api/m01-enhancements/metrics',
    method: 'GET',
    expectedKeys: ['success', 'data', 'timestamp']
  },
  {
    name: 'Comparison',
    path: '/api/m01-enhancements/comparison',
    method: 'GET',
    expectedKeys: ['success', 'data', 'timestamp']
  }
];

async function testRoute(route) {
  const url = `${BASE_URL}${route.path}`;
  
  try {
    console.log(`\n🧪 Testing ${route.name} API...`);
    console.log(`   URL: ${url}`);
    
    const response = await fetch(url, { method: route.method });
    const data = await response.json();
    
    // Check status code
    if (response.status !== 200 && response.status !== 503) {
      console.error(`   ❌ FAIL: Unexpected status code ${response.status}`);
      return false;
    }
    
    // Check expected keys
    const hasAllKeys = route.expectedKeys.every(key => key in data);
    if (!hasAllKeys) {
      console.error(`   ❌ FAIL: Missing expected keys`);
      console.error(`   Expected: ${route.expectedKeys.join(', ')}`);
      console.error(`   Got: ${Object.keys(data).join(', ')}`);
      return false;
    }
    
    // Check success field
    if (response.status === 200 && !data.success) {
      console.error(`   ❌ FAIL: success=false but status=200`);
      return false;
    }
    
    console.log(`   ✅ PASS: Status ${response.status}, all keys present`);
    
    // Log data summary
    if (data.success && data.data) {
      if (route.name === 'Overview') {
        console.log(`   📊 Tasks: ${data.data.summary?.totalTasks || 'N/A'}`);
        console.log(`   📊 Completion: ${data.data.summary?.completionPercentage || 'N/A'}%`);
      } else if (route.name === 'Components') {
        console.log(`   🔧 Components: ${data.data.totalComponents || 'N/A'}`);
      } else if (route.name === 'Metrics') {
        console.log(`   📈 Avg Performance: ${data.data.summary?.averagePerformance || 'N/A'}`);
      } else if (route.name === 'Comparison') {
        console.log(`   ⚖️ M0 Components: ${data.data.m0Stats?.totalComponents || 'N/A'}`);
        console.log(`   ⚖️ M0.1 Tasks: ${data.data.m01Stats?.totalTasks || 'N/A'}`);
      }
    }
    
    return true;
    
  } catch (error) {
    console.error(`   ❌ FAIL: ${error.message}`);
    return false;
  }
}

async function runTests() {
  console.log('============================================================================');
  console.log('M0.1 API Routes Test Suite');
  console.log('============================================================================');
  console.log(`Base URL: ${BASE_URL}`);
  console.log(`Routes to test: ${API_ROUTES.length}`);
  
  const results = [];
  
  for (const route of API_ROUTES) {
    const passed = await testRoute(route);
    results.push({ route: route.name, passed });
  }
  
  console.log('\n============================================================================');
  console.log('Test Results Summary');
  console.log('============================================================================');
  
  const passedCount = results.filter(r => r.passed).length;
  const failedCount = results.length - passedCount;
  
  results.forEach(result => {
    const icon = result.passed ? '✅' : '❌';
    const status = result.passed ? 'PASS' : 'FAIL';
    console.log(`${icon} ${result.route}: ${status}`);
  });
  
  console.log('\n============================================================================');
  console.log(`Total: ${results.length} | Passed: ${passedCount} | Failed: ${failedCount}`);
  console.log('============================================================================');
  
  if (failedCount === 0) {
    console.log('\n🎉 All tests passed!');
    process.exit(0);
  } else {
    console.log('\n❌ Some tests failed. Please check the output above.');
    process.exit(1);
  }
}

// Check if server is running
async function checkServer() {
  try {
    const response = await fetch(BASE_URL);
    return response.ok || response.status === 404; // 404 is fine, means server is running
  } catch (error) {
    return false;
  }
}

// Main execution
(async () => {
  console.log('Checking if development server is running...');
  const serverRunning = await checkServer();
  
  if (!serverRunning) {
    console.error('\n❌ ERROR: Development server is not running!');
    console.error('Please start the server with: npm run dev');
    console.error('Then run this script again.');
    process.exit(1);
  }
  
  console.log('✅ Server is running\n');
  await runTests();
})();

