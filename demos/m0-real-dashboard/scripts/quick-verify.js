/**
 * Quick verification script for M0.1 components registry
 * Run with: node demos/m0-real-dashboard/scripts/quick-verify.js
 */

// Simple verification without TypeScript compilation
const fs = require('fs');
const path = require('path');

const registryPath = path.join(__dirname, '../src/lib/m01-components.ts');
const content = fs.readFileSync(registryPath, 'utf-8');

console.log('╔════════════════════════════════════════════════════════════════╗');
console.log('║         M0.1 COMPONENTS REGISTRY QUICK VERIFICATION            ║');
console.log('╚════════════════════════════════════════════════════════════════╝\n');

// Count tasks
const taskMatches = content.match(/id: 'ENH-TSK-\d{2}\./g);
const taskCount = taskMatches ? taskMatches.length : 0;

// Count enhanced components
const componentMatches = content.match(/id: '[a-z-]+',\s+name: '[A-Za-z]+Enhanced'/g);
const componentCount = componentMatches ? componentMatches.length : 0;

// Count COMPLETE statuses (exact match for status field)
// Note: This includes 1 occurrence in the interface definition (line 51)
// So we expect 51 total: 50 tasks + 1 interface definition
const completeMatches = content.match(/status: 'COMPLETE'/g);
const completeCount = completeMatches ? completeMatches.length : 0;

// Check for helper functions
const helperFunctions = [
  'getAllM01Tasks',
  'getAllM01Components',
  'getTasksByCategory',
  'getComponentsByCategory',
  'getTaskById',
  'getComponentById',
  'getMilestoneStats',
  'getCategoryNames',
  'searchTasks',
  'searchComponents',
  'exportMilestoneSummary'
];

const missingFunctions = helperFunctions.filter(fn => !content.includes(`export function ${fn}`));

console.log('📊 Registry Statistics:\n');
console.log(`✅ Total Tasks Found: ${taskCount} (Expected: 50)`);
console.log(`✅ Enhanced Components Found: ${componentCount} (Expected: 6)`);
console.log(`✅ COMPLETE Status Count: ${completeCount} (Expected: 51 - includes interface definition)`);
console.log(`✅ File Size: ${(content.length / 1024).toFixed(2)} KB`);
console.log(`✅ Lines of Code: ${content.split('\n').length}`);

console.log('\n🔧 Helper Functions:\n');
helperFunctions.forEach(fn => {
  const exists = content.includes(`export function ${fn}`);
  console.log(`${exists ? '✅' : '❌'} ${fn}`);
});

console.log('\n📂 Categories Found:\n');
const categories = ['ENH-TSK-01', 'ENH-TSK-02', 'ENH-TSK-03', 'ENH-TSK-04', 'ENH-TSK-05', 'ENH-TSK-06', 'ENH-TSK-07', 'ENH-TSK-08'];
categories.forEach(cat => {
  const count = (content.match(new RegExp(`category: '${cat}'`, 'g')) || []).length;
  console.log(`   ${cat}: ${count} tasks`);
});

console.log('\n🎯 Component Categories:\n');
const componentCategories = ['memory-safety', 'event-handling', 'timing', 'cleanup', 'buffer'];
componentCategories.forEach(cat => {
  const count = (content.match(new RegExp(`category: '${cat}'`, 'g')) || []).length;
  console.log(`   ${cat}: ${count} components`);
});

console.log('\n╔════════════════════════════════════════════════════════════════╗');
console.log('║                    VERIFICATION RESULTS                        ║');
console.log('╚════════════════════════════════════════════════════════════════╝\n');

const allChecks = [
  { name: 'Task Count', passed: taskCount === 50 },
  { name: 'Component Count', passed: componentCount === 6 },
  { name: 'All Tasks Complete', passed: completeCount === 51 }, // 50 tasks + 1 interface definition
  { name: 'Helper Functions', passed: missingFunctions.length === 0 }
];

allChecks.forEach(check => {
  console.log(`${check.passed ? '✅' : '❌'} ${check.name}`);
});

const allPassed = allChecks.every(check => check.passed);

console.log('\n' + '═'.repeat(66));
if (allPassed) {
  console.log('🎉 ALL VERIFICATIONS PASSED! M0.1 Registry is ready for use.');
} else {
  console.log('⚠️  SOME VERIFICATIONS FAILED! Please review the results above.');
}
console.log('═'.repeat(66) + '\n');

process.exit(allPassed ? 0 : 1);

