/**
 * Test script to investigate component count discrepancy
 * Task A: Component Investigation
 */

import { getM0ComponentManager } from '../src/lib/M0ComponentManager';

async function testComponentCount() {
  console.log('🔍 Starting Component Count Investigation (Task A)...\n');

  try {
    const manager = await getM0ComponentManager();

    const dashboardData = manager.getDashboardData();

    console.log('\n📊 Final Component Count:');
    console.log(`Total Components: ${dashboardData.totalComponents}`);
    console.log(`Governance: ${dashboardData.categories.governance.length}`);
    console.log(`Tracking: ${dashboardData.categories.tracking.length}`);
    console.log(`Memory Safety: ${dashboardData.categories.memorySafety.length}`);
    console.log(`Integration: ${dashboardData.categories.integration.length}`);
    console.log(`Health Score: ${dashboardData.overallHealthScore}%`);

    await manager.shutdown();

    console.log('\n✅ Investigation complete!');

  } catch (error) {
    console.error('❌ Error during investigation:', error);
    process.exit(1);
  }
}

testComponentCount();

