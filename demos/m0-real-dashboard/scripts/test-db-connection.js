/**
 * Test PostgreSQL Database Connection
 * 
 * This script tests the database connection and displays connection info
 */

const { Pool } = require('pg');

async function testConnection() {
  console.log('🔍 Testing PostgreSQL Database Connection...\n');

  const pool = new Pool({
    user: 'postgres',
    password: 'paas123',
    host: 'localhost',
    port: 5432,
    database: 'postgres',
  });

  try {
    // Test basic connection
    console.log('📡 Connecting to database...');
    const client = await pool.connect();
    console.log('✅ Connection successful!\n');

    // Get database version
    const versionResult = await client.query('SELECT version()');
    console.log('📊 Database Version:');
    console.log(versionResult.rows[0].version);
    console.log('');

    // Get current database and user
    const dbResult = await client.query('SELECT current_database(), current_user');
    console.log('📋 Connection Details:');
    console.log(`   Database: ${dbResult.rows[0].current_database}`);
    console.log(`   User: ${dbResult.rows[0].current_user}`);
    console.log('');

    // Get active connections
    const connectionsResult = await client.query(
      "SELECT count(*) as count FROM pg_stat_activity WHERE state = 'active'"
    );
    console.log('🔗 Active Connections:', connectionsResult.rows[0].count);
    console.log('');

    // Test a simple query
    console.log('🧪 Testing simple query...');
    const testResult = await client.query('SELECT NOW() as current_time');
    console.log('✅ Query successful!');
    console.log(`   Current Time: ${testResult.rows[0].current_time}`);
    console.log('');

    client.release();
    console.log('✅ All tests passed! Database is ready for use.\n');
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    console.error('\n📝 Please check:');
    console.error('   1. PostgreSQL server is running');
    console.error('   2. Connection details are correct (user, password, host, port)');
    console.error('   3. Database "postgres" exists');
    console.error('   4. User has proper permissions\n');
    process.exit(1);
  } finally {
    await pool.end();
  }
}

testConnection();

