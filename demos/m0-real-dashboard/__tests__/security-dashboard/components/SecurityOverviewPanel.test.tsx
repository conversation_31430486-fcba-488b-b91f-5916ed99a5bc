/**
 * ============================================================================
 * OA FRAMEWORK - M0 REAL DASHBOARD
 * Security Overview Panel Component Tests
 * ============================================================================
 * 
 * @fileoverview Tests for SecurityOverviewPanel component
 * @module __tests__/security-dashboard/components/SecurityOverviewPanel.test
 * @version 1.0.0
 * @since 2025-10-22
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2025 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { SecurityOverviewPanel } from '@/components/security/SecurityOverviewPanel';
import type { ISecurityData } from '@/types/security-types';

// ============================================================================
// TEST DATA
// ============================================================================

const mockSecurityData: ISecurityData = {
  totalSecurityComponents: 19,
  healthyComponents: 19,
  errorComponents: 0,
  filteredCount: 19,
  page: 1,
  limit: 50,
  totalPages: 1,
  metrics: {
    memoryUsage: 45.2,
    bufferUtilization: 62.8,
    threatLevel: 'low',
    activeProtections: 19,
    lastSecurityScan: new Date().toISOString(),
  },
  components: [],
  query: {},
};

// ============================================================================
// TEST SUITE
// ============================================================================

describe('SecurityOverviewPanel', () => {
  // ============================================================================
  // RENDERING TESTS
  // ============================================================================

  describe('Rendering', () => {
    it('should render security overview panel', () => {
      render(<SecurityOverviewPanel data={mockSecurityData} />);

      expect(screen.getByText('Security Overview')).toBeInTheDocument();
    });

    it('should display total security components', () => {
      render(<SecurityOverviewPanel data={mockSecurityData} />);

      expect(screen.getByText('19')).toBeInTheDocument();
      expect(screen.getByText(/total components/i)).toBeInTheDocument();
    });

    it('should display healthy components count', () => {
      render(<SecurityOverviewPanel data={mockSecurityData} />);

      expect(screen.getByText('19')).toBeInTheDocument();
      expect(screen.getByText(/healthy/i)).toBeInTheDocument();
    });

    it('should display threat level', () => {
      render(<SecurityOverviewPanel data={mockSecurityData} />);

      expect(screen.getByText(/low/i)).toBeInTheDocument();
      expect(screen.getByText(/threat level/i)).toBeInTheDocument();
    });

    it('should display active protections count', () => {
      render(<SecurityOverviewPanel data={mockSecurityData} />);

      expect(screen.getByText('19')).toBeInTheDocument();
      expect(screen.getByText(/active protections/i)).toBeInTheDocument();
    });
  });

  // ============================================================================
  // THREAT LEVEL TESTS
  // ============================================================================

  describe('Threat Level Display', () => {
    it('should display low threat level with green color', () => {
      const data = { ...mockSecurityData, metrics: { ...mockSecurityData.metrics, threatLevel: 'low' as const } };
      const { container } = render(<SecurityOverviewPanel data={data} />);

      const threatLevelElement = container.querySelector('.bg-green-100');
      expect(threatLevelElement).toBeInTheDocument();
    });

    it('should display medium threat level with yellow color', () => {
      const data = { ...mockSecurityData, metrics: { ...mockSecurityData.metrics, threatLevel: 'medium' as const } };
      const { container } = render(<SecurityOverviewPanel data={data} />);

      const threatLevelElement = container.querySelector('.bg-yellow-100');
      expect(threatLevelElement).toBeInTheDocument();
    });

    it('should display high threat level with red color', () => {
      const data = { ...mockSecurityData, metrics: { ...mockSecurityData.metrics, threatLevel: 'high' as const } };
      const { container } = render(<SecurityOverviewPanel data={data} />);

      const threatLevelElement = container.querySelector('.bg-red-100');
      expect(threatLevelElement).toBeInTheDocument();
    });
  });

  // ============================================================================
  // HEALTH PERCENTAGE TESTS
  // ============================================================================

  describe('Health Percentage Calculation', () => {
    it('should calculate 100% health when all components are healthy', () => {
      const data = {
        ...mockSecurityData,
        totalSecurityComponents: 19,
        healthyComponents: 19,
        errorComponents: 0,
      };
      render(<SecurityOverviewPanel data={data} />);

      expect(screen.getByText(/100%/i)).toBeInTheDocument();
    });

    it('should calculate correct health percentage with errors', () => {
      const data = {
        ...mockSecurityData,
        totalSecurityComponents: 20,
        healthyComponents: 18,
        errorComponents: 2,
      };
      render(<SecurityOverviewPanel data={data} />);

      expect(screen.getByText(/90\.0/i)).toBeInTheDocument();
    });

    it('should handle zero components gracefully', () => {
      const data = {
        ...mockSecurityData,
        totalSecurityComponents: 0,
        healthyComponents: 0,
        errorComponents: 0,
      };
      
      // Should not throw error
      expect(() => render(<SecurityOverviewPanel data={data} />)).not.toThrow();
    });
  });

  // ============================================================================
  // WARNING COMPONENTS TESTS
  // ============================================================================

  describe('Warning Components Display', () => {
    it('should calculate warning components correctly', () => {
      const data = {
        ...mockSecurityData,
        totalSecurityComponents: 20,
        healthyComponents: 15,
        errorComponents: 2,
      };
      render(<SecurityOverviewPanel data={data} />);

      // Warning components = 20 - 15 - 2 = 3
      expect(screen.getByText('3')).toBeInTheDocument();
    });

    it('should show zero warnings when all components are healthy or error', () => {
      const data = {
        ...mockSecurityData,
        totalSecurityComponents: 20,
        healthyComponents: 18,
        errorComponents: 2,
      };
      render(<SecurityOverviewPanel data={data} />);

      // Warning components = 20 - 18 - 2 = 0
      expect(screen.getByText('0')).toBeInTheDocument();
    });
  });

  // ============================================================================
  // CUSTOM CLASSNAME TESTS
  // ============================================================================

  describe('Custom ClassName', () => {
    it('should apply custom className', () => {
      const { container } = render(<SecurityOverviewPanel data={mockSecurityData} className="custom-class" />);

      const panel = container.querySelector('.custom-class');
      expect(panel).toBeInTheDocument();
    });

    it('should work without custom className', () => {
      expect(() => render(<SecurityOverviewPanel data={mockSecurityData} />)).not.toThrow();
    });
  });

  // ============================================================================
  // METRICS DISPLAY TESTS
  // ============================================================================

  describe('Metrics Display', () => {
    it('should display memory usage percentage', () => {
      render(<SecurityOverviewPanel data={mockSecurityData} />);

      expect(screen.getByText(/45\.2%/i)).toBeInTheDocument();
    });

    it('should display buffer utilization percentage', () => {
      render(<SecurityOverviewPanel data={mockSecurityData} />);

      expect(screen.getByText(/62\.8%/i)).toBeInTheDocument();
    });

    it('should handle high memory usage (>80%)', () => {
      const data = { ...mockSecurityData, metrics: { ...mockSecurityData.metrics, memoryUsage: 85.5 } };
      render(<SecurityOverviewPanel data={data} />);

      expect(screen.getByText(/85\.5%/i)).toBeInTheDocument();
    });

    it('should handle critical memory usage (>90%)', () => {
      const data = { ...mockSecurityData, metrics: { ...mockSecurityData.metrics, memoryUsage: 95.2 } };
      render(<SecurityOverviewPanel data={data} />);

      expect(screen.getByText(/95\.2%/i)).toBeInTheDocument();
    });
  });

  // ============================================================================
  // EDGE CASES
  // ============================================================================

  describe('Edge Cases', () => {
    it('should handle missing metrics gracefully', () => {
      const data = {
        ...mockSecurityData,
        metrics: {
          memoryUsage: 0,
          bufferUtilization: 0,
          threatLevel: 'low' as const,
          activeProtections: 0,
          lastSecurityScan: new Date().toISOString(),
        },
      };

      expect(() => render(<SecurityOverviewPanel data={data} />)).not.toThrow();
    });

    it('should handle very large component counts', () => {
      const data = {
        ...mockSecurityData,
        totalSecurityComponents: 1000,
        healthyComponents: 950,
        errorComponents: 25,
      };

      render(<SecurityOverviewPanel data={data} />);

      expect(screen.getByText('1000')).toBeInTheDocument();
      expect(screen.getByText('950')).toBeInTheDocument();
      expect(screen.getByText('25')).toBeInTheDocument();
    });
  });
});

