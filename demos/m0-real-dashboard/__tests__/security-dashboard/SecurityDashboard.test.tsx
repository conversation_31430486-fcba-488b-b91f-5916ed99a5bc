/**
 * ============================================================================
 * OA FRAMEWORK - M0 REAL DASHBOARD
 * Security Dashboard Tests
 * ============================================================================
 * 
 * @fileoverview Comprehensive tests for Security Dashboard page
 * @module __tests__/security-dashboard/SecurityDashboard.test
 * @version 1.0.0
 * @since 2025-10-22
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2025 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import SecurityDashboardPage from '@/app/security-dashboard/page';

// Mock the hooks
jest.mock('@/hooks/useSecurityData');
jest.mock('@/hooks/useSecurityOperations');
jest.mock('@/hooks/useSecurityAlerts');

// Mock the components
jest.mock('@/components/security/SecurityOverviewPanel', () => ({
  SecurityOverviewPanel: ({ data }: { data: unknown }) => (
    <div data-testid="security-overview-panel">Security Overview Panel - {JSON.stringify(data)}</div>
  ),
}));

jest.mock('@/components/security/MemoryUsageMonitor', () => ({
  MemoryUsageMonitor: ({ data }: { data: unknown }) => (
    <div data-testid="memory-usage-monitor">Memory Usage Monitor - {JSON.stringify(data)}</div>
  ),
}));

jest.mock('@/components/security/BufferUtilizationChart', () => ({
  BufferUtilizationChart: ({ data }: { data: unknown }) => (
    <div data-testid="buffer-utilization-chart">Buffer Utilization Chart - {JSON.stringify(data)}</div>
  ),
}));

jest.mock('@/components/security/ThreatLevelIndicator', () => ({
  ThreatLevelIndicator: ({ threatLevel }: { threatLevel: string }) => (
    <div data-testid="threat-level-indicator">Threat Level: {threatLevel}</div>
  ),
}));

jest.mock('@/components/security/ComponentStatusGrid', () => ({
  ComponentStatusGrid: ({ components }: { components: unknown[] }) => (
    <div data-testid="component-status-grid">Components: {components.length}</div>
  ),
}));

jest.mock('@/components/security/SecurityOperationsPanel', () => ({
  SecurityOperationsPanel: () => (
    <div data-testid="security-operations-panel">Security Operations Panel</div>
  ),
}));

jest.mock('@/components/security/OperationResultsDisplay', () => ({
  OperationResultsDisplay: () => (
    <div data-testid="operation-results-display">Operation Results Display</div>
  ),
}));

jest.mock('@/components/security/AlertNotification', () => ({
  AlertNotification: () => (
    <div data-testid="alert-notification">Alert Notification</div>
  ),
}));

jest.mock('@/components/security/AlertHistoryPanel', () => ({
  AlertHistoryPanel: () => (
    <div data-testid="alert-history-panel">Alert History Panel</div>
  ),
}));

// ============================================================================
// TEST DATA
// ============================================================================

const mockSecurityData = {
  totalSecurityComponents: 19,
  healthyComponents: 19,
  errorComponents: 0,
  filteredCount: 19,
  page: 1,
  limit: 50,
  totalPages: 1,
  metrics: {
    memoryUsage: 45.2,
    bufferUtilization: 62.8,
    threatLevel: 'low' as const,
    activeProtections: 19,
    lastSecurityScan: new Date().toISOString(),
  },
  components: [
    {
      id: 'memory-safe-resource-manager',
      name: 'MemorySafeResourceManager',
      status: 'healthy',
      category: 'memorySafety',
      healthScore: 100,
      lastUpdate: new Date().toISOString(),
      securityType: 'memory-management' as const,
    },
  ],
  query: {},
};

// ============================================================================
// TEST SUITE
// ============================================================================

describe('SecurityDashboardPage', () => {
  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();

    // Setup default mock implementations
    const { useSecurityData } = require('@/hooks/useSecurityData');
    const { useSecurityOperations } = require('@/hooks/useSecurityOperations');
    const { useSecurityAlerts } = require('@/hooks/useSecurityAlerts');

    useSecurityData.mockReturnValue({
      data: mockSecurityData,
      loading: false,
      error: null,
      lastUpdate: new Date(),
      refresh: jest.fn(),
      isRefreshing: false,
    });

    useSecurityOperations.mockReturnValue({
      operationStatus: 'idle',
      operationResult: null,
      operationError: null,
      operationHistory: [],
      runMemoryScan: jest.fn(),
      runBufferAnalysis: jest.fn(),
      runSecurityAudit: jest.fn(),
      isOperationRunning: false,
    });

    useSecurityAlerts.mockReturnValue({
      alerts: [],
      unacknowledgedCount: 0,
      criticalCount: 0,
      warningCount: 0,
      acknowledgeAlert: jest.fn(),
      acknowledgeAll: jest.fn(),
      clearAlert: jest.fn(),
      clearAll: jest.fn(),
    });
  });

  // ============================================================================
  // RENDERING TESTS
  // ============================================================================

  describe('Rendering', () => {
    it('should render security dashboard with all components', async () => {
      render(<SecurityDashboardPage />);

      await waitFor(() => {
        expect(screen.getByTestId('security-overview-panel')).toBeInTheDocument();
        expect(screen.getByTestId('memory-usage-monitor')).toBeInTheDocument();
        expect(screen.getByTestId('buffer-utilization-chart')).toBeInTheDocument();
        expect(screen.getByTestId('threat-level-indicator')).toBeInTheDocument();
        expect(screen.getByTestId('component-status-grid')).toBeInTheDocument();
        expect(screen.getByTestId('security-operations-panel')).toBeInTheDocument();
        expect(screen.getByTestId('operation-results-display')).toBeInTheDocument();
        expect(screen.getByTestId('alert-history-panel')).toBeInTheDocument();
      });
    });

    it('should display loading state', () => {
      const { useSecurityData } = require('@/hooks/useSecurityData');
      useSecurityData.mockReturnValue({
        data: null,
        loading: true,
        error: null,
        lastUpdate: null,
        refresh: jest.fn(),
        isRefreshing: false,
      });

      render(<SecurityDashboardPage />);

      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });

    it('should display error state', () => {
      const { useSecurityData } = require('@/hooks/useSecurityData');
      useSecurityData.mockReturnValue({
        data: null,
        loading: false,
        error: 'Failed to fetch security data',
        lastUpdate: null,
        refresh: jest.fn(),
        isRefreshing: false,
      });

      render(<SecurityDashboardPage />);

      expect(screen.getByText(/error/i)).toBeInTheDocument();
      expect(screen.getByText(/failed to fetch security data/i)).toBeInTheDocument();
    });
  });

  // ============================================================================
  // DATA FETCHING TESTS
  // ============================================================================

  describe('Data Fetching', () => {
    it('should fetch security data on mount', () => {
      const { useSecurityData } = require('@/hooks/useSecurityData');
      const mockRefresh = jest.fn();
      useSecurityData.mockReturnValue({
        data: mockSecurityData,
        loading: false,
        error: null,
        lastUpdate: new Date(),
        refresh: mockRefresh,
        isRefreshing: false,
      });

      render(<SecurityDashboardPage />);

      expect(useSecurityData).toHaveBeenCalled();
    });

    it('should handle refresh with custom interval', () => {
      const { useSecurityData } = require('@/hooks/useSecurityData');
      
      render(<SecurityDashboardPage />);

      // Verify default refresh interval (30000ms)
      expect(useSecurityData).toHaveBeenCalledWith(30000, true);
    });
  });

  // ============================================================================
  // INTERACTION TESTS
  // ============================================================================

  describe('User Interactions', () => {
    it('should handle manual refresh', async () => {
      const mockRefresh = jest.fn();
      const { useSecurityData } = require('@/hooks/useSecurityData');
      useSecurityData.mockReturnValue({
        data: mockSecurityData,
        loading: false,
        error: null,
        lastUpdate: new Date(),
        refresh: mockRefresh,
        isRefreshing: false,
      });

      render(<SecurityDashboardPage />);

      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      fireEvent.click(refreshButton);

      await waitFor(() => {
        expect(mockRefresh).toHaveBeenCalled();
      });
    });
  });

  // ============================================================================
  // ALERT TESTS
  // ============================================================================

  describe('Alert Management', () => {
    it('should display alerts when present', () => {
      const { useSecurityAlerts } = require('@/hooks/useSecurityAlerts');
      useSecurityAlerts.mockReturnValue({
        alerts: [
          {
            id: '1',
            type: 'warning',
            message: 'High memory usage detected',
            timestamp: new Date(),
          },
        ],
        unacknowledgedCount: 1,
        criticalCount: 0,
        warningCount: 1,
        acknowledgeAlert: jest.fn(),
        acknowledgeAll: jest.fn(),
        clearAlert: jest.fn(),
        clearAll: jest.fn(),
      });

      render(<SecurityDashboardPage />);

      expect(screen.getByTestId('alert-notification')).toBeInTheDocument();
    });
  });
});

