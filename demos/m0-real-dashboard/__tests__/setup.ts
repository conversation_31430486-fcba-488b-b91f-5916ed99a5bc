/**
 * @file Jest Test Setup
 * @filepath demos/m0-real-dashboard/__tests__/setup.ts
 * @authority President & CEO, E.Z. Consultancy
 * @purpose Configure Jest test environment for M0 Real Dashboard integration tests
 */

// Increase test timeout for integration tests
jest.setTimeout(60000);

// Mock console methods to reduce noise during tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};

// Add custom matchers if needed
expect.extend({
  toBeWithinRange(received: number, floor: number, ceiling: number) {
    const pass = received >= floor && received <= ceiling;
    if (pass) {
      return {
        message: () =>
          `expected ${received} not to be within range ${floor} - ${ceiling}`,
        pass: true,
      };
    } else {
      return {
        message: () =>
          `expected ${received} to be within range ${floor} - ${ceiling}`,
        pass: false,
      };
    }
  },
});

// Setup cleanup
afterEach(async () => {
  // Clear all timers after each test to prevent leaks
  jest.clearAllTimers();

  // Small delay to allow async operations to complete
  await new Promise(resolve => setTimeout(resolve, 10));
});

afterAll(async () => {
  // Clear all timers
  jest.clearAllTimers();

  // Force garbage collection after all tests
  if (global.gc) {
    global.gc();
  }

  // Final delay to allow cleanup
  await new Promise(resolve => setTimeout(resolve, 100));
});

