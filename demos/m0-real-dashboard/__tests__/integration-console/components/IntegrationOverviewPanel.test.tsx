/**
 * ============================================================================
 * INTEGRATION OVERVIEW PANEL - COMPONENT TESTS
 * ============================================================================
 *
 * Test suite for IntegrationOverviewPanel component
 *
 * Author: AI Assistant (Phase 3D Implementation)
 * Created: 2025-10-22
 * Authority: President & CEO, E.Z. Consultancy
 * ============================================================================
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { IntegrationOverviewPanel } from '../../../src/components/integration/IntegrationOverviewPanel';

// ============================================================================
// MOCK DATA
// ============================================================================

const mockData = {
  totalIntegrationComponents: 15,
  healthyComponents: 12,
  errorComponents: 1,
  filteredCount: 15,
  page: 1,
  limit: 50,
  totalPages: 1,
  metrics: {
    activeBridges: 5,
    messagesThroughput: 5000,
    integrationHealth: 80,
    crossComponentCalls: 1500,
    lastIntegrationTest: new Date().toISOString(),
  },
  components: [],
  query: {},
};

// ============================================================================
// RENDERING TESTS
// ============================================================================

describe('IntegrationOverviewPanel - Rendering', () => {
  test('renders component title', () => {
    render(<IntegrationOverviewPanel data={mockData} />);
    expect(screen.getByText('Integration Overview')).toBeInTheDocument();
  });

  test('displays total components count', () => {
    render(<IntegrationOverviewPanel data={mockData} />);
    expect(screen.getByText('15')).toBeInTheDocument();
  });

  test('displays healthy components count', () => {
    render(<IntegrationOverviewPanel data={mockData} />);
    expect(screen.getByText('12')).toBeInTheDocument();
  });

  test('displays error components count', () => {
    render(<IntegrationOverviewPanel data={mockData} />);
    expect(screen.getByText('1')).toBeInTheDocument();
  });

  test('displays health percentage', () => {
    render(<IntegrationOverviewPanel data={mockData} />);
    expect(screen.getByText('80%')).toBeInTheDocument();
  });
});

// ============================================================================
// METRICS TESTS
// ============================================================================

describe('IntegrationOverviewPanel - Metrics', () => {
  test('displays active bridges count', () => {
    render(<IntegrationOverviewPanel data={mockData} />);
    expect(screen.getByText('5')).toBeInTheDocument();
  });

  test('displays messages throughput', () => {
    render(<IntegrationOverviewPanel data={mockData} />);
    expect(screen.getByText(/5.0K\/min/i)).toBeInTheDocument();
  });

  test('displays cross-component calls', () => {
    render(<IntegrationOverviewPanel data={mockData} />);
    expect(screen.getByText('1,500')).toBeInTheDocument();
  });

  test('displays integration health score', () => {
    render(<IntegrationOverviewPanel data={mockData} />);
    const healthScores = screen.getAllByText('80%');
    expect(healthScores.length).toBeGreaterThan(0);
  });
});

// ============================================================================
// HEALTH STATUS TESTS
// ============================================================================

describe('IntegrationOverviewPanel - Health Status', () => {
  test('shows green color for healthy status (>=80%)', () => {
    const healthyData = { ...mockData, healthyComponents: 14 };
    render(<IntegrationOverviewPanel data={healthyData} />);
    // Health percentage should be ~93%
    expect(screen.getByText(/93%/i)).toBeInTheDocument();
  });

  test('shows yellow color for warning status (60-79%)', () => {
    const warningData = { ...mockData, healthyComponents: 10 };
    render(<IntegrationOverviewPanel data={warningData} />);
    // Health percentage should be ~67%
    expect(screen.getByText(/67%/i)).toBeInTheDocument();
  });

  test('shows red color for error status (<60%)', () => {
    const errorData = { ...mockData, healthyComponents: 5 };
    render(<IntegrationOverviewPanel data={errorData} />);
    // Health percentage should be ~33%
    expect(screen.getByText(/33%/i)).toBeInTheDocument();
  });
});

// ============================================================================
// EDGE CASES
// ============================================================================

describe('IntegrationOverviewPanel - Edge Cases', () => {
  test('handles zero components', () => {
    const emptyData = {
      ...mockData,
      totalIntegrationComponents: 0,
      healthyComponents: 0,
      errorComponents: 0,
    };
    render(<IntegrationOverviewPanel data={emptyData} />);
    expect(screen.getByText('0%')).toBeInTheDocument();
  });

  test('handles all healthy components', () => {
    const allHealthyData = {
      ...mockData,
      totalIntegrationComponents: 15,
      healthyComponents: 15,
      errorComponents: 0,
    };
    render(<IntegrationOverviewPanel data={allHealthyData} />);
    expect(screen.getByText('100%')).toBeInTheDocument();
  });

  test('handles all error components', () => {
    const allErrorData = {
      ...mockData,
      totalIntegrationComponents: 15,
      healthyComponents: 0,
      errorComponents: 15,
    };
    render(<IntegrationOverviewPanel data={allErrorData} />);
    expect(screen.getByText('0%')).toBeInTheDocument();
  });
});

