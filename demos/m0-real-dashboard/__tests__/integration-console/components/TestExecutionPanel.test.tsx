/**
 * ============================================================================
 * TEST EXECUTION PANEL - COMPONENT TESTS
 * ============================================================================
 *
 * Test suite for TestExecutionPanel component
 *
 * Author: AI Assistant (Phase 3D Implementation)
 * Created: 2025-10-22
 * Authority: President & CEO, E.Z. Consultancy
 * ============================================================================
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { TestExecutionPanel } from '../../../src/components/integration/TestExecutionPanel';

// ============================================================================
// MOCK HANDLER
// ============================================================================

const mockOnExecute = jest.fn().mockResolvedValue(undefined);

beforeEach(() => {
  mockOnExecute.mockClear();
});

// ============================================================================
// RENDERING TESTS
// ============================================================================

describe('TestExecutionPanel - Rendering', () => {
  test('renders component title', () => {
    render(
      <TestExecutionPanel
        onExecute={mockOnExecute}
        operationStatus="idle"
      />
    );
    expect(screen.getByText('Integration Operations')).toBeInTheDocument();
  });

  test('renders component description', () => {
    render(
      <TestExecutionPanel
        onExecute={mockOnExecute}
        operationStatus="idle"
      />
    );
    expect(screen.getByText(/Execute integration tests and health checks/i)).toBeInTheDocument();
  });

  test('renders all three operation buttons', () => {
    render(
      <TestExecutionPanel
        onExecute={mockOnExecute}
        operationStatus="idle"
      />
    );
    expect(screen.getByRole('button', { name: /Execute Bridge Test/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Execute Coordination Check/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Execute Integration Health/i })).toBeInTheDocument();
  });

  test('renders status indicator', () => {
    render(
      <TestExecutionPanel
        onExecute={mockOnExecute}
        operationStatus="idle"
      />
    );
    expect(screen.getByText(/Status:/i)).toBeInTheDocument();
  });
});

// ============================================================================
// STATUS TESTS
// ============================================================================

describe('TestExecutionPanel - Status', () => {
  test('displays idle status', () => {
    render(
      <TestExecutionPanel
        onExecute={mockOnExecute}
        operationStatus="idle"
      />
    );
    expect(screen.getByText(/Ready/i)).toBeInTheDocument();
  });

  test('displays running status', () => {
    render(
      <TestExecutionPanel
        onExecute={mockOnExecute}
        operationStatus="running"
        currentOperation="bridge-test"
      />
    );
    expect(screen.getByText(/Running.../i)).toBeInTheDocument();
  });

  test('displays success status', () => {
    render(
      <TestExecutionPanel
        onExecute={mockOnExecute}
        operationStatus="success"
      />
    );
    expect(screen.getByText(/Success/i)).toBeInTheDocument();
  });

  test('displays error status', () => {
    render(
      <TestExecutionPanel
        onExecute={mockOnExecute}
        operationStatus="error"
      />
    );
    expect(screen.getByText(/Error/i)).toBeInTheDocument();
  });
});

// ============================================================================
// USER INTERACTION TESTS
// ============================================================================

describe('TestExecutionPanel - User Interaction', () => {
  test('clicking bridge test button calls onExecute', () => {
    render(
      <TestExecutionPanel
        onExecute={mockOnExecute}
        operationStatus="idle"
      />
    );

    const bridgeButton = screen.getByRole('button', { name: /Execute Bridge Test/i });
    fireEvent.click(bridgeButton);

    expect(mockOnExecute).toHaveBeenCalledWith('bridge-test');
  });

  test('clicking coordination check button calls onExecute', () => {
    render(
      <TestExecutionPanel
        onExecute={mockOnExecute}
        operationStatus="idle"
      />
    );

    const coordButton = screen.getByRole('button', { name: /Execute Coordination Check/i });
    fireEvent.click(coordButton);

    expect(mockOnExecute).toHaveBeenCalledWith('coordination-check');
  });

  test('clicking integration health button calls onExecute', () => {
    render(
      <TestExecutionPanel
        onExecute={mockOnExecute}
        operationStatus="idle"
      />
    );

    const healthButton = screen.getByRole('button', { name: /Execute Integration Health/i });
    fireEvent.click(healthButton);

    expect(mockOnExecute).toHaveBeenCalledWith('integration-health');
  });
});

// ============================================================================
// DISABLED STATE TESTS
// ============================================================================

describe('TestExecutionPanel - Disabled States', () => {
  test('all buttons are disabled when operation is running', () => {
    render(
      <TestExecutionPanel
        onExecute={mockOnExecute}
        operationStatus="running"
        currentOperation="bridge-test"
      />
    );

    const buttons = screen.getAllByRole('button');
    const operationButtons = buttons.filter(btn => 
      btn.textContent?.includes('Execute') || btn.textContent?.includes('Running')
    );

    operationButtons.forEach(button => {
      expect(button).toBeDisabled();
    });
  });

  test('buttons are enabled when status is idle', () => {
    render(
      <TestExecutionPanel
        onExecute={mockOnExecute}
        operationStatus="idle"
      />
    );

    const bridgeButton = screen.getByRole('button', { name: /Execute Bridge Test/i });
    const coordButton = screen.getByRole('button', { name: /Execute Coordination Check/i });
    const healthButton = screen.getByRole('button', { name: /Execute Integration Health/i });

    expect(bridgeButton).not.toBeDisabled();
    expect(coordButton).not.toBeDisabled();
    expect(healthButton).not.toBeDisabled();
  });

  test('buttons are enabled after successful operation', () => {
    render(
      <TestExecutionPanel
        onExecute={mockOnExecute}
        operationStatus="success"
      />
    );

    const bridgeButton = screen.getByRole('button', { name: /Execute Bridge Test/i });
    expect(bridgeButton).not.toBeDisabled();
  });
});

// ============================================================================
// OPERATION DESCRIPTIONS
// ============================================================================

describe('TestExecutionPanel - Operation Descriptions', () => {
  test('displays bridge test description', () => {
    render(
      <TestExecutionPanel
        onExecute={mockOnExecute}
        operationStatus="idle"
      />
    );
    expect(screen.getByText(/Test bridge connectivity and message flow/i)).toBeInTheDocument();
  });

  test('displays coordination check description', () => {
    render(
      <TestExecutionPanel
        onExecute={mockOnExecute}
        operationStatus="idle"
      />
    );
    expect(screen.getByText(/Check component coordination and synchronization/i)).toBeInTheDocument();
  });

  test('displays integration health description', () => {
    render(
      <TestExecutionPanel
        onExecute={mockOnExecute}
        operationStatus="idle"
      />
    );
    expect(screen.getByText(/Analyze overall integration health/i)).toBeInTheDocument();
  });
});

