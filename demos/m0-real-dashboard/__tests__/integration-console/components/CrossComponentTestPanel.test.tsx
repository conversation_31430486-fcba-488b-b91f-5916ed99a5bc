/**
 * ============================================================================
 * CROSS-COMPONENT TEST PANEL - COMPONENT TESTS
 * ============================================================================
 *
 * Test suite for CrossComponentTestPanel component
 *
 * Author: AI Assistant (Phase 3D Implementation)
 * Created: 2025-10-22
 * Authority: President & CEO, E<PERSON>Z. Consultancy
 * ============================================================================
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { CrossComponentTestPanel } from '../../../src/components/integration/CrossComponentTestPanel';

// ============================================================================
// RENDERING TESTS
// ============================================================================

describe('CrossComponentTestPanel - Rendering', () => {
  test('renders component title', () => {
    render(<CrossComponentTestPanel />);
    expect(screen.getByText('Cross-Component Tests')).toBeInTheDocument();
  });

  test('renders component description', () => {
    render(<CrossComponentTestPanel />);
    expect(screen.getByText(/Execute integration tests between components/i)).toBeInTheDocument();
  });

  test('renders all test scenarios', () => {
    render(<CrossComponentTestPanel />);
    expect(screen.getByText('Bridge Message Flow')).toBeInTheDocument();
    expect(screen.getByText('Data Synchronization')).toBeInTheDocument();
    expect(screen.getByText('Event Propagation')).toBeInTheDocument();
    expect(screen.getByText('Dependency Check')).toBeInTheDocument();
  });

  test('renders run test buttons for each scenario', () => {
    render(<CrossComponentTestPanel />);
    const runButtons = screen.getAllByRole('button', { name: /Run Test/i });
    expect(runButtons).toHaveLength(4);
  });
});

// ============================================================================
// USER INTERACTION TESTS
// ============================================================================

describe('CrossComponentTestPanel - User Interaction', () => {
  test('clicking run test button executes test', async () => {
    render(<CrossComponentTestPanel />);

    const runButtons = screen.getAllByRole('button', { name: /Run Test/i });
    fireEvent.click(runButtons[0]);

    await waitFor(() => {
      expect(screen.getByText(/Running Test.../i)).toBeInTheDocument();
    });
  });

  test('test shows success state after completion', async () => {
    render(<CrossComponentTestPanel />);

    const runButtons = screen.getAllByRole('button', { name: /Run Test/i });
    fireEvent.click(runButtons[0]);

    await waitFor(() => {
      expect(screen.getByText(/Test completed successfully/i)).toBeInTheDocument();
    }, { timeout: 3000 });
  });

  test('button is disabled while test is running', async () => {
    render(<CrossComponentTestPanel />);

    const runButtons = screen.getAllByRole('button', { name: /Run Test/i });
    fireEvent.click(runButtons[0]);

    await waitFor(() => {
      expect(runButtons[0]).toBeDisabled();
    });
  });
});

// ============================================================================
// TEST SCENARIOS
// ============================================================================

describe('CrossComponentTestPanel - Test Scenarios', () => {
  test('displays test type badges', () => {
    render(<CrossComponentTestPanel />);
    expect(screen.getByText('message flow')).toBeInTheDocument();
    expect(screen.getByText('data sync')).toBeInTheDocument();
    expect(screen.getByText('event propagation')).toBeInTheDocument();
    expect(screen.getByText('dependency check')).toBeInTheDocument();
  });

  test('displays component flow for each test', () => {
    render(<CrossComponentTestPanel />);
    expect(screen.getByText('IntegrationBridge')).toBeInTheDocument();
    expect(screen.getByText('MessageCoordinator')).toBeInTheDocument();
    expect(screen.getByText('DataSyncManager')).toBeInTheDocument();
    expect(screen.getByText('IntegrationMonitor')).toBeInTheDocument();
  });
});

// ============================================================================
// SUMMARY TESTS
// ============================================================================

describe('CrossComponentTestPanel - Summary', () => {
  test('displays test summary section', () => {
    render(<CrossComponentTestPanel />);
    expect(screen.getByText('Passed')).toBeInTheDocument();
    expect(screen.getByText('Failed')).toBeInTheDocument();
    expect(screen.getByText('Total')).toBeInTheDocument();
  });

  test('shows correct total count', () => {
    render(<CrossComponentTestPanel />);
    expect(screen.getByText('4')).toBeInTheDocument(); // Total tests
  });

  test('updates passed count after successful test', async () => {
    render(<CrossComponentTestPanel />);

    const runButtons = screen.getAllByRole('button', { name: /Run Test/i });
    fireEvent.click(runButtons[0]);

    await waitFor(() => {
      const passedElements = screen.getAllByText('1');
      expect(passedElements.length).toBeGreaterThan(0);
    }, { timeout: 3000 });
  });
});

// ============================================================================
// CUSTOM HANDLER TESTS
// ============================================================================

describe('CrossComponentTestPanel - Custom Handler', () => {
  test('calls custom onTestExecute handler when provided', async () => {
    const mockHandler = jest.fn().mockResolvedValue(undefined);
    render(<CrossComponentTestPanel onTestExecute={mockHandler} />);

    const runButtons = screen.getAllByRole('button', { name: /Run Test/i });
    fireEvent.click(runButtons[0]);

    await waitFor(() => {
      expect(mockHandler).toHaveBeenCalledWith('test-1');
    });
  });

  test('handles custom handler errors gracefully', async () => {
    const mockHandler = jest.fn().mockRejectedValue(new Error('Test failed'));
    render(<CrossComponentTestPanel onTestExecute={mockHandler} />);

    const runButtons = screen.getAllByRole('button', { name: /Run Test/i });
    fireEvent.click(runButtons[0]);

    await waitFor(() => {
      expect(screen.getByText(/Test failed/i)).toBeInTheDocument();
    }, { timeout: 3000 });
  });
});

