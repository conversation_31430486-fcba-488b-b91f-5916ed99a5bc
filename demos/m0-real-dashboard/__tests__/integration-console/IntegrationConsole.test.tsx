/**
 * ============================================================================
 * INTEGRATION CONSOLE - MAIN PAGE TESTS
 * ============================================================================
 *
 * Comprehensive test suite for Integration Console page
 *
 * Test Categories:
 * - Rendering tests
 * - Data fetching tests
 * - User interaction tests
 * - Error handling tests
 * - Operations tests
 * - Responsive design tests
 * - Accessibility tests
 *
 * Author: AI Assistant (Phase 3D Implementation)
 * Created: 2025-10-22
 * Authority: President & CEO, E.Z. Consultancy
 * ============================================================================
 */

import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import IntegrationConsolePage from '../../src/app/integration-console/page';

// ============================================================================
// MOCK DATA
// ============================================================================

const mockIntegrationData = {
  success: true,
  data: {
    totalIntegrationComponents: 15,
    healthyComponents: 12,
    errorComponents: 1,
    filteredCount: 15,
    page: 1,
    limit: 50,
    totalPages: 1,
    metrics: {
      activeBridges: 5,
      messagesThroughput: 5000,
      integrationHealth: 80,
      crossComponentCalls: 1500,
      lastIntegrationTest: new Date().toISOString(),
    },
    components: [
      {
        id: 'int-001',
        name: 'IntegrationBridge',
        status: 'healthy',
        category: 'integration',
        healthScore: 95,
        lastUpdate: new Date().toISOString(),
        integrationType: 'bridge',
        responseTime: 25,
        metrics: {
          operationCount: 100,
          errorCount: 2,
          successRate: 98,
        },
        dependencies: [],
      },
      {
        id: 'int-002',
        name: 'MessageCoordinator',
        status: 'healthy',
        category: 'integration',
        healthScore: 88,
        lastUpdate: new Date().toISOString(),
        integrationType: 'coordinator',
        responseTime: 30,
        metrics: {
          operationCount: 150,
          errorCount: 5,
          successRate: 97,
        },
        dependencies: ['int-001'],
      },
    ],
    query: {},
  },
  timestamp: new Date().toISOString(),
};

// ============================================================================
// MOCK SETUP
// ============================================================================

global.fetch = jest.fn();

beforeEach(() => {
  (global.fetch as jest.Mock).mockResolvedValue({
    ok: true,
    json: async () => mockIntegrationData,
  });
});

afterEach(() => {
  jest.clearAllMocks();
});

// ============================================================================
// RENDERING TESTS
// ============================================================================

describe('IntegrationConsole - Rendering', () => {
  test('renders page title and description', async () => {
    render(<IntegrationConsolePage />);

    await waitFor(() => {
      expect(screen.getByText('Integration Console')).toBeInTheDocument();
    });

    expect(screen.getByText(/Monitor integration health/i)).toBeInTheDocument();
  });

  test('renders refresh button', async () => {
    render(<IntegrationConsolePage />);

    await waitFor(() => {
      expect(screen.getByRole('button', { name: /refresh/i })).toBeInTheDocument();
    });
  });

  test('renders all main sections', async () => {
    render(<IntegrationConsolePage />);

    await waitFor(() => {
      expect(screen.getByText('Integration Overview')).toBeInTheDocument();
      expect(screen.getByText('Cross-Component Tests')).toBeInTheDocument();
      expect(screen.getByText('Dependency Graph')).toBeInTheDocument();
      expect(screen.getByText('Integration Components')).toBeInTheDocument();
      expect(screen.getByText('Integration Operations')).toBeInTheDocument();
    });
  });
});

// ============================================================================
// DATA FETCHING TESTS
// ============================================================================

describe('IntegrationConsole - Data Fetching', () => {
  test('fetches integration data on mount', async () => {
    render(<IntegrationConsolePage />);

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/m0-integration'),
        expect.any(Object)
      );
    });
  });

  test('displays loading state initially', () => {
    render(<IntegrationConsolePage />);

    expect(screen.getByText(/Loading integration data/i)).toBeInTheDocument();
  });

  test('displays integration data after loading', async () => {
    render(<IntegrationConsolePage />);

    await waitFor(() => {
      expect(screen.getByText('15')).toBeInTheDocument(); // Total components
    });
  });

  test('handles API errors gracefully', async () => {
    (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('API Error'));

    render(<IntegrationConsolePage />);

    await waitFor(() => {
      expect(screen.getByText(/Error Loading Integration Data/i)).toBeInTheDocument();
    });
  });
});

// ============================================================================
// USER INTERACTION TESTS
// ============================================================================

describe('IntegrationConsole - User Interaction', () => {
  test('manual refresh button triggers data refetch', async () => {
    render(<IntegrationConsolePage />);

    await waitFor(() => {
      expect(screen.getByText('Integration Console')).toBeInTheDocument();
    });

    const refreshButton = screen.getByRole('button', { name: /refresh/i });
    fireEvent.click(refreshButton);

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledTimes(2); // Initial + manual refresh
    });
  });

  test('operation buttons are clickable', async () => {
    render(<IntegrationConsolePage />);

    await waitFor(() => {
      expect(screen.getByText('Integration Operations')).toBeInTheDocument();
    });

    const bridgeTestButton = screen.getByRole('button', { name: /Execute Bridge Test/i });
    expect(bridgeTestButton).toBeInTheDocument();
    expect(bridgeTestButton).not.toBeDisabled();
  });
});

// ============================================================================
// OPERATIONS TESTS
// ============================================================================

describe('IntegrationConsole - Operations', () => {
  test('displays operation status', async () => {
    render(<IntegrationConsolePage />);

    await waitFor(() => {
      expect(screen.getByText(/Status:/i)).toBeInTheDocument();
    });
  });

  test('shows all three operation types', async () => {
    render(<IntegrationConsolePage />);

    await waitFor(() => {
      expect(screen.getByText('Bridge Test')).toBeInTheDocument();
      expect(screen.getByText('Coordination Check')).toBeInTheDocument();
      expect(screen.getByText('Integration Health')).toBeInTheDocument();
    });
  });
});

// ============================================================================
// RESPONSIVE DESIGN TESTS
// ============================================================================

describe('IntegrationConsole - Responsive Design', () => {
  test('renders in mobile viewport', async () => {
    global.innerWidth = 375;
    global.innerHeight = 667;

    render(<IntegrationConsolePage />);

    await waitFor(() => {
      expect(screen.getByText('Integration Console')).toBeInTheDocument();
    });
  });

  test('renders in tablet viewport', async () => {
    global.innerWidth = 768;
    global.innerHeight = 1024;

    render(<IntegrationConsolePage />);

    await waitFor(() => {
      expect(screen.getByText('Integration Console')).toBeInTheDocument();
    });
  });

  test('renders in desktop viewport', async () => {
    global.innerWidth = 1920;
    global.innerHeight = 1080;

    render(<IntegrationConsolePage />);

    await waitFor(() => {
      expect(screen.getByText('Integration Console')).toBeInTheDocument();
    });
  });
});

// ============================================================================
// ACCESSIBILITY TESTS
// ============================================================================

describe('IntegrationConsole - Accessibility', () => {
  test('has accessible heading structure', async () => {
    render(<IntegrationConsolePage />);

    await waitFor(() => {
      const heading = screen.getByRole('heading', { name: /Integration Console/i });
      expect(heading).toBeInTheDocument();
    });
  });

  test('buttons have accessible labels', async () => {
    render(<IntegrationConsolePage />);

    await waitFor(() => {
      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      expect(refreshButton).toBeInTheDocument();
    });
  });

  test('supports keyboard navigation', async () => {
    render(<IntegrationConsolePage />);

    await waitFor(() => {
      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      refreshButton.focus();
      expect(refreshButton).toHaveFocus();
    });
  });
});

