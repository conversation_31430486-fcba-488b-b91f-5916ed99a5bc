/**
 * ============================================================================
 * TRACKING DASHBOARD - Main Page Tests
 * ============================================================================
 *
 * Comprehensive test suite for the tracking dashboard main page.
 *
 * Test Categories:
 * - Rendering
 * - Data fetching
 * - User interaction
 * - Error handling
 * - Operations
 * - Responsive design
 * - Accessibility
 *
 * Authority: President & CEO, E<PERSON>Z. Consultancy
 * Created: 2025-10-22
 * ============================================================================
 */

import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import TrackingDashboardPage from '../../src/app/tracking-dashboard/page';

// Mock fetch
global.fetch = jest.fn();

// Mock hooks
jest.mock('../../src/hooks/useTrackingData');
jest.mock('../../src/hooks/useTrackingOperations');
jest.mock('../../src/hooks/useTrackingAlerts');

import { useTrackingData } from '../../src/hooks/useTrackingData';
import { useTrackingOperations } from '../../src/hooks/useTrackingOperations';
import { useTrackingAlerts } from '../../src/hooks/useTrackingAlerts';

const mockUseTrackingData = useTrackingData as jest.MockedFunction<typeof useTrackingData>;
const mockUseTrackingOperations = useTrackingOperations as jest.MockedFunction<typeof useTrackingOperations>;
const mockUseTrackingAlerts = useTrackingAlerts as jest.MockedFunction<typeof useTrackingAlerts>;

describe('TrackingDashboardPage', () => {
  const mockTrackingData = {
    totalTrackingComponents: 33,
    healthyComponents: 28,
    errorComponents: 2,
    filteredCount: 33,
    page: 1,
    limit: 50,
    totalPages: 1,
    metrics: {
      activeSessions: 45,
      totalEvents: 12500,
      averageResponseTime: 125.5,
      dataProcessingRate: 5000,
      lastActivity: new Date().toISOString(),
    },
    components: [
      {
        id: 'session-tracker-1',
        name: 'Session Tracker',
        status: 'healthy' as const,
        category: 'tracking',
        healthScore: 95,
        lastUpdate: new Date().toISOString(),
        trackingType: 'session' as const,
        responseTime: 120,
      },
      {
        id: 'analytics-engine-1',
        name: 'Analytics Engine',
        status: 'warning' as const,
        category: 'tracking',
        healthScore: 75,
        lastUpdate: new Date().toISOString(),
        trackingType: 'analytics' as const,
        responseTime: 200,
      },
    ],
    query: {},
  };

  const mockOperationResult = {
    success: true,
    operation: 'session-analysis' as const,
    timestamp: new Date().toISOString(),
    duration: 1500,
    data: { activeSessions: 45 },
  };

  const mockAlerts = [
    {
      id: 'alert-1',
      severity: 'warning' as const,
      type: 'session' as const,
      message: 'High session count detected',
      timestamp: new Date().toISOString(),
      acknowledged: false,
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();

    mockUseTrackingData.mockReturnValue({
      data: mockTrackingData,
      loading: false,
      error: null,
      lastUpdate: new Date(),
      refresh: jest.fn(),
      isRefreshing: false,
      setQuery: jest.fn(),
    });

    mockUseTrackingOperations.mockReturnValue({
      operationStatus: 'idle',
      operationResult: null,
      runSessionAnalysis: jest.fn(),
      runComponentHealthCheck: jest.fn(),
      runEventTimeline: jest.fn(),
    });

    mockUseTrackingAlerts.mockReturnValue({
      alerts: mockAlerts,
      unacknowledgedCount: 1,
      acknowledgeAlert: jest.fn(),
      clearAlert: jest.fn(),
      clearAll: jest.fn(),
    });
  });

  describe('Rendering', () => {
    it('should render the dashboard header', () => {
      render(<TrackingDashboardPage />);
      expect(screen.getByText('Tracking Dashboard')).toBeInTheDocument();
      expect(screen.getByText(/Real-time tracking component monitoring/i)).toBeInTheDocument();
    });

    it('should render all main components', () => {
      render(<TrackingDashboardPage />);
      expect(screen.getByText('Tracking Overview')).toBeInTheDocument();
      expect(screen.getByText('Session Analytics')).toBeInTheDocument();
      expect(screen.getByText('Component Health Monitor')).toBeInTheDocument();
      expect(screen.getByText('Event Timeline')).toBeInTheDocument();
      expect(screen.getByText('Component Status Grid')).toBeInTheDocument();
      expect(screen.getByText('Tracking Operations')).toBeInTheDocument();
    });

    it('should display tracking metrics', () => {
      render(<TrackingDashboardPage />);
      expect(screen.getByText('33')).toBeInTheDocument(); // Total components
      expect(screen.getByText('28')).toBeInTheDocument(); // Healthy components
    });
  });

  describe('Data Fetching', () => {
    it('should display loading state', () => {
      mockUseTrackingData.mockReturnValue({
        data: null,
        loading: true,
        error: null,
        lastUpdate: new Date(),
        refresh: jest.fn(),
        isRefreshing: false,
        setQuery: jest.fn(),
      });

      render(<TrackingDashboardPage />);
      // Loading state should show skeleton or loading indicators
      expect(screen.queryByText('Tracking Overview')).toBeInTheDocument();
    });

    it('should display error state', () => {
      mockUseTrackingData.mockReturnValue({
        data: null,
        loading: false,
        error: 'Failed to fetch data',
        lastUpdate: new Date(),
        refresh: jest.fn(),
        isRefreshing: false,
        setQuery: jest.fn(),
      });

      render(<TrackingDashboardPage />);
      expect(screen.getByText('Error Loading Data')).toBeInTheDocument();
      expect(screen.getByText('Failed to fetch data')).toBeInTheDocument();
    });

    it('should call refresh when refresh button is clicked', async () => {
      const mockRefresh = jest.fn();
      mockUseTrackingData.mockReturnValue({
        data: mockTrackingData,
        loading: false,
        error: null,
        lastUpdate: new Date(),
        refresh: mockRefresh,
        isRefreshing: false,
        setQuery: jest.fn(),
      });

      render(<TrackingDashboardPage />);
      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      fireEvent.click(refreshButton);

      await waitFor(() => {
        expect(mockRefresh).toHaveBeenCalled();
      });
    });
  });

  describe('User Interaction', () => {
    it('should toggle alert visibility', () => {
      render(<TrackingDashboardPage />);
      const alertToggle = screen.getByLabelText('Toggle alerts');
      
      // Alerts should be visible initially
      expect(screen.getByText('High session count detected')).toBeInTheDocument();
      
      // Click to hide alerts
      fireEvent.click(alertToggle);
      
      // Alerts should be hidden
      expect(screen.queryByText('High session count detected')).not.toBeInTheDocument();
    });

    it('should display unacknowledged alert count badge', () => {
      render(<TrackingDashboardPage />);
      expect(screen.getByText('1')).toBeInTheDocument(); // Badge count
    });
  });

  describe('Operations', () => {
    it('should execute session analysis operation', async () => {
      const mockRunSessionAnalysis = jest.fn();
      mockUseTrackingOperations.mockReturnValue({
        operationStatus: 'idle',
        operationResult: null,
        runSessionAnalysis: mockRunSessionAnalysis,
        runComponentHealthCheck: jest.fn(),
        runEventTimeline: jest.fn(),
      });

      render(<TrackingDashboardPage />);
      const analysisButton = screen.getByRole('button', { name: /run analysis/i });
      fireEvent.click(analysisButton);

      await waitFor(() => {
        expect(mockRunSessionAnalysis).toHaveBeenCalled();
      });
    });

    it('should display operation results', () => {
      mockUseTrackingOperations.mockReturnValue({
        operationStatus: 'success',
        operationResult: mockOperationResult,
        runSessionAnalysis: jest.fn(),
        runComponentHealthCheck: jest.fn(),
        runEventTimeline: jest.fn(),
      });

      render(<TrackingDashboardPage />);
      expect(screen.getByText('Operation Results')).toBeInTheDocument();
      expect(screen.getByText('Operation Successful')).toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    it('should render with responsive grid layout', () => {
      const { container } = render(<TrackingDashboardPage />);
      const gridElements = container.querySelectorAll('.grid');
      expect(gridElements.length).toBeGreaterThan(0);
    });
  });

  describe('Accessibility', () => {
    it('should have accessible navigation link', () => {
      render(<TrackingDashboardPage />);
      const backLink = screen.getByRole('link');
      expect(backLink).toHaveAttribute('href', '/');
    });

    it('should have accessible buttons', () => {
      render(<TrackingDashboardPage />);
      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      expect(refreshButton).toBeInTheDocument();
    });

    it('should have aria-label for alert toggle', () => {
      render(<TrackingDashboardPage />);
      const alertToggle = screen.getByLabelText('Toggle alerts');
      expect(alertToggle).toBeInTheDocument();
    });
  });
});

