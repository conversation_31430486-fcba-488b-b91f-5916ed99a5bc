/**
 * ============================================================================
 * TRACKING OPERATIONS PANEL - Component Tests
 * ============================================================================
 *
 * Test suite for TrackingOperationsPanel component.
 *
 * Test Categories:
 * - Rendering
 * - Operation buttons
 * - Status indicators
 * - User interaction
 * - Disabled states
 *
 * Authority: President & CEO, E.Z. Consultancy
 * Created: 2025-10-22
 * ============================================================================
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { TrackingOperationsPanel } from '../../../src/components/tracking/TrackingOperationsPanel';
import type { TOperationStatus } from '../../../src/types/tracking-types';

describe('TrackingOperationsPanel', () => {
  const mockOnRunSessionAnalysis = jest.fn();
  const mockOnRunComponentHealthCheck = jest.fn();
  const mockOnRunEventTimeline = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render the component', () => {
      render(
        <TrackingOperationsPanel
          onRunSessionAnalysis={mockOnRunSessionAnalysis}
          onRunComponentHealthCheck={mockOnRunComponentHealthCheck}
          onRunEventTimeline={mockOnRunEventTimeline}
          operationStatus="idle"
        />
      );
      expect(screen.getByText('Tracking Operations')).toBeInTheDocument();
    });

    it('should render all three operation sections', () => {
      render(
        <TrackingOperationsPanel
          onRunSessionAnalysis={mockOnRunSessionAnalysis}
          onRunComponentHealthCheck={mockOnRunComponentHealthCheck}
          onRunEventTimeline={mockOnRunEventTimeline}
          operationStatus="idle"
        />
      );
      expect(screen.getByText('Session Analysis')).toBeInTheDocument();
      expect(screen.getByText('Component Health Check')).toBeInTheDocument();
      expect(screen.getByText('Event Timeline')).toBeInTheDocument();
    });

    it('should display operation descriptions', () => {
      render(
        <TrackingOperationsPanel
          onRunSessionAnalysis={mockOnRunSessionAnalysis}
          onRunComponentHealthCheck={mockOnRunComponentHealthCheck}
          onRunEventTimeline={mockOnRunEventTimeline}
          operationStatus="idle"
        />
      );
      expect(screen.getByText('Analyze active sessions and session metrics')).toBeInTheDocument();
      expect(screen.getByText('Check health status of tracking components')).toBeInTheDocument();
      expect(screen.getByText('Generate timeline of tracking events')).toBeInTheDocument();
    });
  });

  describe('Operation Buttons', () => {
    it('should render all operation buttons', () => {
      render(
        <TrackingOperationsPanel
          onRunSessionAnalysis={mockOnRunSessionAnalysis}
          onRunComponentHealthCheck={mockOnRunComponentHealthCheck}
          onRunEventTimeline={mockOnRunEventTimeline}
          operationStatus="idle"
        />
      );
      expect(screen.getByRole('button', { name: /run analysis/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /run check/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /generate timeline/i })).toBeInTheDocument();
    });

    it('should call onRunSessionAnalysis when session analysis button is clicked', () => {
      render(
        <TrackingOperationsPanel
          onRunSessionAnalysis={mockOnRunSessionAnalysis}
          onRunComponentHealthCheck={mockOnRunComponentHealthCheck}
          onRunEventTimeline={mockOnRunEventTimeline}
          operationStatus="idle"
        />
      );
      const button = screen.getByRole('button', { name: /run analysis/i });
      fireEvent.click(button);
      expect(mockOnRunSessionAnalysis).toHaveBeenCalledTimes(1);
    });

    it('should call onRunComponentHealthCheck when health check button is clicked', () => {
      render(
        <TrackingOperationsPanel
          onRunSessionAnalysis={mockOnRunSessionAnalysis}
          onRunComponentHealthCheck={mockOnRunComponentHealthCheck}
          onRunEventTimeline={mockOnRunEventTimeline}
          operationStatus="idle"
        />
      );
      const button = screen.getByRole('button', { name: /run check/i });
      fireEvent.click(button);
      expect(mockOnRunComponentHealthCheck).toHaveBeenCalledTimes(1);
    });

    it('should call onRunEventTimeline when timeline button is clicked', () => {
      render(
        <TrackingOperationsPanel
          onRunSessionAnalysis={mockOnRunSessionAnalysis}
          onRunComponentHealthCheck={mockOnRunComponentHealthCheck}
          onRunEventTimeline={mockOnRunEventTimeline}
          operationStatus="idle"
        />
      );
      const button = screen.getByRole('button', { name: /generate timeline/i });
      fireEvent.click(button);
      expect(mockOnRunEventTimeline).toHaveBeenCalledTimes(1);
    });
  });

  describe('Status Indicators', () => {
    it('should not show status indicator when idle', () => {
      render(
        <TrackingOperationsPanel
          onRunSessionAnalysis={mockOnRunSessionAnalysis}
          onRunComponentHealthCheck={mockOnRunComponentHealthCheck}
          onRunEventTimeline={mockOnRunEventTimeline}
          operationStatus="idle"
        />
      );
      expect(screen.queryByText('Operation in progress...')).not.toBeInTheDocument();
    });

    it('should show running status indicator', () => {
      render(
        <TrackingOperationsPanel
          onRunSessionAnalysis={mockOnRunSessionAnalysis}
          onRunComponentHealthCheck={mockOnRunComponentHealthCheck}
          onRunEventTimeline={mockOnRunEventTimeline}
          operationStatus="running"
        />
      );
      expect(screen.getByText('Operation in progress...')).toBeInTheDocument();
    });

    it('should show success status indicator', () => {
      render(
        <TrackingOperationsPanel
          onRunSessionAnalysis={mockOnRunSessionAnalysis}
          onRunComponentHealthCheck={mockOnRunComponentHealthCheck}
          onRunEventTimeline={mockOnRunEventTimeline}
          operationStatus="success"
        />
      );
      expect(screen.getByText('Operation completed successfully')).toBeInTheDocument();
    });

    it('should show error status indicator', () => {
      render(
        <TrackingOperationsPanel
          onRunSessionAnalysis={mockOnRunSessionAnalysis}
          onRunComponentHealthCheck={mockOnRunComponentHealthCheck}
          onRunEventTimeline={mockOnRunEventTimeline}
          operationStatus="error"
        />
      );
      expect(screen.getByText('Operation failed')).toBeInTheDocument();
    });
  });

  describe('Disabled States', () => {
    it('should disable all buttons when operation is running', () => {
      render(
        <TrackingOperationsPanel
          onRunSessionAnalysis={mockOnRunSessionAnalysis}
          onRunComponentHealthCheck={mockOnRunComponentHealthCheck}
          onRunEventTimeline={mockOnRunEventTimeline}
          operationStatus="running"
        />
      );
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toBeDisabled();
      });
    });

    it('should enable all buttons when operation is idle', () => {
      render(
        <TrackingOperationsPanel
          onRunSessionAnalysis={mockOnRunSessionAnalysis}
          onRunComponentHealthCheck={mockOnRunComponentHealthCheck}
          onRunEventTimeline={mockOnRunEventTimeline}
          operationStatus="idle"
        />
      );
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).not.toBeDisabled();
      });
    });

    it('should show "Running..." text on buttons when operation is running', () => {
      render(
        <TrackingOperationsPanel
          onRunSessionAnalysis={mockOnRunSessionAnalysis}
          onRunComponentHealthCheck={mockOnRunComponentHealthCheck}
          onRunEventTimeline={mockOnRunEventTimeline}
          operationStatus="running"
        />
      );
      const runningButtons = screen.getAllByText('Running...');
      expect(runningButtons.length).toBe(3);
    });
  });

  describe('Responsive Behavior', () => {
    it('should render with custom className', () => {
      const { container } = render(
        <TrackingOperationsPanel
          onRunSessionAnalysis={mockOnRunSessionAnalysis}
          onRunComponentHealthCheck={mockOnRunComponentHealthCheck}
          onRunEventTimeline={mockOnRunEventTimeline}
          operationStatus="idle"
          className="custom-class"
        />
      );
      const component = container.firstChild as HTMLElement;
      expect(component).toHaveClass('custom-class');
    });

    it('should have proper button styling', () => {
      const { container } = render(
        <TrackingOperationsPanel
          onRunSessionAnalysis={mockOnRunSessionAnalysis}
          onRunComponentHealthCheck={mockOnRunComponentHealthCheck}
          onRunEventTimeline={mockOnRunEventTimeline}
          operationStatus="idle"
        />
      );
      const buttons = container.querySelectorAll('button');
      buttons.forEach(button => {
        expect(button).toHaveClass('rounded-lg');
      });
    });
  });
});

