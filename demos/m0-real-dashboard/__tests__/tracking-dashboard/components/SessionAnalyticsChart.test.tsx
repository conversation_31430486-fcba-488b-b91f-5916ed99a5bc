/**
 * ============================================================================
 * SESSION ANALYTICS CHART - Component Tests
 * ============================================================================
 *
 * Test suite for SessionAnalyticsChart component.
 *
 * Test Categories:
 * - Rendering
 * - Chart data
 * - Metrics display
 * - Responsive behavior
 *
 * Authority: President & CEO, E.Z. Consultancy
 * Created: 2025-10-22
 * ============================================================================
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { SessionAnalyticsChart } from '../../../src/components/tracking/SessionAnalyticsChart';
import type { ITrackingMetrics } from '../../../src/types/tracking-types';

describe('SessionAnalyticsChart', () => {
  const mockMetrics: ITrackingMetrics = {
    activeSessions: 45,
    totalEvents: 12500,
    averageResponseTime: 125.5,
    dataProcessingRate: 5000,
    lastActivity: new Date().toISOString(),
  };

  describe('Rendering', () => {
    it('should render the component', () => {
      render(<SessionAnalyticsChart metrics={mockMetrics} />);
      expect(screen.getByText('Session Analytics')).toBeInTheDocument();
    });

    it('should display time range label', () => {
      render(<SessionAnalyticsChart metrics={mockMetrics} />);
      expect(screen.getByText('Last 60 minutes')).toBeInTheDocument();
    });

    it('should render chart container', () => {
      const { container } = render(<SessionAnalyticsChart metrics={mockMetrics} />);
      const chartContainer = container.querySelector('.recharts-wrapper');
      expect(chartContainer).toBeInTheDocument();
    });
  });

  describe('Metrics Display', () => {
    it('should display active sessions metric', () => {
      render(<SessionAnalyticsChart metrics={mockMetrics} />);
      expect(screen.getByText('45')).toBeInTheDocument();
      expect(screen.getByText('Active Sessions')).toBeInTheDocument();
    });

    it('should display total events metric', () => {
      render(<SessionAnalyticsChart metrics={mockMetrics} />);
      expect(screen.getByText('12,500')).toBeInTheDocument();
      expect(screen.getByText('Total Events')).toBeInTheDocument();
    });

    it('should display processing rate metric', () => {
      render(<SessionAnalyticsChart metrics={mockMetrics} />);
      expect(screen.getByText('5,000/s')).toBeInTheDocument();
      expect(screen.getByText('Processing Rate')).toBeInTheDocument();
    });
  });

  describe('Chart Data', () => {
    it('should generate time-series data points', () => {
      const { container } = render(<SessionAnalyticsChart metrics={mockMetrics} />);
      const lines = container.querySelectorAll('.recharts-line');
      expect(lines.length).toBe(3); // 3 data series
    });

    it('should render legend', () => {
      const { container } = render(<SessionAnalyticsChart metrics={mockMetrics} />);
      const legend = container.querySelector('.recharts-legend-wrapper');
      expect(legend).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle zero metrics', () => {
      const zeroMetrics: ITrackingMetrics = {
        activeSessions: 0,
        totalEvents: 0,
        averageResponseTime: 0,
        dataProcessingRate: 0,
        lastActivity: new Date().toISOString(),
      };

      render(<SessionAnalyticsChart metrics={zeroMetrics} />);
      expect(screen.getByText('0')).toBeInTheDocument();
    });

    it('should handle large numbers', () => {
      const largeMetrics: ITrackingMetrics = {
        activeSessions: 1000,
        totalEvents: 1000000,
        averageResponseTime: 500,
        dataProcessingRate: 50000,
        lastActivity: new Date().toISOString(),
      };

      render(<SessionAnalyticsChart metrics={largeMetrics} />);
      expect(screen.getByText('1,000')).toBeInTheDocument();
      expect(screen.getByText('1,000,000')).toBeInTheDocument();
    });
  });

  describe('Responsive Behavior', () => {
    it('should render with custom className', () => {
      const { container } = render(<SessionAnalyticsChart metrics={mockMetrics} className="custom-class" />);
      const component = container.firstChild as HTMLElement;
      expect(component).toHaveClass('custom-class');
    });

    it('should have responsive container', () => {
      const { container } = render(<SessionAnalyticsChart metrics={mockMetrics} />);
      const responsiveContainer = container.querySelector('.recharts-responsive-container');
      expect(responsiveContainer).toBeInTheDocument();
    });
  });
});

