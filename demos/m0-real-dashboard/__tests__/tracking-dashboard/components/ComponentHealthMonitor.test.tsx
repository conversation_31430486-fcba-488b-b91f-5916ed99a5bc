/**
 * ============================================================================
 * COMPONENT HEALTH MONITOR - Component Tests
 * ============================================================================
 *
 * Test suite for ComponentHealthMonitor component.
 *
 * Test Categories:
 * - Rendering
 * - Component display
 * - Health scores
 * - Status indicators
 * - Edge cases
 *
 * Authority: President & CEO, E.Z. Consultancy
 * Created: 2025-10-22
 * ============================================================================
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ComponentHealthMonitor } from '../../../src/components/tracking/ComponentHealthMonitor';
import type { ITrackingComponent } from '../../../src/types/tracking-types';

describe('ComponentHealthMonitor', () => {
  const mockComponents: ITrackingComponent[] = [
    {
      id: 'session-tracker-1',
      name: 'Session Tracker',
      status: 'healthy',
      category: 'tracking',
      healthScore: 95,
      lastUpdate: new Date().toISOString(),
      trackingType: 'session',
      responseTime: 120,
      metrics: {
        operationCount: 1000,
        errorCount: 5,
        successRate: 99.5,
      },
    },
    {
      id: 'analytics-engine-1',
      name: 'Analytics Engine',
      status: 'warning',
      category: 'tracking',
      healthScore: 75,
      lastUpdate: new Date().toISOString(),
      trackingType: 'analytics',
      responseTime: 200,
      metrics: {
        operationCount: 500,
        errorCount: 10,
        successRate: 98.0,
      },
    },
    {
      id: 'data-processor-1',
      name: 'Data Processor',
      status: 'error',
      category: 'tracking',
      healthScore: 45,
      lastUpdate: new Date().toISOString(),
      trackingType: 'data-management',
      responseTime: 350,
      metrics: {
        operationCount: 200,
        errorCount: 50,
        successRate: 75.0,
      },
    },
  ];

  describe('Rendering', () => {
    it('should render the component', () => {
      render(<ComponentHealthMonitor components={mockComponents} />);
      expect(screen.getByText('Component Health Monitor')).toBeInTheDocument();
    });

    it('should display top 10 components label', () => {
      render(<ComponentHealthMonitor components={mockComponents} />);
      expect(screen.getByText('Top 10 Components')).toBeInTheDocument();
    });

    it('should render component cards', () => {
      render(<ComponentHealthMonitor components={mockComponents} />);
      expect(screen.getByText('Session Tracker')).toBeInTheDocument();
      expect(screen.getByText('Analytics Engine')).toBeInTheDocument();
      expect(screen.getByText('Data Processor')).toBeInTheDocument();
    });
  });

  describe('Component Display', () => {
    it('should display health scores', () => {
      render(<ComponentHealthMonitor components={mockComponents} />);
      expect(screen.getByText('95%')).toBeInTheDocument();
      expect(screen.getByText('75%')).toBeInTheDocument();
      expect(screen.getByText('45%')).toBeInTheDocument();
    });

    it('should display tracking type badges', () => {
      render(<ComponentHealthMonitor components={mockComponents} />);
      expect(screen.getByText('session')).toBeInTheDocument();
      expect(screen.getByText('analytics')).toBeInTheDocument();
      expect(screen.getByText('data-management')).toBeInTheDocument();
    });

    it('should display response times', () => {
      render(<ComponentHealthMonitor components={mockComponents} />);
      expect(screen.getByText('120ms')).toBeInTheDocument();
      expect(screen.getByText('200ms')).toBeInTheDocument();
      expect(screen.getByText('350ms')).toBeInTheDocument();
    });

    it('should display component metrics', () => {
      render(<ComponentHealthMonitor components={mockComponents} />);
      expect(screen.getByText('1,000')).toBeInTheDocument(); // Operations
      expect(screen.getByText('99.5%')).toBeInTheDocument(); // Success rate
    });
  });

  describe('Status Indicators', () => {
    it('should display healthy status icon', () => {
      const { container } = render(<ComponentHealthMonitor components={mockComponents} />);
      const healthyIcons = container.querySelectorAll('.text-green-600');
      expect(healthyIcons.length).toBeGreaterThan(0);
    });

    it('should display warning status icon', () => {
      const { container } = render(<ComponentHealthMonitor components={mockComponents} />);
      const warningIcons = container.querySelectorAll('.text-yellow-600');
      expect(warningIcons.length).toBeGreaterThan(0);
    });

    it('should display error status icon', () => {
      const { container } = render(<ComponentHealthMonitor components={mockComponents} />);
      const errorIcons = container.querySelectorAll('.text-red-600');
      expect(errorIcons.length).toBeGreaterThan(0);
    });
  });

  describe('Summary Statistics', () => {
    it('should display average health', () => {
      render(<ComponentHealthMonitor components={mockComponents} />);
      const avgHealth = Math.round((95 + 75 + 45) / 3);
      expect(screen.getByText(`${avgHealth}%`)).toBeInTheDocument();
    });

    it('should display healthy count', () => {
      render(<ComponentHealthMonitor components={mockComponents} />);
      expect(screen.getByText('Healthy')).toBeInTheDocument();
      expect(screen.getByText('1')).toBeInTheDocument(); // 1 healthy component
    });

    it('should display issues count', () => {
      render(<ComponentHealthMonitor components={mockComponents} />);
      expect(screen.getByText('Issues')).toBeInTheDocument();
      expect(screen.getByText('2')).toBeInTheDocument(); // 2 components with issues
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty components array', () => {
      render(<ComponentHealthMonitor components={[]} />);
      expect(screen.getByText('No components available')).toBeInTheDocument();
    });

    it('should limit to top 10 components', () => {
      const manyComponents = Array.from({ length: 20 }, (_, i) => ({
        id: `component-${i}`,
        name: `Component ${i}`,
        status: 'healthy' as const,
        category: 'tracking',
        healthScore: 90 - i,
        lastUpdate: new Date().toISOString(),
        trackingType: 'session' as const,
      }));

      const { container } = render(<ComponentHealthMonitor components={manyComponents} />);
      const componentCards = container.querySelectorAll('.bg-gray-50');
      expect(componentCards.length).toBe(10);
    });

    it('should handle components without metrics', () => {
      const componentsWithoutMetrics: ITrackingComponent[] = [
        {
          id: 'simple-component',
          name: 'Simple Component',
          status: 'healthy',
          category: 'tracking',
          healthScore: 90,
          lastUpdate: new Date().toISOString(),
          trackingType: 'session',
        },
      ];

      render(<ComponentHealthMonitor components={componentsWithoutMetrics} />);
      expect(screen.getByText('Simple Component')).toBeInTheDocument();
    });
  });

  describe('Responsive Behavior', () => {
    it('should render with custom className', () => {
      const { container } = render(<ComponentHealthMonitor components={mockComponents} className="custom-class" />);
      const component = container.firstChild as HTMLElement;
      expect(component).toHaveClass('custom-class');
    });

    it('should have hover effects on component cards', () => {
      const { container } = render(<ComponentHealthMonitor components={mockComponents} />);
      const cards = container.querySelectorAll('.hover\\:bg-gray-100');
      expect(cards.length).toBeGreaterThan(0);
    });
  });
});

