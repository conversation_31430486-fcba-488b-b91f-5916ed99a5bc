# Working Directory Issue - Root Cause Analysis & Solution

**Authority**: President & CEO, <PERSON><PERSON><PERSON>. Consultancy  
**Date**: 2025-10-20  
**Status**: 🔍 **ANALYSIS COMPLETE** - Solution Provided

---

## 🎯 **Problem Summary**

**Issue**: Integration tests behave differently depending on execution location

| Execution Location | Test Result | Command |
|-------------------|-------------|---------|
| **OA Framework Root** (`/home/<USER>/dev/web-dev/oa-prod/`) | ❌ **FAIL** | `npm test` (from root) |
| **M0 Dashboard Subdirectory** (`/home/<USER>/dev/web-dev/oa-prod/demos/m0-real-dashboard/`) | ✅ **PASS** | `npm test` (from subdirectory) |

---

## 🔍 **Root Cause Analysis**

### **1. Multiple Jest Configurations Conflict**

The OA Framework repository has **TWO separate Jest configurations**:

#### **Root Jest Configuration** (`/jest.config.js`)
```javascript
module.exports = {
  testEnvironment: 'node',
  preset: 'ts-jest',
  
  // ❌ PROBLEM: Looks for tests in root __tests__ directories
  testMatch: [
    '**/__tests__/**/*.test.ts',
    '**/?(*.)+(spec|test).ts'
  ],
  
  // ❌ PROBLEM: Path aliases resolve relative to ROOT
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1',           // Points to /home/<USER>/oa-prod/
    '^shared/(.*)$': '<rootDir>/shared/$1'
  },
  
  // ❌ PROBLEM: Setup file at root level
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  
  maxWorkers: 1,
  testTimeout: 30000
};
```

#### **M0 Dashboard Jest Configuration** (`/demos/m0-real-dashboard/jest.config.js`)
```javascript
module.exports = {
  testEnvironment: 'node',
  
  // ✅ CORRECT: Specific to integration tests
  testMatch: [
    '**/__tests__/integration/**/*.test.ts',
    '**/__tests__/integration/**/*.test.tsx'
  ],
  
  // ✅ CORRECT: Path aliases resolve relative to M0 Dashboard
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',              // Points to demos/m0-real-dashboard/src/
    '^@lib/(.*)$': '<rootDir>/src/lib/$1',
    '^@components/(.*)$': '<rootDir>/src/components/$1'
  },
  
  // ✅ CORRECT: Setup file in M0 Dashboard
  setupFilesAfterEnv: ['<rootDir>/__tests__/setup.ts'],
  
  maxWorkers: 1,
  testTimeout: 60000
};
```

### **2. Path Resolution Issues**

When running from **ROOT directory**:
- Jest uses **root `jest.config.js`**
- `<rootDir>` = `/home/<USER>/dev/web-dev/oa-prod/`
- Path alias `@/` resolves to `/home/<USER>/dev/web-dev/oa-prod/` (WRONG!)
- Expected: `@/` should resolve to `/home/<USER>/dev/web-dev/oa-prod/demos/m0-real-dashboard/src/`

When running from **M0 Dashboard subdirectory**:
- Jest uses **M0 Dashboard `jest.config.js`**
- `<rootDir>` = `/home/<USER>/dev/web-dev/oa-prod/demos/m0-real-dashboard/`
- Path alias `@/` resolves to `/home/<USER>/dev/web-dev/oa-prod/demos/m0-real-dashboard/src/` (CORRECT!)

### **3. Module Resolution Differences**

**From Root**:
```typescript
// In test file: demos/m0-real-dashboard/__tests__/integration/M0ComponentManager.integration.test.ts
import { M0ComponentManager } from '@/lib/M0ComponentManager';

// Jest resolves to: /home/<USER>/dev/web-dev/oa-prod/lib/M0ComponentManager
// ❌ WRONG PATH - File doesn't exist here!
```

**From Subdirectory**:
```typescript
// In test file: demos/m0-real-dashboard/__tests__/integration/M0ComponentManager.integration.test.ts
import { M0ComponentManager } from '@/lib/M0ComponentManager';

// Jest resolves to: /home/<USER>/dev/web-dev/oa-prod/demos/m0-real-dashboard/src/lib/M0ComponentManager
// ✅ CORRECT PATH - File exists!
```

### **4. Setup File Resolution**

**From Root**:
- Looks for: `/home/<USER>/dev/web-dev/oa-prod/jest.setup.js` (exists)
- ❌ WRONG: This is the OA Framework setup, not M0 Dashboard setup
- Missing M0 Dashboard-specific test configuration

**From Subdirectory**:
- Looks for: `/home/<USER>/dev/web-dev/oa-prod/demos/m0-real-dashboard/__tests__/setup.ts` (exists)
- ✅ CORRECT: M0 Dashboard-specific setup with proper cleanup

### **5. Separate node_modules**

Both locations have their own `node_modules`:
- Root: `/home/<USER>/dev/web-dev/oa-prod/node_modules/` (OA Framework dependencies)
- Subdirectory: `/home/<USER>/dev/web-dev/oa-prod/demos/m0-real-dashboard/node_modules/` (M0 Dashboard dependencies)

This is intentional for the Next.js application, but creates complexity for Jest.

---

## ✅ **Solution Options**

### **Option 1: Always Run Tests from Subdirectory (Recommended)** ⭐

**Approach**: Establish clear convention that M0 Dashboard tests MUST be run from the subdirectory.

**Implementation**:
1. Document the requirement clearly
2. Add npm scripts at root level that `cd` into subdirectory
3. Update CI/CD pipelines to run from correct directory

**Pros**:
- ✅ Simple and reliable
- ✅ No configuration changes needed
- ✅ Clear separation of concerns
- ✅ Works with existing setup

**Cons**:
- ⚠️ Requires developers to remember the convention
- ⚠️ Root-level `npm test` won't run M0 Dashboard tests

**Implementation Steps**:

1. **Add npm scripts to root `package.json`**:
```json
{
  "scripts": {
    "test:m0-dashboard": "cd demos/m0-real-dashboard && npm test",
    "test:m0-dashboard:integration": "cd demos/m0-real-dashboard && npm run test:integration",
    "test:m0-dashboard:coverage": "cd demos/m0-real-dashboard && npm run test:coverage"
  }
}
```

2. **Create documentation** (see below)

3. **Update CI/CD** to use correct working directory

---

### **Option 2: Configure Root Jest to Exclude M0 Dashboard**

**Approach**: Update root Jest config to explicitly ignore M0 Dashboard tests.

**Implementation**:

Update `/jest.config.js`:
```javascript
module.exports = {
  // ... existing config ...
  
  // Ignore M0 Dashboard tests when running from root
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/build/',
    '/coverage/',
    '/demos/m0-real-dashboard/',  // ← ADD THIS
  ],
};
```

**Pros**:
- ✅ Prevents accidental test execution from wrong location
- ✅ Clear separation between OA Framework and M0 Dashboard tests

**Cons**:
- ⚠️ Still requires running M0 Dashboard tests from subdirectory
- ⚠️ Doesn't solve the underlying path resolution issue

---

### **Option 3: Monorepo Configuration with Jest Projects**

**Approach**: Configure Jest to handle multiple projects with different configurations.

**Implementation**:

Update `/jest.config.js`:
```javascript
module.exports = {
  projects: [
    // OA Framework tests
    {
      displayName: 'oa-framework',
      testEnvironment: 'node',
      preset: 'ts-jest',
      testMatch: [
        '<rootDir>/tests/**/*.test.ts',
        '<rootDir>/server/**/__tests__/**/*.test.ts',
        '<rootDir>/shared/**/__tests__/**/*.test.ts'
      ],
      moduleNameMapper: {
        '^@/(.*)$': '<rootDir>/$1',
        '^shared/(.*)$': '<rootDir>/shared/$1'
      },
      setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
    },
    
    // M0 Dashboard tests
    {
      displayName: 'm0-dashboard',
      testEnvironment: 'node',
      rootDir: '<rootDir>/demos/m0-real-dashboard',
      testMatch: [
        '<rootDir>/demos/m0-real-dashboard/__tests__/**/*.test.ts'
      ],
      moduleNameMapper: {
        '^@/(.*)$': '<rootDir>/demos/m0-real-dashboard/src/$1',
        '^@lib/(.*)$': '<rootDir>/demos/m0-real-dashboard/src/lib/$1',
        '^@components/(.*)$': '<rootDir>/demos/m0-real-dashboard/src/components/$1'
      },
      setupFilesAfterEnv: ['<rootDir>/demos/m0-real-dashboard/__tests__/setup.ts'],
      transform: {
        '^.+\\.tsx?$': ['ts-jest', {
          tsconfig: {
            jsx: 'react',
            esModuleInterop: true,
            allowSyntheticDefaultImports: true,
            strict: true,
            skipLibCheck: true,
            moduleResolution: 'node',
            resolveJsonModule: true,
            isolatedModules: true,
            noEmit: true
          }
        }]
      },
    }
  ]
};
```

**Pros**:
- ✅ Can run all tests from root with `npm test`
- ✅ Proper path resolution for each project
- ✅ Professional monorepo setup

**Cons**:
- ⚠️ More complex configuration
- ⚠️ Requires testing to ensure no conflicts
- ⚠️ May need adjustments to existing test scripts

---

## 🎯 **Recommended Solution**

**Use Option 1 (Run from Subdirectory) + Option 2 (Exclude from Root)**

This provides:
1. ✅ Clear, simple convention
2. ✅ Prevents accidental misconfiguration
3. ✅ Minimal changes to existing setup
4. ✅ Easy to document and follow

---

## 📝 **Implementation Steps**

### **Step 1: Update Root Jest Config**

Add M0 Dashboard to ignore patterns in `/jest.config.js`.

### **Step 2: Add npm Scripts to Root**

Add convenience scripts to root `package.json` for running M0 Dashboard tests.

### **Step 3: Create Documentation**

Create clear documentation explaining where to run tests from.

### **Step 4: Update CI/CD**

Ensure CI/CD pipelines run M0 Dashboard tests from correct directory.

---

## 📚 **Developer Guidelines**

### **Running M0 Dashboard Tests**

**✅ CORRECT - Run from M0 Dashboard directory**:
```bash
cd demos/m0-real-dashboard
npm test                          # Run all tests
npm run test:integration          # Run integration tests only
npm run test:coverage             # Run with coverage
```

**✅ CORRECT - Run from root using npm scripts**:
```bash
npm run test:m0-dashboard         # Run all M0 Dashboard tests
npm run test:m0-dashboard:integration
npm run test:m0-dashboard:coverage
```

**❌ INCORRECT - Don't run from root directly**:
```bash
# DON'T DO THIS - Will use wrong Jest config!
npm test demos/m0-real-dashboard/__tests__/
```

### **Running OA Framework Tests**

**✅ CORRECT - Run from root**:
```bash
npm test                          # Run all OA Framework tests
npm run test:coverage             # Run with coverage
npm run test:m0-components        # Run M0 component tests
```

---

## 🔧 **Troubleshooting**

### **Issue: Tests fail with "Cannot find module '@/lib/...'"**

**Cause**: Running from wrong directory  
**Solution**: Run from `demos/m0-real-dashboard/` directory

### **Issue: Wrong Jest config being used**

**Cause**: Jest picks up root config instead of subdirectory config  
**Solution**: Ensure you're in the correct directory before running `npm test`

### **Issue: Setup file not found**

**Cause**: Path resolution using wrong `<rootDir>`  
**Solution**: Run from subdirectory where setup file exists

---

## ✅ **Verification**

After implementing the solution, verify:

1. **From Root**:
   ```bash
   cd /home/<USER>/dev/web-dev/oa-prod
   npm run test:m0-dashboard
   # Should pass all 39 tests
   ```

2. **From Subdirectory**:
   ```bash
   cd /home/<USER>/dev/web-dev/oa-prod/demos/m0-real-dashboard
   npm test
   # Should pass all 39 tests
   ```

3. **Root tests don't include M0 Dashboard**:
   ```bash
   cd /home/<USER>/dev/web-dev/oa-prod
   npm test
   # Should NOT run M0 Dashboard tests
   ```

---

**Authority**: President & CEO, E.Z. Consultancy  
**Status**: ✅ **ANALYSIS COMPLETE - SOLUTION PROVIDED**  
**Recommendation**: **Option 1 + Option 2** (Run from subdirectory + exclude from root)

