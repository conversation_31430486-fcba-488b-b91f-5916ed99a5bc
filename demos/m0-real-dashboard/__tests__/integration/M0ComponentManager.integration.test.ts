/**
 * @file M0 Component Manager Integration Test Suite
 * @filepath demos/m0-real-dashboard/__tests__/integration/M0ComponentManager.integration.test.ts
 * @component M0ComponentManager-Integration-Tests
 * @authority President & CEO, E<PERSON><PERSON><PERSON> Consultancy
 * @purpose Comprehensive integration testing for all 137 M0 components
 * @created 2025-10-19
 * @updated 2025-10-21 (Phase 4 - Task 1: Tracking Category 100% Completion)
 * @status INTEGRATION TESTING
 *
 * @description
 * Enterprise-grade integration test suite validating:
 * - All 137 components work together correctly (Phase 4: +8 tracking components)
 * - Cross-component interactions (governance ↔ tracking, memory-safety ↔ integration)
 * - Memory safety under load conditions
 * - Timer coordination and resource cleanup
 * - Component health monitoring
 * - API endpoint stability
 * - BaseTrackingService lifecycle management
 * - Circular dependency resolution (CleanupEnums)
 *
 * 🎯 SUCCESS CRITERIA:
 * - 100% test pass rate
 * - Zero memory leaks detected
 * - Timer utilization < 75% threshold (150/200)
 * - All 137 components maintain 100% health score
 * - Zero runtime errors or warnings
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.3)
 * @authority-level critical-integration-testing
 * @authority-validator "President & CEO, <PERSON><PERSON><PERSON><PERSON> Consultancy"
 * @governance-compliance anti-simplification-policy-compliant
 * @milestone-compliance M0-integration-testing-standards
 */

import { M0ComponentManager } from '../../src/lib/M0ComponentManager';

// Test timeout configuration for integration tests
const INTEGRATION_TEST_TIMEOUT = 60000; // 60 seconds for comprehensive integration tests
const LOAD_TEST_TIMEOUT = 120000; // 2 minutes for load testing scenarios

describe('M0 Component Manager - Integration Test Suite', () => {
  let componentManager: M0ComponentManager;

  beforeAll(() => {
    // Test environment is set in __tests__/setup.ts
    // process.env.NODE_ENV is read-only in TypeScript strict mode
  });

  beforeEach(() => {
    // Create fresh instance for each test
    componentManager = new M0ComponentManager();
  });

  afterEach(async () => {
    // Cleanup after each test
    if (componentManager) {
      await componentManager.shutdown();
    }
  });

  // ============================================================================
  // TEST SUITE 1: COMPONENT INITIALIZATION & LIFECYCLE
  // ============================================================================

  describe('Component Initialization & Lifecycle', () => {
    it('should initialize all 136 components successfully', async () => {
      await componentManager.initialize();

      const dashboardData = componentManager.getDashboardData();

      expect(dashboardData.totalComponents).toBe(136);
      expect(dashboardData.healthyComponents).toBe(136);
      expect(dashboardData.overallHealthScore).toBe(100);
    }, INTEGRATION_TEST_TIMEOUT);

    it('should initialize components in correct order (governance → tracking → memory-safety → integration)', async () => {
      const initializationOrder: string[] = [];
      
      // Spy on component registration to track order
      const originalRegister = (componentManager as any)._registerComponent.bind(componentManager);
      (componentManager as any)._registerComponent = (id: string, component: any, category: string) => {
        initializationOrder.push(category);
        return originalRegister(id, component, category);
      };

      await componentManager.initialize();

      // Verify governance components initialized first
      const firstGovernanceIndex = initializationOrder.indexOf('governance');
      const firstTrackingIndex = initializationOrder.indexOf('tracking');
      const firstMemorySafetyIndex = initializationOrder.indexOf('memory-safety');
      const firstIntegrationIndex = initializationOrder.indexOf('integration');

      expect(firstGovernanceIndex).toBeLessThan(firstTrackingIndex);
      expect(firstTrackingIndex).toBeLessThan(firstMemorySafetyIndex);
      expect(firstMemorySafetyIndex).toBeLessThan(firstIntegrationIndex);
    }, INTEGRATION_TEST_TIMEOUT);

    it('should handle component shutdown gracefully', async () => {
      await componentManager.initialize();

      const dashboardDataBefore = componentManager.getDashboardData();
      expect(dashboardDataBefore.totalComponents).toBe(136);

      await componentManager.shutdown();

      // Verify all components are cleaned up
      const componentInstances = (componentManager as any)._componentInstances;
      const componentStatuses = (componentManager as any)._componentStatuses;

      expect(componentInstances).toBeDefined();
      expect(componentStatuses).toBeDefined();
      expect(componentInstances.size).toBe(0);
      expect(componentStatuses.size).toBe(0);
    }, INTEGRATION_TEST_TIMEOUT);

    it('should support multiple initialize/shutdown cycles without memory leaks', async () => {
      const initialMemory = process.memoryUsage().heapUsed;

      // Run 5 initialization/shutdown cycles
      for (let i = 0; i < 5; i++) {
        await componentManager.initialize();
        await componentManager.shutdown();
        
        // Create new instance for next cycle
        if (i < 4) {
          componentManager = new M0ComponentManager();
        }
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryGrowth = finalMemory - initialMemory;

      // Memory growth should be minimal (< 50MB for 5 cycles)
      expect(memoryGrowth).toBeLessThan(50 * 1024 * 1024);
    }, LOAD_TEST_TIMEOUT);
  });

  // ============================================================================
  // TEST SUITE 2: CROSS-COMPONENT INTERACTIONS
  // ============================================================================

  describe('Cross-Component Interactions', () => {
    beforeEach(async () => {
      await componentManager.initialize();
    });

    it('should validate governance ↔ tracking component interactions', async () => {
      const dashboardData = componentManager.getDashboardData();

      // Verify governance components exist
      const governanceComponents = dashboardData.categories.governance;
      expect(governanceComponents.length).toBe(69);

      // Verify tracking components exist
      const trackingComponents = dashboardData.categories.tracking;
      expect(trackingComponents.length).toBe(33);

      // Verify all components are healthy
      governanceComponents.forEach(component => {
        expect(component.healthScore).toBe(100);
      });

      trackingComponents.forEach(component => {
        expect(component.healthScore).toBe(100);
      });
    }, INTEGRATION_TEST_TIMEOUT);

    it('should validate memory-safety ↔ integration component interactions', async () => {
      const dashboardData = componentManager.getDashboardData();

      // Verify memory-safety components exist
      const memorySafetyComponents = dashboardData.categories.memorySafety;
      expect(memorySafetyComponents.length).toBe(19);

      // Verify integration components exist
      const integrationComponents = dashboardData.categories.integration;
      expect(integrationComponents.length).toBe(15);

      // Verify all components are healthy
      memorySafetyComponents.forEach(component => {
        expect(component.healthScore).toBe(100);
      });

      integrationComponents.forEach(component => {
        expect(component.healthScore).toBe(100);
      });
    }, INTEGRATION_TEST_TIMEOUT);

    it('should validate circular dependency fix (CleanupEnums) works in production scenarios', async () => {
      // Verify CleanupCoordinatorEnhanced is initialized
      const cleanupCoordinator = componentManager.getAllComponents().find(
        c => c.id === 'cleanup-coordinator-enhanced'
      );
      expect(cleanupCoordinator).toBeDefined();
      expect(cleanupCoordinator?.healthScore).toBe(100);

      // Verify MemorySafetyManager is initialized
      const memorySafetyManager = componentManager.getAllComponents().find(
        c => c.id === 'memory-safety-manager'
      );
      expect(memorySafetyManager).toBeDefined();
      expect(memorySafetyManager?.healthScore).toBe(100);

      // Verify MemorySafetyManagerEnhanced is initialized
      const memorySafetyManagerEnhanced = componentManager.getAllComponents().find(
        c => c.id === 'memory-safety-manager-enhanced'
      );
      expect(memorySafetyManagerEnhanced).toBeDefined();
      expect(memorySafetyManagerEnhanced?.healthScore).toBe(100);
    }, INTEGRATION_TEST_TIMEOUT);
  });

  // ============================================================================
  // TEST SUITE 3: MEMORY SAFETY UNDER LOAD
  // ============================================================================

  describe('Memory Safety Under Load', () => {
    beforeEach(async () => {
      await componentManager.initialize();
    });

    it('should maintain memory safety during concurrent getDashboardData calls', async () => {
      const initialMemory = process.memoryUsage().heapUsed;

      // Execute 20 concurrent getDashboardData calls
      const promises = Array.from({ length: 20 }, () => 
        componentManager.getDashboardData()
      );

      const results = await Promise.all(promises);

      // Verify all results are consistent
      results.forEach(result => {
        expect(result.totalComponents).toBe(136);
        expect(result.overallHealthScore).toBe(100);
      });

      // Force garbage collection
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryGrowth = finalMemory - initialMemory;

      // Memory growth should be minimal (< 20MB for 20 concurrent calls)
      expect(memoryGrowth).toBeLessThan(20 * 1024 * 1024);
    }, LOAD_TEST_TIMEOUT);

    it('should detect and prevent memory leaks during extended operation', async () => {
      const memorySnapshots: number[] = [];

      // Take memory snapshots over 10 iterations
      for (let i = 0; i < 10; i++) {
        componentManager.getDashboardData();

        if (global.gc) {
          global.gc();
        }

        memorySnapshots.push(process.memoryUsage().heapUsed);

        // Small delay between iterations
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Calculate memory growth trend
      const firstSnapshot = memorySnapshots[0];
      const lastSnapshot = memorySnapshots[memorySnapshots.length - 1];
      const memoryGrowth = lastSnapshot - firstSnapshot;

      // Memory growth should be minimal (< 10MB over 10 iterations)
      expect(memoryGrowth).toBeLessThan(10 * 1024 * 1024);
    }, LOAD_TEST_TIMEOUT);
  });

  // ============================================================================
  // TEST SUITE 4: TIMER COORDINATION & RESOURCE MANAGEMENT
  // ============================================================================

  describe('Timer Coordination & Resource Management', () => {
    beforeEach(async () => {
      await componentManager.initialize();
    });

    it('should maintain timer utilization below 75% threshold (150/200)', async () => {
      // Verify all components have valid metrics
      const allComponents = componentManager.getAllComponents();

      expect(allComponents.length).toBe(136);

      // Check that all components have metrics
      allComponents.forEach(component => {
        expect(component.metrics).toBeDefined();
        expect(component.metrics.responseTime).toBeGreaterThanOrEqual(0);
        expect(component.metrics.memoryUsage).toBeGreaterThanOrEqual(0);
      });

      // Note: Timer utilization is managed by BaseTrackingService and TimerCoordinationService
      // This test validates that all components have valid metrics
      // Actual timer utilization tracking is handled by the resilient timing infrastructure
    }, INTEGRATION_TEST_TIMEOUT);

    it('should coordinate timers across all 136 components without conflicts', async () => {
      // Verify all components have valid timer configurations
      const allComponents = componentManager.getAllComponents();

      expect(allComponents.length).toBe(136);

      allComponents.forEach(component => {
        expect(component.id).toBeDefined();
        expect(component.category).toBeDefined();
        expect(component.healthScore).toBe(100);
        expect(component.status).toBe('healthy');
      });

      // Verify timer coordination service is healthy
      const timerCoordinator = allComponents.find(
        c => c.id === 'timer-coordination-service' || c.id === 'timer-coordination-service-enhanced'
      );
      expect(timerCoordinator).toBeDefined();
      expect(timerCoordinator?.status).toBe('healthy');
    }, INTEGRATION_TEST_TIMEOUT);

    it('should cleanup all timers during shutdown', async () => {
      const allComponentsBefore = componentManager.getAllComponents();

      // Verify timer coordination service is healthy before shutdown
      const timerCoordinator = allComponentsBefore.find(
        c => c.id === 'timer-coordination-service' || c.id === 'timer-coordination-service-enhanced'
      );

      expect(timerCoordinator).toBeDefined();
      expect(timerCoordinator?.status).toBe('healthy');

      await componentManager.shutdown();

      // Verify all timers are cleaned up
      const componentInstances = (componentManager as any)._componentInstances;
      const componentStatuses = (componentManager as any)._componentStatuses;

      expect(componentInstances).toBeDefined();
      expect(componentStatuses).toBeDefined();
      expect(componentInstances.size).toBe(0);
      expect(componentStatuses.size).toBe(0);
    }, INTEGRATION_TEST_TIMEOUT);
  });

  // ============================================================================
  // TEST SUITE 5: COMPONENT HEALTH MONITORING
  // ============================================================================

  describe('Component Health Monitoring', () => {
    beforeEach(async () => {
      await componentManager.initialize();
    });

    it('should maintain 100% health score for all 136 components', async () => {
      const dashboardData = componentManager.getDashboardData();

      expect(dashboardData.totalComponents).toBe(136);
      expect(dashboardData.healthyComponents).toBe(136);
      expect(dashboardData.overallHealthScore).toBe(100);

      // Verify each component individually
      componentManager.getAllComponents().forEach(component => {
        expect(component.healthScore).toBe(100);
        expect(component.status).toBe('healthy');
      });
    }, INTEGRATION_TEST_TIMEOUT);

    it('should track component health across categories', async () => {
      const dashboardData = componentManager.getDashboardData();

      // Verify governance components (49)
      expect(dashboardData.categories.governance.length).toBe(69);
      dashboardData.categories.governance.forEach(component => {
        expect(component.healthScore).toBe(100);
      });

      // Verify tracking components (21)
      expect(dashboardData.categories.tracking.length).toBe(33);
      dashboardData.categories.tracking.forEach(component => {
        expect(component.healthScore).toBe(100);
      });

      // Verify memory-safety components (15)
      expect(dashboardData.categories.memorySafety.length).toBe(19);
      dashboardData.categories.memorySafety.forEach(component => {
        expect(component.healthScore).toBe(100);
      });

      // Verify integration components (15)
      expect(dashboardData.categories.integration.length).toBe(15);
      dashboardData.categories.integration.forEach(component => {
        expect(component.healthScore).toBe(100);
      });
    }, INTEGRATION_TEST_TIMEOUT);

    it('should maintain health score under stress conditions', async () => {
      // Execute multiple concurrent operations
      const operations = Array.from({ length: 50 }, async () => {
        return componentManager.getDashboardData();
      });

      const results = await Promise.all(operations);

      // Verify all results maintain 100% health score
      results.forEach(result => {
        expect(result.overallHealthScore).toBe(100);
        expect(result.healthyComponents).toBe(136);
      });
    }, LOAD_TEST_TIMEOUT);
  });

  // ============================================================================
  // TEST SUITE 6: API ENDPOINT STABILITY
  // ============================================================================

  describe('API Endpoint Stability', () => {
    beforeEach(async () => {
      await componentManager.initialize();
    });

    it('should handle sequential API requests without errors', async () => {
      const results = [];

      // Execute 10 sequential requests
      for (let i = 0; i < 10; i++) {
        const result = componentManager.getDashboardData();
        results.push(result);
      }

      // Verify all results are consistent
      results.forEach(result => {
        expect(result.totalComponents).toBe(136);
        expect(result.overallHealthScore).toBe(100);
      });
    }, INTEGRATION_TEST_TIMEOUT);

    it('should handle concurrent API requests without race conditions', async () => {
      // Execute 30 concurrent requests
      const promises = Array.from({ length: 30 }, () =>
        componentManager.getDashboardData()
      );

      const results = await Promise.all(promises);

      // Verify all results are consistent
      results.forEach(result => {
        expect(result.totalComponents).toBe(136);
        expect(result.healthyComponents).toBe(136);
        expect(result.overallHealthScore).toBe(100);
      });

      // Verify data structure consistency
      const firstResult = results[0];
      results.forEach(result => {
        expect(result.categories.governance.length).toBe(firstResult.categories.governance.length);
        expect(result.categories.tracking.length).toBe(firstResult.categories.tracking.length);
        expect(result.categories.memorySafety.length).toBe(firstResult.categories.memorySafety.length);
        expect(result.categories.integration.length).toBe(firstResult.categories.integration.length);
      });
    }, LOAD_TEST_TIMEOUT);

    it('should maintain API stability during rapid-fire requests', async () => {
      const results = [];

      // Execute 100 rapid-fire requests
      for (let i = 0; i < 100; i++) {
        const result = componentManager.getDashboardData();
        results.push(result);
      }

      // Verify all results are valid
      expect(results.length).toBe(100);
      results.forEach(result => {
        expect(result.totalComponents).toBe(136);
        expect(result.overallHealthScore).toBe(100);
      });
    }, LOAD_TEST_TIMEOUT);
  });

  // ============================================================================
  // TEST SUITE 7: ERROR HANDLING & RECOVERY (PHASE 1 - COVERAGE IMPROVEMENT)
  // ============================================================================

  describe('Error Handling & Recovery', () => {
    it('should handle component initialization failures gracefully', async () => {
      // Mock doInitialize to throw an error
      const originalDoInitialize = (componentManager as any).doInitialize.bind(componentManager);
      (componentManager as any).doInitialize = jest.fn().mockRejectedValue(
        new Error('Component initialization failed')
      );

      // Attempt initialization - should throw error
      await expect(componentManager.initialize()).rejects.toThrow('Component initialization failed');

      // Restore original method
      (componentManager as any).doInitialize = originalDoInitialize;
    }, INTEGRATION_TEST_TIMEOUT);

    it('should handle component discovery failures gracefully', async () => {
      // Mock _discoverM0Components to throw an error
      const originalDiscover = (componentManager as any)._discoverM0Components.bind(componentManager);
      (componentManager as any)._discoverM0Components = jest.fn().mockRejectedValue(
        new Error('Component discovery failed')
      );

      // Attempt initialization - should throw error
      await expect(componentManager.initialize()).rejects.toThrow();

      // Restore original method
      (componentManager as any)._discoverM0Components = originalDiscover;
    }, INTEGRATION_TEST_TIMEOUT);

    it('should handle resilient timing initialization failures gracefully', async () => {
      // Create a new instance to test constructor-level error handling
      const testManager = new M0ComponentManager();

      // The component should still initialize even if resilient timing fails
      // (it logs a warning and continues in degraded mode)
      await testManager.initialize();

      const dashboardData = testManager.getDashboardData();
      expect(dashboardData.totalComponents).toBe(136);

      await testManager.shutdown();
    }, INTEGRATION_TEST_TIMEOUT);

    it('should handle component refresh failures gracefully', async () => {
      await componentManager.initialize();

      // Mock _updateAllComponentStatuses to throw an error
      const originalUpdate = (componentManager as any)._updateAllComponentStatuses.bind(componentManager);
      (componentManager as any)._updateAllComponentStatuses = jest.fn().mockRejectedValue(
        new Error('Status update failed')
      );

      // Attempt refresh - should throw error
      await expect(componentManager.refreshAllComponents()).rejects.toThrow('Status update failed');

      // Restore original method
      (componentManager as any)._updateAllComponentStatuses = originalUpdate;
    }, INTEGRATION_TEST_TIMEOUT);

    it('should handle health check method failures gracefully', async () => {
      await componentManager.initialize();

      // Get a component instance and mock its getHealthStatus to throw
      const componentInstances = (componentManager as any)._componentInstances;
      const firstComponentId = Array.from(componentInstances.keys())[0] as string;
      const firstComponent = componentInstances.get(firstComponentId);

      if (firstComponent && typeof (firstComponent as any).getHealthStatus === 'function') {
        const originalHealthCheck = (firstComponent as any).getHealthStatus.bind(firstComponent);
        (firstComponent as any).getHealthStatus = jest.fn().mockRejectedValue(
          new Error('Health check failed')
        );

        // Trigger health check
        await (componentManager as any)._checkComponentHealth(firstComponentId, firstComponent);

        // Verify component status was updated to error
        const status = componentManager.getComponentStatus(firstComponentId);
        expect(status).toBeDefined();
        expect(status?.status).toBe('error');
        expect(status?.healthScore).toBe(0);

        // Restore original method
        (firstComponent as any).getHealthStatus = originalHealthCheck;

        // Restore component health status to healthy
        const componentStatus = (componentManager as any)._componentStatuses.get(firstComponentId);
        if (componentStatus) {
          componentStatus.status = 'healthy';
          componentStatus.healthScore = 100;
        }

        // Update dashboard data to reflect restored health
        (componentManager as any)._updateDashboardData();
      }
    }, INTEGRATION_TEST_TIMEOUT);

    it('should handle individual component shutdown failures gracefully', async () => {
      await componentManager.initialize();

      // Mock one component's shutdown to throw an error
      const componentInstances = (componentManager as any)._componentInstances;
      const firstComponentId = Array.from(componentInstances.keys())[0] as string;
      const firstComponent = componentInstances.get(firstComponentId);

      if (firstComponent && typeof (firstComponent as any).shutdown === 'function') {
        const originalShutdown = (firstComponent as any).shutdown.bind(firstComponent);
        (firstComponent as any).shutdown = jest.fn().mockRejectedValue(
          new Error('Component shutdown failed')
        );

        // Shutdown should complete despite individual component failure
        await componentManager.shutdown();

        // Verify component instances are cleared (internal state)
        const instancesAfterShutdown = (componentManager as any)._componentInstances;
        expect(instancesAfterShutdown.size).toBe(0);

        // Restore original method (though manager is already shut down)
        (firstComponent as any).shutdown = originalShutdown;
      } else {
        // If no component has shutdown method, just verify normal shutdown works
        await componentManager.shutdown();
        const instancesAfterShutdown = (componentManager as any)._componentInstances;
        expect(instancesAfterShutdown.size).toBe(0);
      }
    }, INTEGRATION_TEST_TIMEOUT);
  });

  // ============================================================================
  // TEST SUITE 8: PUBLIC API METHODS (PHASE 2 - COVERAGE IMPROVEMENT)
  // ============================================================================

  describe('Public API Methods', () => {
    beforeEach(async () => {
      await componentManager.initialize();
    });

    describe('getComponentStatus()', () => {
      it('should return status for valid component IDs', async () => {
        // Get all component IDs
        const componentInstances = (componentManager as any)._componentInstances;
        const componentIds = Array.from(componentInstances.keys()) as string[];

        // Test first 10 components
        componentIds.slice(0, 10).forEach((componentId) => {
          const status = componentManager.getComponentStatus(componentId);

          expect(status).toBeDefined();
          expect(status?.id).toBe(componentId);
          expect(status?.status).toBeDefined();
          expect(status?.healthScore).toBeDefined();
          expect(status?.metrics).toBeDefined();
        });
      }, INTEGRATION_TEST_TIMEOUT);

      it('should return undefined for invalid component IDs', async () => {
        const invalidIds = [
          'non-existent-component',
          'invalid-id-123',
          'fake-component',
          '',
          'null'
        ];

        invalidIds.forEach(invalidId => {
          const status = componentManager.getComponentStatus(invalidId);
          expect(status).toBeUndefined();
        });
      }, INTEGRATION_TEST_TIMEOUT);

      it('should return status for all 104 registered components', async () => {
        const componentInstances = (componentManager as any)._componentInstances;
        const componentIds = Array.from(componentInstances.keys()) as string[];

        expect(componentIds.length).toBe(136);

        // Verify all components have valid status
        componentIds.forEach((componentId) => {
          const status = componentManager.getComponentStatus(componentId);
          expect(status).toBeDefined();
          expect(status?.id).toBe(componentId);
        });
      }, INTEGRATION_TEST_TIMEOUT);
    });

    describe('getComponentsByCategory()', () => {
      it('should return all governance components (69 components)', async () => {
        const governanceComponents = componentManager.getComponentsByCategory('governance');

        expect(governanceComponents).toBeDefined();
        expect(governanceComponents.length).toBe(69);

        // Verify all returned components are governance category
        governanceComponents.forEach(component => {
          expect(component.category).toBe('governance');
          expect(component.id).toBeDefined();
          expect(component.status).toBeDefined();
          expect(component.healthScore).toBeDefined();
        });
      }, INTEGRATION_TEST_TIMEOUT);

      it('should return all tracking components (33 components)', async () => {
        const trackingComponents = componentManager.getComponentsByCategory('tracking');

        expect(trackingComponents).toBeDefined();
        expect(trackingComponents.length).toBe(33);

        // Verify all returned components are tracking category
        trackingComponents.forEach(component => {
          expect(component.category).toBe('tracking');
          expect(component.id).toBeDefined();
          expect(component.status).toBeDefined();
        });
      }, INTEGRATION_TEST_TIMEOUT);

      it('should return all memory-safety components (19 components)', async () => {
        const memorySafetyComponents = componentManager.getComponentsByCategory('memorySafety');

        expect(memorySafetyComponents).toBeDefined();
        expect(memorySafetyComponents.length).toBe(19);

        // Verify all returned components are memory-safety category
        memorySafetyComponents.forEach(component => {
          expect(component.category).toBe('memory-safety');
          expect(component.id).toBeDefined();
          expect(component.status).toBeDefined();
        });
      }, INTEGRATION_TEST_TIMEOUT);

      it('should return all integration components (15 components)', async () => {
        const integrationComponents = componentManager.getComponentsByCategory('integration');

        expect(integrationComponents).toBeDefined();
        expect(integrationComponents.length).toBe(15);

        // Verify all returned components are integration category
        integrationComponents.forEach(component => {
          expect(component.category).toBe('integration');
          expect(component.id).toBeDefined();
          expect(component.status).toBeDefined();
        });
      }, INTEGRATION_TEST_TIMEOUT);

      it('should return array copies (not references to internal state)', async () => {
        const governanceComponents1 = componentManager.getComponentsByCategory('governance');
        const governanceComponents2 = componentManager.getComponentsByCategory('governance');

        // Arrays should have same content but be different instances
        expect(governanceComponents1.length).toBe(governanceComponents2.length);
        expect(governanceComponents1).not.toBe(governanceComponents2);
      }, INTEGRATION_TEST_TIMEOUT);
    });

    describe('refreshAllComponents()', () => {
      it('should trigger status updates for all components', async () => {
        // Get initial dashboard data
        const initialData = componentManager.getDashboardData();
        const initialTimestamp = initialData.lastUpdated;

        // Wait a bit to ensure timestamp difference
        await new Promise(resolve => setTimeout(resolve, 100));

        // Trigger refresh
        await componentManager.refreshAllComponents();

        // Get updated dashboard data
        const updatedData = componentManager.getDashboardData();
        const updatedTimestamp = updatedData.lastUpdated;

        // Verify data was updated
        expect(updatedData.totalComponents).toBe(136);
        expect(updatedTimestamp.getTime()).toBeGreaterThanOrEqual(initialTimestamp.getTime());
      }, INTEGRATION_TEST_TIMEOUT);

      it('should update component metrics during refresh', async () => {
        // Get initial component status
        const componentInstances = (componentManager as any)._componentInstances;
        const firstComponentId = Array.from(componentInstances.keys())[0] as string;
        const initialStatus = componentManager.getComponentStatus(firstComponentId);
        const initialOperationCount = initialStatus?.metrics.operationCount || 0;

        // Trigger refresh
        await componentManager.refreshAllComponents();

        // Get updated component status
        const updatedStatus = componentManager.getComponentStatus(firstComponentId);
        const updatedOperationCount = updatedStatus?.metrics.operationCount || 0;

        // Verify metrics were updated (operation count should increase)
        expect(updatedOperationCount).toBeGreaterThanOrEqual(initialOperationCount);
      }, INTEGRATION_TEST_TIMEOUT);

      it('should complete refresh without errors', async () => {
        // Trigger refresh - should complete without throwing
        await expect(componentManager.refreshAllComponents()).resolves.not.toThrow();

        // Verify dashboard data is still valid after refresh
        const dashboardData = componentManager.getDashboardData();
        expect(dashboardData.totalComponents).toBe(136);
        expect(dashboardData.healthyComponents).toBeGreaterThan(0);
        expect(dashboardData.healthyComponents).toBeLessThanOrEqual(136);
      }, INTEGRATION_TEST_TIMEOUT);
    });

    describe('Edge Cases & Error Scenarios', () => {
      it('should handle getComponentStatus() after shutdown', async () => {
        // Shutdown the manager
        await componentManager.shutdown();

        // Attempt to get component status
        const status = componentManager.getComponentStatus('any-component-id');

        // Should return undefined (no components registered after shutdown)
        expect(status).toBeUndefined();
      }, INTEGRATION_TEST_TIMEOUT);

      it('should handle getComponentsByCategory() after shutdown', async () => {
        // Shutdown the manager
        await componentManager.shutdown();

        // Attempt to get components by category
        const components = componentManager.getComponentsByCategory('governance');

        // Dashboard data is not cleared on shutdown (returns last known state)
        // This is expected behavior - dashboard data is a snapshot
        expect(components).toBeDefined();
        expect(Array.isArray(components)).toBe(true);
      }, INTEGRATION_TEST_TIMEOUT);

      it('should handle getDashboardData() before initialization', async () => {
        // Create new manager without initializing
        const uninitializedManager = new M0ComponentManager();

        // Get dashboard data before initialization
        const dashboardData = uninitializedManager.getDashboardData();

        // Should return default/empty state
        expect(dashboardData).toBeDefined();
        expect(dashboardData.totalComponents).toBe(0);
        expect(dashboardData.healthyComponents).toBe(0);

        await uninitializedManager.shutdown();
      }, INTEGRATION_TEST_TIMEOUT);

      it('should handle concurrent API calls during initialization', async () => {
        // Create new manager
        const testManager = new M0ComponentManager();

        // Start initialization and immediately make API calls
        const initPromise = testManager.initialize();
        const apiCalls = [
          testManager.getDashboardData(),
          testManager.getDashboardData(),
          testManager.getDashboardData()
        ];

        // Wait for initialization to complete
        await initPromise;

        // Verify API calls returned valid data
        apiCalls.forEach(data => {
          expect(data).toBeDefined();
        });

        await testManager.shutdown();
      }, INTEGRATION_TEST_TIMEOUT);
    });
  });

  // ============================================================================
  // TEST SUITE 8: VALIDATION & TRACKING TESTS (PHASE 3)
  // ============================================================================

  describe('Validation & Tracking Tests (Phase 3)', () => {
    describe('doValidate() Method', () => {
      it('should return valid status when all components are healthy', async () => {
        await componentManager.initialize();

        // Access protected doValidate method
        const validationResult = await (componentManager as any).doValidate();

        expect(validationResult).toBeDefined();
        expect(validationResult.status).toBe('valid');
        expect(validationResult.overallScore).toBe(100);
        expect(validationResult.errors).toHaveLength(0);
        expect(validationResult.validationId).toBeDefined();
        expect(validationResult.componentId).toBe('M0ComponentManager');
      }, INTEGRATION_TEST_TIMEOUT);

      it('should return warnings when no components are registered', async () => {
        // Create new manager without initializing
        const uninitializedManager = new M0ComponentManager();

        // Validate before initialization
        const validationResult = await (uninitializedManager as any).doValidate();

        expect(validationResult).toBeDefined();
        expect(validationResult.status).toBe('valid'); // No errors, just warnings
        expect(validationResult.warnings).toContain('No M0 components are currently registered');
        expect(validationResult.overallScore).toBe(100); // No errors = 100 score

        await uninitializedManager.shutdown();
      }, INTEGRATION_TEST_TIMEOUT);

      it('should return warnings when monitoring is not active but enabled in config', async () => {
        await componentManager.initialize();

        // Stop monitoring by setting flag
        (componentManager as any)._isMonitoring = false;

        // Validate with monitoring disabled
        const validationResult = await (componentManager as any).doValidate();

        expect(validationResult).toBeDefined();
        expect(validationResult.warnings).toContain('Real-time monitoring is not active');
      }, INTEGRATION_TEST_TIMEOUT);

      it('should return errors when components are in error state', async () => {
        await componentManager.initialize();

        // Manually set some components to error state
        const statuses = (componentManager as any)._componentStatuses;
        let errorCount = 0;
        for (const [componentId, status] of statuses) {
          if (errorCount < 3) {
            status.status = 'error';
            errorCount++;
          }
        }

        // Validate with error components
        const validationResult = await (componentManager as any).doValidate();

        expect(validationResult).toBeDefined();
        expect(validationResult.status).toBe('invalid');
        expect(validationResult.errors).toContain('3 components are in error state');
        expect(validationResult.overallScore).toBeLessThan(100);
        expect(validationResult.overallScore).toBeGreaterThanOrEqual(0);
      }, INTEGRATION_TEST_TIMEOUT);

      it('should calculate correct overall score based on error count', async () => {
        await componentManager.initialize();

        // Set 4 components to error state
        const statuses = (componentManager as any)._componentStatuses;
        let errorCount = 0;
        for (const [componentId, status] of statuses) {
          if (errorCount < 4) {
            status.status = 'error';
            errorCount++;
          }
        }

        // Validate and check score calculation
        const validationResult = await (componentManager as any).doValidate();

        // The validation creates 1 error message: "4 components are in error state"
        // Score = 100 - (errors.length * 20) = 100 - (1 * 20) = 80
        expect(validationResult.overallScore).toBe(80);
        expect(validationResult.errors).toHaveLength(1);
        expect(validationResult.errors[0]).toBe('4 components are in error state');
      }, INTEGRATION_TEST_TIMEOUT);

      it('should handle validation errors gracefully', async () => {
        await componentManager.initialize();

        // Force an error by corrupting internal state
        const originalStatuses = (componentManager as any)._componentStatuses;
        (componentManager as any)._componentStatuses = null;

        // Validate with corrupted state
        const validationResult = await (componentManager as any).doValidate();

        expect(validationResult).toBeDefined();
        expect(validationResult.status).toBe('invalid');
        expect(validationResult.overallScore).toBe(0);
        expect(validationResult.errors.length).toBeGreaterThan(0);
        expect(validationResult.errors[0]).toContain('Validation failed:');

        // Restore state
        (componentManager as any)._componentStatuses = originalStatuses;
      }, INTEGRATION_TEST_TIMEOUT);

      it('should include proper validation metadata', async () => {
        await componentManager.initialize();

        const validationResult = await (componentManager as any).doValidate();

        expect(validationResult.metadata).toBeDefined();
        expect(validationResult.metadata.validationMethod).toBe('m0-component-validation');
        expect(validationResult.metadata.rulesApplied).toBe(3);
        expect(validationResult.metadata.dependencyDepth).toBe(0);
        expect(validationResult.metadata.cyclicDependencies).toEqual([]);
        expect(validationResult.metadata.orphanReferences).toEqual([]);
      }, INTEGRATION_TEST_TIMEOUT);

      it('should include proper references metadata', async () => {
        await componentManager.initialize();

        const validationResult = await (componentManager as any).doValidate();

        expect(validationResult.references).toBeDefined();
        expect(validationResult.references.componentId).toBe('M0ComponentManager');
        expect(validationResult.references.metadata.totalReferences).toBe(136);
        expect(validationResult.references.metadata.analysisDepth).toBe(1);
      }, INTEGRATION_TEST_TIMEOUT);
    });

    describe('doTrack() Method', () => {
      it('should track component operations successfully', async () => {
        await componentManager.initialize();

        // Get a component ID using getAllComponents()
        const allComponents = componentManager.getAllComponents();
        const componentId = allComponents[0]?.id;

        expect(componentId).toBeDefined();

        // Get initial operation count
        const initialStatus = (componentManager as any)._componentStatuses.get(componentId);
        const initialCount = initialStatus?.metrics.operationCount || 0;

        // Track operation
        await (componentManager as any).doTrack({ componentId });

        // Verify operation count increased
        const updatedStatus = (componentManager as any)._componentStatuses.get(componentId);
        expect(updatedStatus.metrics.operationCount).toBe(initialCount + 1);
        expect(updatedStatus.lastUpdate).toBeInstanceOf(Date);
      }, INTEGRATION_TEST_TIMEOUT);

      it('should handle tracking for non-existent component gracefully', async () => {
        await componentManager.initialize();

        // Track operation for non-existent component
        await expect(
          (componentManager as any).doTrack({ componentId: 'non-existent-component' })
        ).resolves.not.toThrow();
      }, INTEGRATION_TEST_TIMEOUT);

      it('should handle tracking without componentId gracefully', async () => {
        await componentManager.initialize();

        // Track operation without componentId
        await expect(
          (componentManager as any).doTrack({})
        ).resolves.not.toThrow();
      }, INTEGRATION_TEST_TIMEOUT);

      it('should handle tracking errors gracefully', async () => {
        await componentManager.initialize();

        // Force an error by corrupting internal state
        const originalStatuses = (componentManager as any)._componentStatuses;
        (componentManager as any)._componentStatuses = null;

        // Track operation with corrupted state
        await expect(
          (componentManager as any).doTrack({ componentId: 'any-id' })
        ).resolves.not.toThrow();

        // Restore state
        (componentManager as any)._componentStatuses = originalStatuses;
      }, INTEGRATION_TEST_TIMEOUT);

      it('should update lastUpdate timestamp when tracking', async () => {
        await componentManager.initialize();

        // Get a component ID using getAllComponents()
        const allComponents = componentManager.getAllComponents();
        const componentId = allComponents[0]?.id;

        // Get initial timestamp
        const initialStatus = (componentManager as any)._componentStatuses.get(componentId);
        const initialTimestamp = initialStatus?.lastUpdate;

        // Wait a bit to ensure timestamp difference
        await new Promise(resolve => setTimeout(resolve, 10));

        // Track operation
        await (componentManager as any).doTrack({ componentId });

        // Verify timestamp updated
        const updatedStatus = (componentManager as any)._componentStatuses.get(componentId);
        expect(updatedStatus.lastUpdate.getTime()).toBeGreaterThan(initialTimestamp.getTime());
      }, INTEGRATION_TEST_TIMEOUT);
    });
  });

  // ============================================================================
  // TEST SUITE 9: REAL-TIME MONITORING TESTS (PHASE 4)
  // ============================================================================

  describe('Real-Time Monitoring Tests (Phase 4)', () => {
    describe('Monitoring Lifecycle', () => {
      it('should start monitoring when enableRealTimeUpdates is true', async () => {
        await componentManager.initialize();

        // Verify monitoring started
        expect((componentManager as any)._isMonitoring).toBe(true);
      }, INTEGRATION_TEST_TIMEOUT);

      it('should not start monitoring when enableRealTimeUpdates is false', async () => {
        // Create manager with monitoring disabled
        const config = {
          enableRealTimeUpdates: false,
          updateInterval: 5000,
          healthCheckInterval: 10000,
          performanceThresholds: {
            responseTimeWarning: 1000,
            responseTimeError: 5000,
            memoryUsageWarning: 0.7,
            memoryUsageError: 0.9
          }
        };

        const testManager = new M0ComponentManager(config);
        await testManager.initialize();

        // Verify monitoring not started
        expect((testManager as any)._isMonitoring).toBe(false);

        await testManager.shutdown();
      }, INTEGRATION_TEST_TIMEOUT);

      it('should stop monitoring during shutdown', async () => {
        await componentManager.initialize();

        // Verify monitoring started
        expect((componentManager as any)._isMonitoring).toBe(true);

        // Shutdown
        await componentManager.shutdown();

        // Verify monitoring stopped
        expect((componentManager as any)._isMonitoring).toBe(false);
      }, INTEGRATION_TEST_TIMEOUT);
    });

    describe('_performHealthChecks() Method', () => {
      it('should perform health checks for all components', async () => {
        await componentManager.initialize();

        // Manually trigger health checks
        await (componentManager as any)._performHealthChecks();

        // Verify all components have updated metrics
        const statuses = (componentManager as any)._componentStatuses;
        for (const [componentId, status] of statuses) {
          expect(status.metrics.operationCount).toBeGreaterThan(0);
          expect(status.lastUpdate).toBeInstanceOf(Date);
        }
      }, INTEGRATION_TEST_TIMEOUT);

      it('should skip health checks when monitoring is not active', async () => {
        await componentManager.initialize();

        // Stop monitoring
        (componentManager as any)._isMonitoring = false;

        // Get initial operation counts
        const statuses = (componentManager as any)._componentStatuses;
        const initialCounts = new Map();
        for (const [componentId, status] of statuses) {
          initialCounts.set(componentId, status.metrics.operationCount);
        }

        // Trigger health checks (should be skipped)
        await (componentManager as any)._performHealthChecks();

        // Verify operation counts unchanged
        for (const [componentId, status] of statuses) {
          expect(status.metrics.operationCount).toBe(initialCounts.get(componentId));
        }
      }, INTEGRATION_TEST_TIMEOUT);

      it('should handle health check errors gracefully', async () => {
        await componentManager.initialize();

        // Force an error by corrupting component instances
        const originalInstances = (componentManager as any)._componentInstances;
        (componentManager as any)._componentInstances = new Map([
          ['error-component', { getHealthStatus: () => { throw new Error('Health check failed'); } }]
        ]);

        // Add status for error component
        (componentManager as any)._componentStatuses.set('error-component', {
          id: 'error-component',
          name: 'Error Component',
          category: 'test',
          status: 'healthy',
          healthScore: 100,
          lastUpdate: new Date(),
          metrics: {
            responseTime: 0,
            operationCount: 0,
            errorCount: 0
          }
        });

        // Perform health checks (should not throw)
        await expect(
          (componentManager as any)._performHealthChecks()
        ).resolves.not.toThrow();

        // Restore state
        (componentManager as any)._componentInstances = originalInstances;
      }, INTEGRATION_TEST_TIMEOUT);

      it('should update dashboard data after health checks', async () => {
        await componentManager.initialize();

        // Get initial dashboard data
        const initialData = componentManager.getDashboardData();

        // Manually trigger health checks
        await (componentManager as any)._performHealthChecks();

        // Get updated dashboard data
        const updatedData = componentManager.getDashboardData();

        // Verify dashboard data was updated
        expect(updatedData).toBeDefined();
        expect(updatedData.totalComponents).toBe(initialData.totalComponents);
      }, INTEGRATION_TEST_TIMEOUT);
    });

    describe('_checkComponentHealth() Method', () => {
      it('should check component health with getHealthStatus method', async () => {
        await componentManager.initialize();

        // Get a component with health check method using getAllComponents()
        const allComponents = componentManager.getAllComponents();
        const componentId = allComponents[0]?.id;
        const instance = (componentManager as any)._componentInstances.get(componentId);

        // Check component health
        await (componentManager as any)._checkComponentHealth(componentId, instance);

        // Verify status updated
        const status = (componentManager as any)._componentStatuses.get(componentId);
        expect(status).toBeDefined();
        expect(status.metrics.responseTime).toBeGreaterThanOrEqual(0);
        expect(status.lastUpdate).toBeInstanceOf(Date);
      }, INTEGRATION_TEST_TIMEOUT);

      it('should handle component without status gracefully', async () => {
        await componentManager.initialize();

        // Check health for non-existent component
        await expect(
          (componentManager as any)._checkComponentHealth('non-existent', {})
        ).resolves.not.toThrow();
      }, INTEGRATION_TEST_TIMEOUT);

      it('should mark component as error when health check fails', async () => {
        await componentManager.initialize();

        // Create component with failing health check
        const failingInstance = {
          getHealthStatus: () => { throw new Error('Health check failed'); }
        };

        // Add status for failing component
        const componentId = 'failing-component';
        (componentManager as any)._componentStatuses.set(componentId, {
          id: componentId,
          name: 'Failing Component',
          category: 'test',
          status: 'healthy',
          healthScore: 100,
          lastUpdate: new Date(),
          metrics: {
            responseTime: 0,
            operationCount: 0,
            errorCount: 0
          }
        });

        // Check component health
        await (componentManager as any)._checkComponentHealth(componentId, failingInstance);

        // Verify component marked as error
        const status = (componentManager as any)._componentStatuses.get(componentId);
        expect(status.status).toBe('error');
        expect(status.healthScore).toBe(0);
      }, INTEGRATION_TEST_TIMEOUT);

      it('should handle different health status formats', async () => {
        await componentManager.initialize();

        // Test format: { status: 'healthy' }
        const instance1 = {
          getHealthStatus: async () => ({ status: 'healthy' })
        };

        const componentId1 = 'test-component-1';
        (componentManager as any)._componentStatuses.set(componentId1, {
          id: componentId1,
          name: 'Test Component 1',
          category: 'test',
          status: 'healthy',
          healthScore: 100,
          lastUpdate: new Date(),
          metrics: { responseTime: 0, operationCount: 0, errorCount: 0 }
        });

        await (componentManager as any)._checkComponentHealth(componentId1, instance1);
        let status = (componentManager as any)._componentStatuses.get(componentId1);
        expect(status.status).toBe('healthy');

        // Test format: { healthy: true }
        const instance2 = {
          getHealthStatus: async () => ({ healthy: true })
        };

        const componentId2 = 'test-component-2';
        (componentManager as any)._componentStatuses.set(componentId2, {
          id: componentId2,
          name: 'Test Component 2',
          category: 'test',
          status: 'healthy',
          healthScore: 100,
          lastUpdate: new Date(),
          metrics: { responseTime: 0, operationCount: 0, errorCount: 0 }
        });

        await (componentManager as any)._checkComponentHealth(componentId2, instance2);
        status = (componentManager as any)._componentStatuses.get(componentId2);
        expect(status.status).toBe('healthy');
      }, INTEGRATION_TEST_TIMEOUT);

      it('should mark component as warning when response time exceeds warning threshold', async () => {
        // Create manager with low warning threshold
        const config = {
          enableRealTimeUpdates: true,
          updateInterval: 5000,
          healthCheckInterval: 10000,
          performanceThresholds: {
            responseTimeWarning: 10, // Very low threshold
            responseTimeError: 5000,
            memoryUsageWarning: 0.7,
            memoryUsageError: 0.9
          }
        };

        const testManager = new M0ComponentManager(config);
        await testManager.initialize();

        // Create component with slow health check
        const slowInstance = {
          getHealthStatus: async () => {
            await new Promise(resolve => setTimeout(resolve, 50)); // 50ms delay
            return { status: 'healthy' };
          }
        };

        const componentId = 'slow-component';
        (testManager as any)._componentStatuses.set(componentId, {
          id: componentId,
          name: 'Slow Component',
          category: 'test',
          status: 'healthy',
          healthScore: 100,
          lastUpdate: new Date(),
          metrics: { responseTime: 0, operationCount: 0, errorCount: 0 }
        });

        // Check component health
        await (testManager as any)._checkComponentHealth(componentId, slowInstance);

        // Verify component marked as warning
        const status = (testManager as any)._componentStatuses.get(componentId);
        expect(status.status).toBe('warning');
        expect(status.healthScore).toBe(75);
        expect(status.metrics.responseTime).toBeGreaterThan(10);

        await testManager.shutdown();
      }, INTEGRATION_TEST_TIMEOUT);

      it('should mark component as error when response time exceeds error threshold', async () => {
        // Create manager with low error threshold
        const config = {
          enableRealTimeUpdates: true,
          updateInterval: 5000,
          healthCheckInterval: 10000,
          performanceThresholds: {
            responseTimeWarning: 10,
            responseTimeError: 20, // Very low threshold
            memoryUsageWarning: 0.7,
            memoryUsageError: 0.9
          }
        };

        const testManager = new M0ComponentManager(config);
        await testManager.initialize();

        // Create component with very slow health check
        const verySlowInstance = {
          getHealthStatus: async () => {
            await new Promise(resolve => setTimeout(resolve, 100)); // 100ms delay
            return { status: 'healthy' };
          }
        };

        const componentId = 'very-slow-component';
        (testManager as any)._componentStatuses.set(componentId, {
          id: componentId,
          name: 'Very Slow Component',
          category: 'test',
          status: 'healthy',
          healthScore: 100,
          lastUpdate: new Date(),
          metrics: { responseTime: 0, operationCount: 0, errorCount: 0 }
        });

        // Check component health
        await (testManager as any)._checkComponentHealth(componentId, verySlowInstance);

        // Verify component marked as error
        const status = (testManager as any)._componentStatuses.get(componentId);
        expect(status.status).toBe('error');
        expect(status.healthScore).toBe(25);
        expect(status.metrics.responseTime).toBeGreaterThan(20);

        await testManager.shutdown();
      }, INTEGRATION_TEST_TIMEOUT);

      it('should handle health check outer catch block', async () => {
        await componentManager.initialize();

        // Create component that throws during metrics update
        const componentId = 'error-component';

        // Create a status object that throws when accessing metrics
        const errorStatus = {
          id: componentId,
          name: 'Error Component',
          category: 'test',
          status: 'healthy',
          healthScore: 100,
          lastUpdate: new Date(),
          get metrics() {
            throw new Error('Metrics access error');
          }
        };

        (componentManager as any)._componentStatuses.set(componentId, errorStatus);

        // This should trigger the outer catch block
        await (componentManager as any)._checkComponentHealth(componentId, {});

        // The error status object will throw, so we can't verify the result
        // But the test should not throw an error
      }, INTEGRATION_TEST_TIMEOUT);

      it('should handle non-object health status response', async () => {
        await componentManager.initialize();

        // Create component that returns non-object health status
        const instance = {
          getHealthStatus: async () => 'healthy' // String instead of object
        };

        const componentId = 'non-object-response-component';
        (componentManager as any)._componentStatuses.set(componentId, {
          id: componentId,
          name: 'Non-Object Response Component',
          category: 'test',
          status: 'healthy',
          healthScore: 100,
          lastUpdate: new Date(),
          metrics: { responseTime: 0, operationCount: 0, errorCount: 0 }
        });

        // Check component health
        await (componentManager as any)._checkComponentHealth(componentId, instance);

        // Verify component marked as healthy (non-object response assumes healthy)
        const status = (componentManager as any)._componentStatuses.get(componentId);
        expect(status.status).toBe('healthy');
        expect(status.healthScore).toBe(100);
      }, INTEGRATION_TEST_TIMEOUT);

      it('should handle unknown health status format', async () => {
        await componentManager.initialize();

        // Create component that returns unknown format
        const instance = {
          getHealthStatus: async () => ({ someOtherProperty: 'value' })
        };

        const componentId = 'unknown-format-component';
        (componentManager as any)._componentStatuses.set(componentId, {
          id: componentId,
          name: 'Unknown Format Component',
          category: 'test',
          status: 'healthy',
          healthScore: 100,
          lastUpdate: new Date(),
          metrics: { responseTime: 0, operationCount: 0, errorCount: 0 }
        });

        // Check component health
        await (componentManager as any)._checkComponentHealth(componentId, instance);

        // Verify component marked as healthy (unknown format assumes healthy)
        const status = (componentManager as any)._componentStatuses.get(componentId);
        expect(status.status).toBe('healthy');
        expect(status.healthScore).toBe(100);
      }, INTEGRATION_TEST_TIMEOUT);

      it('should handle health status with status property set to error', async () => {
        await componentManager.initialize();

        // Create component that returns error status
        const instance = {
          getHealthStatus: async () => ({ status: 'error' })
        };

        const componentId = 'error-status-component';
        (componentManager as any)._componentStatuses.set(componentId, {
          id: componentId,
          name: 'Error Status Component',
          category: 'test',
          status: 'healthy',
          healthScore: 100,
          lastUpdate: new Date(),
          metrics: { responseTime: 0, operationCount: 0, errorCount: 0 }
        });

        // Check component health
        await (componentManager as any)._checkComponentHealth(componentId, instance);

        // Verify component marked as error
        const status = (componentManager as any)._componentStatuses.get(componentId);
        expect(status.status).toBe('error');
        expect(status.healthScore).toBe(0);
      }, INTEGRATION_TEST_TIMEOUT);

      it('should handle health status with healthy property set to false', async () => {
        await componentManager.initialize();

        // Create component that returns healthy: false
        const instance = {
          getHealthStatus: async () => ({ healthy: false })
        };

        const componentId = 'unhealthy-component';
        (componentManager as any)._componentStatuses.set(componentId, {
          id: componentId,
          name: 'Unhealthy Component',
          category: 'test',
          status: 'healthy',
          healthScore: 100,
          lastUpdate: new Date(),
          metrics: { responseTime: 0, operationCount: 0, errorCount: 0 }
        });

        // Check component health
        await (componentManager as any)._checkComponentHealth(componentId, instance);

        // Verify component marked as error
        const status = (componentManager as any)._componentStatuses.get(componentId);
        expect(status.status).toBe('error');
        expect(status.healthScore).toBe(0);
      }, INTEGRATION_TEST_TIMEOUT);
    });

    describe('_startMonitoring() Method', () => {
      it('should log info when monitoring is disabled by configuration', async () => {
        // Create manager with monitoring disabled
        const config = {
          enableRealTimeUpdates: false,
          updateInterval: 5000,
          healthCheckInterval: 10000,
          performanceThresholds: {
            responseTimeWarning: 1000,
            responseTimeError: 5000,
            memoryUsageWarning: 0.7,
            memoryUsageError: 0.9
          }
        };

        const testManager = new M0ComponentManager(config);

        // Manually call _startMonitoring
        await (testManager as any)._startMonitoring();

        // Verify monitoring not started
        expect((testManager as any)._isMonitoring).toBe(false);

        await testManager.shutdown();
      }, INTEGRATION_TEST_TIMEOUT);

      it('should create safe intervals for health checks and status updates', async () => {
        await componentManager.initialize();

        // Verify monitoring started
        expect((componentManager as any)._isMonitoring).toBe(true);

        // Verify intervals were created (they should be in the timer registry)
        // This is implicitly tested by the fact that monitoring works
      }, INTEGRATION_TEST_TIMEOUT);
    });
  });

  // ============================================================================
  // TEST SUITE 10: ADDITIONAL COVERAGE TESTS
  // ============================================================================

  describe('Additional Coverage Tests', () => {
    describe('getServiceVersion() Method', () => {
      it('should return correct version number', () => {
        const version = (componentManager as any).getServiceVersion();
        expect(version).toBe('1.0.0');
      });
    });

    describe('Singleton Pattern', () => {
      it('should create and return singleton instance', async () => {
        // Import the singleton function
        const { getM0ComponentManager } = await import('../../src/lib/M0ComponentManager');

        // Get singleton instance
        const instance1 = await getM0ComponentManager();
        expect(instance1).toBeDefined();

        // Get singleton instance again
        const instance2 = await getM0ComponentManager();
        expect(instance2).toBe(instance1); // Should be same instance

        // Cleanup
        await instance1.shutdown();
      }, INTEGRATION_TEST_TIMEOUT);
    });

    describe('Edge Cases for Uncovered Lines', () => {
      it('should handle _updateAllComponentStatuses with Promise.allSettled', async () => {
        await componentManager.initialize();

        // Manually trigger status updates
        await (componentManager as any)._updateAllComponentStatuses();

        // Verify all components have updated statuses
        const statuses = (componentManager as any)._componentStatuses;
        for (const [componentId, status] of statuses) {
          expect(status.lastUpdate).toBeInstanceOf(Date);
        }
      }, INTEGRATION_TEST_TIMEOUT);

      it('should handle component without getHealthStatus method', async () => {
        await componentManager.initialize();

        // Create component without health check method
        const componentId = 'no-health-check-component';
        const instance = {}; // No getHealthStatus method

        (componentManager as any)._componentStatuses.set(componentId, {
          id: componentId,
          name: 'No Health Check Component',
          category: 'test',
          status: 'healthy',
          healthScore: 100,
          lastUpdate: new Date(),
          metrics: { responseTime: 0, operationCount: 0, errorCount: 0 }
        });

        // Check component health
        await (componentManager as any)._checkComponentHealth(componentId, instance);

        // Verify component marked as healthy (no health check = assume healthy)
        const status = (componentManager as any)._componentStatuses.get(componentId);
        expect(status.status).toBe('healthy');
        expect(status.healthScore).toBe(100);
      }, INTEGRATION_TEST_TIMEOUT);
    });
  });
});



