/**
 * @file BaseTrackingService Lifecycle Integration Test
 * @filepath demos/m0-real-dashboard/__tests__/integration/BaseTrackingService.lifecycle.test.ts
 * @component BaseTrackingService-Lifecycle-Tests
 * @authority President & CEO, <PERSON><PERSON><PERSON><PERSON> Consultancy
 * @purpose Validate BaseTrackingService lifecycle management across all components
 * @created 2025-10-19
 * @status INTEGRATION TESTING
 * 
 * @description
 * Comprehensive lifecycle testing for BaseTrackingService components:
 * - Initialization patterns (doInitialize hooks)
 * - Shutdown patterns (doShutdown hooks)
 * - Resource cleanup validation
 * - Timer management verification
 * - Memory safety compliance
 * 
 * 🎯 SUCCESS CRITERIA:
 * - All BaseTrackingService components initialize correctly
 * - All components cleanup resources during shutdown
 * - Zero memory leaks after shutdown
 * - Timer coordination works correctly
 */

import { M0ComponentManager } from '../../src/lib/M0ComponentManager';

const TEST_TIMEOUT = 60000;

describe('BaseTrackingService Lifecycle Integration Tests', () => {
  let componentManager: M0ComponentManager;

  beforeEach(() => {
    componentManager = new M0ComponentManager();
  });

  afterEach(async () => {
    if (componentManager) {
      await componentManager.shutdown();
    }
  });

  describe('Initialization Lifecycle', () => {
    it('should initialize all BaseTrackingService components using doInitialize hooks', async () => {
      await componentManager.initialize();

      const dashboardData = componentManager.getDashboardData();

      // Verify all components initialized successfully
      expect(dashboardData.totalComponents).toBe(87);
      expect(dashboardData.healthyComponents).toBe(87);

      // Verify governance components (many extend BaseTrackingService)
      const governanceComponents = dashboardData.categories.governance;
      expect(governanceComponents.length).toBe(40);
      governanceComponents.forEach(component => {
        expect(component.status).toBe('healthy');
      });

      // Verify tracking components (all extend BaseTrackingService)
      const trackingComponents = dashboardData.categories.tracking;
      expect(trackingComponents.length).toBe(21);
      trackingComponents.forEach(component => {
        expect(component.status).toBe('healthy');
      });
    }, TEST_TIMEOUT);

    it('should handle initialization errors gracefully', async () => {
      // This test verifies that if one component fails, others still initialize
      // In production, M0ComponentManager should handle errors gracefully
      
      await componentManager.initialize();

      const dashboardData = componentManager.getDashboardData();

      // Even with potential errors, system should maintain stability
      expect(dashboardData.totalComponents).toBeGreaterThan(0);
      expect(dashboardData.overallHealthScore).toBeGreaterThanOrEqual(0);
    }, TEST_TIMEOUT);

    it('should initialize components with proper dependency order', async () => {
      await componentManager.initialize();

      const dashboardData = componentManager.getDashboardData();

      // Verify memory-safety components initialized before integration components
      const memorySafetyComponents = dashboardData.categories.memorySafety;
      const integrationComponents = dashboardData.categories.integration;

      expect(memorySafetyComponents.length).toBe(14);
      expect(integrationComponents.length).toBe(12);

      // All components should be healthy
      [...memorySafetyComponents, ...integrationComponents].forEach(component => {
        expect(component.healthScore).toBe(100);
      });
    }, TEST_TIMEOUT);
  });

  describe('Shutdown Lifecycle', () => {
    beforeEach(async () => {
      await componentManager.initialize();
    });

    it('should shutdown all components using doShutdown hooks', async () => {
      const dashboardDataBefore = componentManager.getDashboardData();
      expect(dashboardDataBefore.totalComponents).toBe(87);

      await componentManager.shutdown();

      // Verify all components are cleaned up
      const componentInstances = (componentManager as any)._componentInstances;
      const componentStatuses = (componentManager as any)._componentStatuses;

      expect(componentInstances).toBeDefined();
      expect(componentStatuses).toBeDefined();
      expect(componentInstances.size).toBe(0);
      expect(componentStatuses.size).toBe(0);
    }, TEST_TIMEOUT);

    it('should cleanup all timers during shutdown', async () => {
      const allComponentsBefore = componentManager.getAllComponents();

      // Verify timer coordination service is healthy before shutdown
      const timerCoordinator = allComponentsBefore.find(
        c => c.id === 'timer-coordination-service' || c.id === 'timer-coordination-service-enhanced'
      );

      expect(timerCoordinator).toBeDefined();
      expect(timerCoordinator?.status).toBe('healthy');

      await componentManager.shutdown();

      // After shutdown, all timers should be cleared
      const componentInstances = (componentManager as any)._componentInstances;
      const componentStatuses = (componentManager as any)._componentStatuses;

      expect(componentInstances).toBeDefined();
      expect(componentStatuses).toBeDefined();
      expect(componentInstances.size).toBe(0);
      expect(componentStatuses.size).toBe(0);
    }, TEST_TIMEOUT);

    it('should cleanup resources without memory leaks', async () => {
      const initialMemory = process.memoryUsage().heapUsed;

      // Initialize and shutdown 3 times
      for (let i = 0; i < 3; i++) {
        await componentManager.initialize();
        await componentManager.shutdown();
        
        if (i < 2) {
          componentManager = new M0ComponentManager();
        }
      }

      // Force garbage collection
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryGrowth = finalMemory - initialMemory;

      // Memory growth should be minimal (< 30MB for 3 cycles)
      expect(memoryGrowth).toBeLessThan(30 * 1024 * 1024);
    }, TEST_TIMEOUT);
  });

  describe('Resource Management', () => {
    beforeEach(async () => {
      await componentManager.initialize();
    });

    it('should manage intervals using createSafeInterval pattern', async () => {
      // Verify all components have valid metrics structure
      const allComponents = componentManager.getAllComponents();

      expect(allComponents.length).toBe(87);

      // All components should have metrics defined (even if operationCount is 0)
      allComponents.forEach(component => {
        expect(component.metrics).toBeDefined();
        expect(component.metrics.responseTime).toBeGreaterThanOrEqual(0);
        expect(component.metrics.errorRate).toBeGreaterThanOrEqual(0);
        expect(component.metrics.memoryUsage).toBeGreaterThanOrEqual(0);
        expect(component.metrics.operationCount).toBeGreaterThanOrEqual(0);
        expect(component.healthScore).toBe(100);
      });
    }, TEST_TIMEOUT);

    it('should track resource usage across all components', async () => {
      // Verify all components track resource usage through metrics
      const allComponents = componentManager.getAllComponents();

      expect(allComponents.length).toBe(87);

      // Verify dashboard data aggregates resource usage
      const dashboardData = componentManager.getDashboardData();
      expect(dashboardData.systemMetrics).toBeDefined();
      expect(dashboardData.systemMetrics.totalMemoryUsage).toBeGreaterThanOrEqual(0);
      expect(dashboardData.systemMetrics.averageResponseTime).toBeGreaterThanOrEqual(0);
      expect(dashboardData.systemMetrics.totalOperations).toBeGreaterThanOrEqual(0);
      expect(dashboardData.systemMetrics.errorRate).toBeGreaterThanOrEqual(0);
    }, TEST_TIMEOUT);

    it('should prevent resource leaks during extended operation', async () => {
      const memorySnapshots: number[] = [];

      // Take 5 memory snapshots
      for (let i = 0; i < 5; i++) {
        componentManager.getDashboardData();

        if (global.gc) {
          global.gc();
        }

        memorySnapshots.push(process.memoryUsage().heapUsed);
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Verify memory is stable
      const firstSnapshot = memorySnapshots[0];
      const lastSnapshot = memorySnapshots[memorySnapshots.length - 1];
      const memoryGrowth = lastSnapshot - firstSnapshot;

      // Memory growth should be minimal (< 5MB)
      expect(memoryGrowth).toBeLessThan(5 * 1024 * 1024);
    }, TEST_TIMEOUT);
  });

  describe('Error Handling & Recovery', () => {
    it('should recover from initialization errors', async () => {
      // First initialization
      await componentManager.initialize();
      const firstData = componentManager.getDashboardData();
      expect(firstData.totalComponents).toBe(87);

      // Shutdown
      await componentManager.shutdown();

      // Re-initialize
      componentManager = new M0ComponentManager();
      await componentManager.initialize();
      const secondData = componentManager.getDashboardData();

      // Should have same component count
      expect(secondData.totalComponents).toBe(firstData.totalComponents);
      expect(secondData.overallHealthScore).toBe(100);
    }, TEST_TIMEOUT);

    it('should handle concurrent initialization attempts gracefully', async () => {
      // Attempt concurrent initializations (should be handled gracefully)
      const promises = [
        componentManager.initialize(),
        componentManager.initialize(),
        componentManager.initialize()
      ];

      await Promise.all(promises);

      const dashboardData = componentManager.getDashboardData();
      expect(dashboardData.totalComponents).toBe(87);
      expect(dashboardData.overallHealthScore).toBe(100);
    }, TEST_TIMEOUT);
  });

  describe('Performance Validation', () => {
    beforeEach(async () => {
      await componentManager.initialize();
    });

    it('should initialize all 87 components within acceptable time', async () => {
      const startTime = Date.now();
      
      componentManager = new M0ComponentManager();
      await componentManager.initialize();
      
      const endTime = Date.now();
      const initializationTime = endTime - startTime;

      // Initialization should complete within 30 seconds
      expect(initializationTime).toBeLessThan(30000);
    }, TEST_TIMEOUT);

    it('should shutdown all components within acceptable time', async () => {
      const startTime = Date.now();
      
      await componentManager.shutdown();
      
      const endTime = Date.now();
      const shutdownTime = endTime - startTime;

      // Shutdown should complete within 10 seconds
      expect(shutdownTime).toBeLessThan(10000);
    }, TEST_TIMEOUT);

    it('should maintain performance under load', async () => {
      const startTime = Date.now();

      // Execute 20 getDashboardData calls
      const promises = Array.from({ length: 20 }, () => 
        componentManager.getDashboardData()
      );

      await Promise.all(promises);

      const endTime = Date.now();
      const totalTime = endTime - startTime;

      // Should complete within 10 seconds
      expect(totalTime).toBeLessThan(10000);
    }, TEST_TIMEOUT);
  });
});

