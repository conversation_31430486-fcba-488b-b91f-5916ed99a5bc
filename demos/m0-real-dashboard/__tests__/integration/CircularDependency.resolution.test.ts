/**
 * @file Circular Dependency Resolution Integration Test
 * @filepath demos/m0-real-dashboard/__tests__/integration/CircularDependency.resolution.test.ts
 * @component CircularDependency-Resolution-Tests
 * @authority President & CEO, <PERSON><PERSON><PERSON><PERSON> Consultancy
 * @purpose Validate circular dependency fix (CleanupEnums extraction) works correctly
 * @created 2025-10-19
 * @status INTEGRATION TESTING
 * 
 * @description
 * Validates the circular dependency resolution implemented to fix:
 * - CleanupPriority enum circular dependency error
 * - "Cannot read properties of undefined (reading 'NORMAL')" error
 * - CleanupEnums extraction to standalone file
 * - Backward compatibility with re-exports
 * 
 * 🎯 SUCCESS CRITERIA:
 * - CleanupCoordinatorEnhanced initializes without errors
 * - MemorySafetyManager initializes without errors
 * - MemorySafetyManagerEnhanced initializes without errors
 * - All three components work together correctly
 * - Zero circular dependency errors
 */

import { M0ComponentManager } from '../../src/lib/M0ComponentManager';

const TEST_TIMEOUT = 60000;

describe('Circular Dependency Resolution Integration Tests', () => {
  let componentManager: M0ComponentManager;

  beforeEach(() => {
    componentManager = new M0ComponentManager();
  });

  afterEach(async () => {
    if (componentManager) {
      await componentManager.shutdown();
    }
  });

  describe('CleanupEnums Extraction Validation', () => {
    it('should initialize CleanupCoordinatorEnhanced without circular dependency errors', async () => {
      await componentManager.initialize();

      const dashboardData = componentManager.getDashboardData();
      const allComponents = componentManager.getAllComponents();

      // Find CleanupCoordinatorEnhanced component
      const cleanupCoordinator = allComponents.find(
        c => c.id === 'cleanup-coordinator-enhanced'
      );

      expect(cleanupCoordinator).toBeDefined();
      expect(cleanupCoordinator?.status).toBe('healthy');
      expect(cleanupCoordinator?.healthScore).toBe(100);
      expect(cleanupCoordinator?.category).toBe('memory-safety');
    }, TEST_TIMEOUT);

    it('should initialize MemorySafetyManager without circular dependency errors', async () => {
      await componentManager.initialize();

      const allComponents = componentManager.getAllComponents();

      // Find MemorySafetyManager component
      const memorySafetyManager = allComponents.find(
        c => c.id === 'memory-safety-manager'
      );

      expect(memorySafetyManager).toBeDefined();
      expect(memorySafetyManager?.status).toBe('healthy');
      expect(memorySafetyManager?.healthScore).toBe(100);
      expect(memorySafetyManager?.category).toBe('memory-safety');
    }, TEST_TIMEOUT);

    it('should initialize MemorySafetyManagerEnhanced without circular dependency errors', async () => {
      await componentManager.initialize();

      const allComponents = componentManager.getAllComponents();

      // Find MemorySafetyManagerEnhanced component
      const memorySafetyManagerEnhanced = allComponents.find(
        c => c.id === 'memory-safety-manager-enhanced'
      );

      expect(memorySafetyManagerEnhanced).toBeDefined();
      expect(memorySafetyManagerEnhanced?.status).toBe('healthy');
      expect(memorySafetyManagerEnhanced?.healthScore).toBe(100);
      expect(memorySafetyManagerEnhanced?.category).toBe('memory-safety');
    }, TEST_TIMEOUT);

    it('should initialize all three components together without conflicts', async () => {
      await componentManager.initialize();

      const allComponents = componentManager.getAllComponents();

      // Find all three components
      const cleanupCoordinator = allComponents.find(
        c => c.id === 'cleanup-coordinator-enhanced'
      );
      const memorySafetyManager = allComponents.find(
        c => c.id === 'memory-safety-manager'
      );
      const memorySafetyManagerEnhanced = allComponents.find(
        c => c.id === 'memory-safety-manager-enhanced'
      );

      // Verify all three are initialized and healthy
      expect(cleanupCoordinator).toBeDefined();
      expect(memorySafetyManager).toBeDefined();
      expect(memorySafetyManagerEnhanced).toBeDefined();

      expect(cleanupCoordinator?.healthScore).toBe(100);
      expect(memorySafetyManager?.healthScore).toBe(100);
      expect(memorySafetyManagerEnhanced?.healthScore).toBe(100);
    }, TEST_TIMEOUT);
  });

  describe('Backward Compatibility Validation', () => {
    it('should maintain backward compatibility with re-exports', async () => {
      await componentManager.initialize();

      const dashboardData = componentManager.getDashboardData();

      // Verify all memory-safety components are healthy
      const memorySafetyComponents = dashboardData.categories.memorySafety;
      expect(memorySafetyComponents.length).toBe(14);

      memorySafetyComponents.forEach(component => {
        expect(component.healthScore).toBe(100);
        expect(component.status).toBe('healthy');
      });
    }, TEST_TIMEOUT);

    it('should work correctly across multiple initialization cycles', async () => {
      // First cycle
      await componentManager.initialize();
      const firstData = await componentManager.getDashboardData();
      await componentManager.shutdown();

      // Second cycle
      componentManager = new M0ComponentManager();
      await componentManager.initialize();
      const secondData = await componentManager.getDashboardData();
      await componentManager.shutdown();

      // Third cycle
      componentManager = new M0ComponentManager();
      await componentManager.initialize();
      const thirdData = await componentManager.getDashboardData();

      // Verify consistency across cycles
      expect(firstData.totalComponents).toBe(87);
      expect(secondData.totalComponents).toBe(87);
      expect(thirdData.totalComponents).toBe(87);

      expect(firstData.overallHealthScore).toBe(100);
      expect(secondData.overallHealthScore).toBe(100);
      expect(thirdData.overallHealthScore).toBe(100);
    }, TEST_TIMEOUT);
  });

  describe('Production Scenario Validation', () => {
    it('should handle concurrent requests without circular dependency errors', async () => {
      await componentManager.initialize();

      // Execute 30 concurrent requests
      const promises = Array.from({ length: 30 }, () => 
        componentManager.getDashboardData()
      );

      const results = await Promise.all(promises);

      // Verify all results are consistent
      results.forEach(result => {
        const cleanupCoordinator = componentManager.getAllComponents().find(
          c => c.id === 'cleanup-coordinator-enhanced'
        );
        const memorySafetyManager = componentManager.getAllComponents().find(
          c => c.id === 'memory-safety-manager'
        );
        const memorySafetyManagerEnhanced = componentManager.getAllComponents().find(
          c => c.id === 'memory-safety-manager-enhanced'
        );

        expect(cleanupCoordinator?.healthScore).toBe(100);
        expect(memorySafetyManager?.healthScore).toBe(100);
        expect(memorySafetyManagerEnhanced?.healthScore).toBe(100);
      });
    }, TEST_TIMEOUT);

    it('should maintain stability under load', async () => {
      await componentManager.initialize();

      // Execute 100 sequential requests
      for (let i = 0; i < 100; i++) {
        const result = await componentManager.getDashboardData();

        const cleanupCoordinator = componentManager.getAllComponents().find(
          c => c.id === 'cleanup-coordinator-enhanced'
        );
        const memorySafetyManager = componentManager.getAllComponents().find(
          c => c.id === 'memory-safety-manager'
        );
        const memorySafetyManagerEnhanced = componentManager.getAllComponents().find(
          c => c.id === 'memory-safety-manager-enhanced'
        );

        expect(cleanupCoordinator?.healthScore).toBe(100);
        expect(memorySafetyManager?.healthScore).toBe(100);
        expect(memorySafetyManagerEnhanced?.healthScore).toBe(100);
      }
    }, TEST_TIMEOUT);

    it('should not throw "Cannot read properties of undefined" errors', async () => {
      // This test specifically validates the original error is fixed
      let errorThrown = false;
      let errorMessage = '';

      try {
        await componentManager.initialize();
        await componentManager.getDashboardData();
      } catch (error) {
        errorThrown = true;
        errorMessage = error instanceof Error ? error.message : String(error);
      }

      expect(errorThrown).toBe(false);
      expect(errorMessage).not.toContain('Cannot read properties of undefined');
      expect(errorMessage).not.toContain('NORMAL');
    }, TEST_TIMEOUT);
  });

  describe('Memory Safety Integration', () => {
    beforeEach(async () => {
      await componentManager.initialize();
    });

    it('should integrate CleanupCoordinatorEnhanced with other memory-safety components', async () => {
      const dashboardData = componentManager.getDashboardData();

      // Verify all memory-safety components work together
      const memorySafetyComponents = dashboardData.categories.memorySafety;
      expect(memorySafetyComponents.length).toBe(14);

      // Find key components
      const cleanupCoordinator = memorySafetyComponents.find(
        c => c.id === 'cleanup-coordinator-enhanced'
      );
      const timerCoordinator = memorySafetyComponents.find(
        c => c.id === 'timer-coordination-service'
      );
      const eventHandlerRegistry = memorySafetyComponents.find(
        c => c.id === 'event-handler-registry'
      );

      expect(cleanupCoordinator).toBeDefined();
      expect(timerCoordinator).toBeDefined();
      expect(eventHandlerRegistry).toBeDefined();

      // All should be healthy
      expect(cleanupCoordinator?.healthScore).toBe(100);
      expect(timerCoordinator?.healthScore).toBe(100);
      expect(eventHandlerRegistry?.healthScore).toBe(100);
    }, TEST_TIMEOUT);

    it('should cleanup resources correctly during shutdown', async () => {
      const dashboardDataBefore = await componentManager.getDashboardData();
      expect(dashboardDataBefore.totalComponents).toBe(87);

      await componentManager.shutdown();

      // Verify all components are cleaned up
      const componentInstances = (componentManager as any)._componentInstances;
      const componentStatuses = (componentManager as any)._componentStatuses;

      expect(componentInstances).toBeDefined();
      expect(componentStatuses).toBeDefined();
      expect(componentInstances.size).toBe(0);
      expect(componentStatuses.size).toBe(0);
    }, TEST_TIMEOUT);
  });
});

