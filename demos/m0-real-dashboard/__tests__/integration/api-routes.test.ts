/**
 * ============================================================================
 * API Routes Integration Tests - Priority 2 Phase 3
 * ============================================================================
 * 
 * Comprehensive tests for all specialized API routes:
 * - /api/m0-governance
 * - /api/m0-tracking
 * - /api/m0-security
 * - /api/m0-integration
 * 
 * Tests cover:
 * - GET requests with category filtering
 * - Response format consistency
 * - Category-specific metrics
 * - Error handling
 * - POST operations (refresh functionality)
 * 
 * Author: AI Assistant (Priority 2 - Phase 3 Implementation)
 * Created: 2025-10-21
 * Authority: President & CEO, E.Z. Consultancy
 * ============================================================================
 */

import { GET as governanceGET, POST as governancePOST } from '../../src/app/api/m0-governance/route';
import { GET as trackingGET, POST as trackingPOST } from '../../src/app/api/m0-tracking/route';
import { GET as securityGET, POST as securityPOST } from '../../src/app/api/m0-security/route';
import { GET as integrationGET, POST as integrationPOST } from '../../src/app/api/m0-integration/route';
import { NextRequest } from 'next/server';

// ============================================================================
// TEST SETUP
// ============================================================================

describe('API Routes Integration Tests - Priority 2', () => {
  
  // ============================================================================
  // GOVERNANCE API TESTS
  // ============================================================================
  
  describe('/api/m0-governance', () => {

    it('should return governance component data with correct structure', async () => {
      const request = new NextRequest('http://localhost:3000/api/m0-governance');
      const response = await governanceGET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toBeDefined();
      expect(data.data.totalGovernanceComponents).toBeGreaterThan(0);
      expect(Array.isArray(data.data.components)).toBe(true);
      expect(data.timestamp).toBeDefined();
    });

    it('should include governance-specific metrics', async () => {
      const request = new NextRequest('http://localhost:3000/api/m0-governance');
      const response = await governanceGET(request);
      const data = await response.json();

      expect(data.data.metrics).toBeDefined();
      expect(data.data.metrics.complianceScore).toBeDefined();
      expect(data.data.metrics.ruleCount).toBeDefined();
      expect(data.data.metrics.violationCount).toBeDefined();
      expect(data.data.metrics.frameworksActive).toBeDefined();
    });

    it('should categorize governance components correctly', async () => {
      const request = new NextRequest('http://localhost:3000/api/m0-governance');
      const response = await governanceGET(request);
      const data = await response.json();

      const components = data.data.components;
      expect(components.length).toBeGreaterThan(0);

      // Check that all components have governance type
      components.forEach((component: { governanceType: string }) => {
        expect(['rule-engine', 'compliance', 'framework', 'analytics', 'reporting'])
          .toContain(component.governanceType);
      });
    });

    it('should handle POST refresh operation', async () => {
      const request = new NextRequest('http://localhost:3000/api/m0-governance', {
        method: 'POST',
        body: JSON.stringify({ operation: 'compliance-check', componentId: 'test-component', parameters: {} })
      });

      const response = await governancePOST(request);
      const data = await response.json();

      expect(response.status).toBeGreaterThanOrEqual(200);
      expect(data.success).toBeDefined();
    });
  });
  
  // ============================================================================
  // TRACKING API TESTS
  // ============================================================================
  
  describe('/api/m0-tracking', () => {

    it('should return tracking component data with correct structure', async () => {
      const request = new NextRequest('http://localhost:3000/api/m0-tracking');
      const response = await trackingGET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toBeDefined();
      expect(data.data.totalTrackingComponents).toBeGreaterThan(0);
      expect(Array.isArray(data.data.components)).toBe(true);
      expect(data.timestamp).toBeDefined();
    });

    it('should include tracking-specific metrics', async () => {
      const request = new NextRequest('http://localhost:3000/api/m0-tracking');
      const response = await trackingGET(request);
      const data = await response.json();

      expect(data.data.metrics).toBeDefined();
      expect(data.data.metrics.activeSessions).toBeDefined();
      expect(data.data.metrics.totalEvents).toBeDefined();
      expect(data.data.metrics.averageResponseTime).toBeDefined();
      expect(data.data.metrics.dataProcessingRate).toBeDefined();
    });

    it('should categorize tracking components correctly', async () => {
      const request = new NextRequest('http://localhost:3000/api/m0-tracking');
      const response = await trackingGET(request);
      const data = await response.json();

      const components = data.data.components;
      expect(components.length).toBeGreaterThan(0);

      // Check that all components have tracking type
      components.forEach((component: { trackingType: string }) => {
        expect(['session', 'analytics', 'orchestration', 'progress', 'data-management'])
          .toContain(component.trackingType);
      });
    });

    it('should handle POST operation', async () => {
      const request = new NextRequest('http://localhost:3000/api/m0-tracking', {
        method: 'POST',
        body: JSON.stringify({ operation: 'session-analysis', componentId: 'test-component', parameters: {} })
      });

      const response = await trackingPOST(request);
      const data = await response.json();

      expect(response.status).toBeGreaterThanOrEqual(200);
      expect(data.success).toBeDefined();
    });
  });
  
  // ============================================================================
  // SECURITY API TESTS
  // ============================================================================
  
  describe('/api/m0-security', () => {

    it('should return security component data with correct structure', async () => {
      const request = new NextRequest('http://localhost:3000/api/m0-security');
      const response = await securityGET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toBeDefined();
      expect(data.data.totalSecurityComponents).toBeGreaterThan(0);
      expect(Array.isArray(data.data.components)).toBe(true);
      expect(data.timestamp).toBeDefined();
    });

    it('should include security-specific metrics', async () => {
      const request = new NextRequest('http://localhost:3000/api/m0-security');
      const response = await securityGET(request);
      const data = await response.json();

      expect(data.data.metrics).toBeDefined();
      expect(data.data.metrics.memoryUsage).toBeDefined();
      expect(data.data.metrics.bufferUtilization).toBeDefined();
      expect(data.data.metrics.threatLevel).toBeDefined();
      expect(data.data.metrics.activeProtections).toBeDefined();
      expect(['low', 'medium', 'high']).toContain(data.data.metrics.threatLevel);
    });

    it('should categorize security components correctly', async () => {
      const request = new NextRequest('http://localhost:3000/api/m0-security');
      const response = await securityGET(request);
      const data = await response.json();

      const components = data.data.components;
      expect(components.length).toBeGreaterThan(0);

      // Check that all components have security type
      components.forEach((component: { securityType: string }) => {
        expect(['memory-management', 'buffer-protection', 'event-handling', 'environment-control'])
          .toContain(component.securityType);
      });
    });

    it('should handle POST operation', async () => {
      const request = new NextRequest('http://localhost:3000/api/m0-security', {
        method: 'POST',
        body: JSON.stringify({ operation: 'memory-scan', componentId: 'test-component', parameters: {} })
      });

      const response = await securityPOST(request);
      const data = await response.json();

      expect(response.status).toBeGreaterThanOrEqual(200);
      expect(data.success).toBeDefined();
    });
  });
  
  // ============================================================================
  // INTEGRATION API TESTS
  // ============================================================================
  
  describe('/api/m0-integration', () => {

    it('should return integration component data with correct structure', async () => {
      const request = new NextRequest('http://localhost:3000/api/m0-integration');
      const response = await integrationGET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toBeDefined();
      expect(data.data.totalIntegrationComponents).toBeGreaterThan(0);
      expect(Array.isArray(data.data.components)).toBe(true);
      expect(data.timestamp).toBeDefined();
    });

    it('should include integration-specific metrics', async () => {
      const request = new NextRequest('http://localhost:3000/api/m0-integration');
      const response = await integrationGET(request);
      const data = await response.json();

      expect(data.data.metrics).toBeDefined();
      expect(data.data.metrics.activeBridges).toBeDefined();
      expect(data.data.metrics.messagesThroughput).toBeDefined();
      expect(data.data.metrics.integrationHealth).toBeDefined();
      expect(data.data.metrics.crossComponentCalls).toBeDefined();
    });

    it('should categorize integration components correctly', async () => {
      const request = new NextRequest('http://localhost:3000/api/m0-integration');
      const response = await integrationGET(request);
      const data = await response.json();

      const components = data.data.components;
      expect(components.length).toBeGreaterThan(0);

      // Check that all components have integration type
      components.forEach((component: { integrationType: string }) => {
        expect(['bridge', 'coordinator', 'monitor', 'validator'])
          .toContain(component.integrationType);
      });
    });

    it('should handle POST operation', async () => {
      const request = new NextRequest('http://localhost:3000/api/m0-integration', {
        method: 'POST',
        body: JSON.stringify({ operation: 'bridge-test', componentId: 'test-component', parameters: {} })
      });

      const response = await integrationPOST(request);
      const data = await response.json();

      expect(response.status).toBeGreaterThanOrEqual(200);
      expect(data.success).toBeDefined();
    });
  });
  
  // ============================================================================
  // CROSS-CATEGORY CONSISTENCY TESTS
  // ============================================================================
  
  describe('Cross-Category Consistency', () => {

    it('should have consistent response format across all categories', async () => {
      const responses = await Promise.all([
        governanceGET(new NextRequest('http://localhost:3000/api/m0-governance')),
        trackingGET(new NextRequest('http://localhost:3000/api/m0-tracking')),
        securityGET(new NextRequest('http://localhost:3000/api/m0-security')),
        integrationGET(new NextRequest('http://localhost:3000/api/m0-integration'))
      ]);

      const dataArray = await Promise.all(responses.map(r => r.json()));

      dataArray.forEach(data => {
        expect(data.success).toBe(true);
        expect(data.data).toBeDefined();
        expect(data.data.healthyComponents).toBeDefined();
        expect(data.data.errorComponents).toBeDefined();
        expect(Array.isArray(data.data.components)).toBe(true);
        expect(data.data.metrics).toBeDefined();
        expect(data.timestamp).toBeDefined();
      });
    });

    it('should return all 136 components across all categories', async () => {
      const responses = await Promise.all([
        governanceGET(new NextRequest('http://localhost:3000/api/m0-governance')),
        trackingGET(new NextRequest('http://localhost:3000/api/m0-tracking')),
        securityGET(new NextRequest('http://localhost:3000/api/m0-security')),
        integrationGET(new NextRequest('http://localhost:3000/api/m0-integration'))
      ]);

      const dataArray = await Promise.all(responses.map(r => r.json()));

      const totalComponents =
        dataArray[0].data.totalGovernanceComponents +
        dataArray[1].data.totalTrackingComponents +
        dataArray[2].data.totalSecurityComponents +
        dataArray[3].data.totalIntegrationComponents;

      expect(totalComponents).toBe(136);
    });
  });
});

