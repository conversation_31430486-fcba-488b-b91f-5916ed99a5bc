/**
 * ============================================================================
 * OA FRAMEWORK - M0 REAL DASHBOARD
 * Violations List Component Tests
 * ============================================================================
 * 
 * @fileoverview Tests for ViolationsList component
 * @module __tests__/governance-dashboard/components/ViolationsList.test
 * @version 1.0.0
 * @since 2025-10-22
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2025 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ViolationsList } from '@/components/governance/ViolationsList';
import type { IGovernanceData } from '@/types/governance-types';

// ============================================================================
// MOCK DATA
// ============================================================================

const mockGovernanceData: IGovernanceData = {
  totalGovernanceComponents: 69,
  healthyComponents: 65,
  errorComponents: 2,
  filteredCount: 69,
  page: 1,
  limit: 50,
  totalPages: 2,
  metrics: {
    complianceScore: 85.5,
    ruleCount: 150,
    violationCount: 5,
    frameworksActive: 12,
    lastAudit: new Date().toISOString(),
  },
  components: [
    {
      id: 'gov-comp-1',
      name: 'Compliance Framework Manager',
      status: 'error',
      category: 'governance',
      healthScore: 45,
      lastUpdate: new Date().toISOString(),
      governanceType: 'framework',
      responseTime: 45,
    },
    {
      id: 'gov-comp-2',
      name: 'Rule Engine Core',
      status: 'warning',
      category: 'governance',
      healthScore: 72,
      lastUpdate: new Date().toISOString(),
      governanceType: 'rule-engine',
      responseTime: 38,
    },
  ],
  query: {},
};

describe('ViolationsList', () => {
  // --------------------------------------------------------------------------
  // RENDERING TESTS
  // --------------------------------------------------------------------------

  it('should render violations list', () => {
    render(<ViolationsList data={mockGovernanceData} />);
    expect(screen.getByText(/Compliance Violations/i)).toBeInTheDocument();
  });

  it('should display violation count', () => {
    render(<ViolationsList data={mockGovernanceData} />);
    expect(screen.getByText(/5 violations/i)).toBeInTheDocument();
  });

  it('should display violations based on error components', () => {
    render(<ViolationsList data={mockGovernanceData} />);
    expect(screen.getByText(/Critical compliance violation detected/i)).toBeInTheDocument();
  });

  // --------------------------------------------------------------------------
  // FILTER TESTS
  // --------------------------------------------------------------------------

  it('should have severity filter dropdown', () => {
    render(<ViolationsList data={mockGovernanceData} />);
    const severityFilter = screen.getByRole('combobox', { name: /Severity:/i });
    expect(severityFilter).toBeInTheDocument();
  });

  it('should have status filter dropdown', () => {
    render(<ViolationsList data={mockGovernanceData} />);
    const statusFilter = screen.getByRole('combobox', { name: /Status:/i });
    expect(statusFilter).toBeInTheDocument();
  });

  it('should filter violations by severity', () => {
    render(<ViolationsList data={mockGovernanceData} />);
    
    const severityFilter = screen.getByRole('combobox', { name: /Severity:/i });
    fireEvent.change(severityFilter, { target: { value: 'critical' } });
    
    // Should show only critical violations
    expect(screen.getByText(/CRITICAL/i)).toBeInTheDocument();
  });

  it('should filter violations by status', () => {
    render(<ViolationsList data={mockGovernanceData} />);
    
    const statusFilter = screen.getByRole('combobox', { name: /Status:/i });
    fireEvent.change(statusFilter, { target: { value: 'active' } });
    
    // Should show only active violations
    const violations = screen.queryAllByText(/RESOLVED/i);
    expect(violations.length).toBeGreaterThan(0);
  });

  // --------------------------------------------------------------------------
  // VIOLATION DETAILS TESTS
  // --------------------------------------------------------------------------

  it('should display violation severity badges', () => {
    render(<ViolationsList data={mockGovernanceData} />);
    expect(screen.getByText(/CRITICAL/i)).toBeInTheDocument();
  });

  it('should display violation messages', () => {
    render(<ViolationsList data={mockGovernanceData} />);
    expect(screen.getByText(/Critical compliance violation detected/i)).toBeInTheDocument();
  });

  it('should display component names in violations', () => {
    render(<ViolationsList data={mockGovernanceData} />);
    expect(screen.getByText(/Compliance Framework Manager/i)).toBeInTheDocument();
  });

  // --------------------------------------------------------------------------
  // EMPTY STATE TESTS
  // --------------------------------------------------------------------------

  it('should show empty state when no violations match filters', () => {
    const emptyData: IGovernanceData = {
      ...mockGovernanceData,
      metrics: {
        ...mockGovernanceData.metrics,
        violationCount: 0,
      },
      components: [],
    };

    render(<ViolationsList data={emptyData} />);
    
    const severityFilter = screen.getByRole('combobox', { name: /Severity:/i });
    fireEvent.change(severityFilter, { target: { value: 'critical' } });
    
    expect(screen.getByText(/No violations match the selected filters/i)).toBeInTheDocument();
  });

  // --------------------------------------------------------------------------
  // CUSTOM CLASS TESTS
  // --------------------------------------------------------------------------

  it('should apply custom className', () => {
    const { container } = render(<ViolationsList data={mockGovernanceData} className="custom-class" />);
    const element = container.querySelector('.custom-class');
    expect(element).toBeInTheDocument();
  });
});

