/**
 * ============================================================================
 * OA FRAMEWORK - M0 REAL DASHBOARD
 * Compliance Operations Panel Component Tests
 * ============================================================================
 * 
 * @fileoverview Tests for ComplianceOperationsPanel component
 * @module __tests__/governance-dashboard/components/ComplianceOperationsPanel.test
 * @version 1.0.0
 * @since 2025-10-22
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2025 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ComplianceOperationsPanel } from '@/components/governance/ComplianceOperationsPanel';

describe('ComplianceOperationsPanel', () => {
  const mockOnComplianceCheck = jest.fn();
  const mockOnRuleValidation = jest.fn();
  const mockOnFrameworkAudit = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  // --------------------------------------------------------------------------
  // RENDERING TESTS
  // --------------------------------------------------------------------------

  it('should render operations panel', () => {
    render(
      <ComplianceOperationsPanel
        operationStatus="idle"
        onComplianceCheck={mockOnComplianceCheck}
        onRuleValidation={mockOnRuleValidation}
        onFrameworkAudit={mockOnFrameworkAudit}
      />
    );
    expect(screen.getByText(/Governance Operations/i)).toBeInTheDocument();
  });

  it('should display all operation buttons', () => {
    render(
      <ComplianceOperationsPanel
        operationStatus="idle"
        onComplianceCheck={mockOnComplianceCheck}
        onRuleValidation={mockOnRuleValidation}
        onFrameworkAudit={mockOnFrameworkAudit}
      />
    );
    
    expect(screen.getByText(/Compliance Check/i)).toBeInTheDocument();
    expect(screen.getByText(/Rule Validation/i)).toBeInTheDocument();
    expect(screen.getByText(/Framework Audit/i)).toBeInTheDocument();
  });

  it('should display operation descriptions', () => {
    render(
      <ComplianceOperationsPanel
        operationStatus="idle"
        onComplianceCheck={mockOnComplianceCheck}
        onRuleValidation={mockOnRuleValidation}
        onFrameworkAudit={mockOnFrameworkAudit}
      />
    );
    
    expect(screen.getByText(/Verify compliance status across all frameworks/i)).toBeInTheDocument();
    expect(screen.getByText(/Validate all active governance rules/i)).toBeInTheDocument();
    expect(screen.getByText(/Perform comprehensive framework audit/i)).toBeInTheDocument();
  });

  // --------------------------------------------------------------------------
  // INTERACTION TESTS
  // --------------------------------------------------------------------------

  it('should call onComplianceCheck when compliance check button is clicked', () => {
    render(
      <ComplianceOperationsPanel
        operationStatus="idle"
        onComplianceCheck={mockOnComplianceCheck}
        onRuleValidation={mockOnRuleValidation}
        onFrameworkAudit={mockOnFrameworkAudit}
      />
    );
    
    const buttons = screen.getAllByText(/Run Check/i);
    fireEvent.click(buttons[0]);
    
    expect(mockOnComplianceCheck).toHaveBeenCalledTimes(1);
  });

  it('should call onRuleValidation when rule validation button is clicked', () => {
    render(
      <ComplianceOperationsPanel
        operationStatus="idle"
        onComplianceCheck={mockOnComplianceCheck}
        onRuleValidation={mockOnRuleValidation}
        onFrameworkAudit={mockOnFrameworkAudit}
      />
    );
    
    const button = screen.getByText(/Validate Rules/i);
    fireEvent.click(button);
    
    expect(mockOnRuleValidation).toHaveBeenCalledTimes(1);
  });

  it('should call onFrameworkAudit when framework audit button is clicked', () => {
    render(
      <ComplianceOperationsPanel
        operationStatus="idle"
        onComplianceCheck={mockOnComplianceCheck}
        onRuleValidation={mockOnRuleValidation}
        onFrameworkAudit={mockOnFrameworkAudit}
      />
    );
    
    const button = screen.getByText(/Run Audit/i);
    fireEvent.click(button);
    
    expect(mockOnFrameworkAudit).toHaveBeenCalledTimes(1);
  });

  // --------------------------------------------------------------------------
  // STATUS TESTS
  // --------------------------------------------------------------------------

  it('should disable buttons when operation is running', () => {
    render(
      <ComplianceOperationsPanel
        operationStatus="running"
        onComplianceCheck={mockOnComplianceCheck}
        onRuleValidation={mockOnRuleValidation}
        onFrameworkAudit={mockOnFrameworkAudit}
      />
    );
    
    const buttons = screen.getAllByRole('button');
    buttons.forEach(button => {
      expect(button).toBeDisabled();
    });
  });

  it('should show running status indicator', () => {
    render(
      <ComplianceOperationsPanel
        operationStatus="running"
        onComplianceCheck={mockOnComplianceCheck}
        onRuleValidation={mockOnRuleValidation}
        onFrameworkAudit={mockOnFrameworkAudit}
      />
    );
    
    expect(screen.getByText(/Operation in progress/i)).toBeInTheDocument();
  });

  it('should show success status indicator', () => {
    render(
      <ComplianceOperationsPanel
        operationStatus="success"
        onComplianceCheck={mockOnComplianceCheck}
        onRuleValidation={mockOnRuleValidation}
        onFrameworkAudit={mockOnFrameworkAudit}
      />
    );
    
    expect(screen.getByText(/Operation completed successfully/i)).toBeInTheDocument();
  });

  it('should show error status indicator', () => {
    render(
      <ComplianceOperationsPanel
        operationStatus="error"
        onComplianceCheck={mockOnComplianceCheck}
        onRuleValidation={mockOnRuleValidation}
        onFrameworkAudit={mockOnFrameworkAudit}
      />
    );
    
    expect(screen.getByText(/Operation failed/i)).toBeInTheDocument();
  });

  it('should not show status indicator when idle', () => {
    render(
      <ComplianceOperationsPanel
        operationStatus="idle"
        onComplianceCheck={mockOnComplianceCheck}
        onRuleValidation={mockOnRuleValidation}
        onFrameworkAudit={mockOnFrameworkAudit}
      />
    );
    
    expect(screen.queryByText(/Operation in progress/i)).not.toBeInTheDocument();
    expect(screen.queryByText(/Operation completed successfully/i)).not.toBeInTheDocument();
    expect(screen.queryByText(/Operation failed/i)).not.toBeInTheDocument();
  });

  // --------------------------------------------------------------------------
  // CUSTOM CLASS TESTS
  // --------------------------------------------------------------------------

  it('should apply custom className', () => {
    const { container } = render(
      <ComplianceOperationsPanel
        operationStatus="idle"
        onComplianceCheck={mockOnComplianceCheck}
        onRuleValidation={mockOnRuleValidation}
        onFrameworkAudit={mockOnFrameworkAudit}
        className="custom-class"
      />
    );
    
    const element = container.querySelector('.custom-class');
    expect(element).toBeInTheDocument();
  });
});

