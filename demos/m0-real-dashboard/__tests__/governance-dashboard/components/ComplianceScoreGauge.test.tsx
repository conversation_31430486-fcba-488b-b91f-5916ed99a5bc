/**
 * ============================================================================
 * OA FRAMEWORK - M0 REAL DASHBOARD
 * Compliance Score Gauge Component Tests
 * ============================================================================
 * 
 * @fileoverview Tests for ComplianceScoreGauge component
 * @module __tests__/governance-dashboard/components/ComplianceScoreGauge.test
 * @version 1.0.0
 * @since 2025-10-22
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2025 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ComplianceScoreGauge } from '@/components/governance/ComplianceScoreGauge';

describe('ComplianceScoreGauge', () => {
  // --------------------------------------------------------------------------
  // RENDERING TESTS
  // --------------------------------------------------------------------------

  it('should render compliance score gauge', () => {
    render(<ComplianceScoreGauge score={85} />);
    expect(screen.getByText(/Compliance Score/i)).toBeInTheDocument();
  });

  it('should display score percentage', () => {
    render(<ComplianceScoreGauge score={85} />);
    expect(screen.getByText(/85%/i)).toBeInTheDocument();
  });

  it('should display status text for excellent score', () => {
    render(<ComplianceScoreGauge score={95} />);
    expect(screen.getByText(/Excellent/i)).toBeInTheDocument();
  });

  it('should display status text for good score', () => {
    render(<ComplianceScoreGauge score={75} />);
    expect(screen.getByText(/Good/i)).toBeInTheDocument();
  });

  it('should display status text for fair score', () => {
    render(<ComplianceScoreGauge score={55} />);
    expect(screen.getByText(/Fair/i)).toBeInTheDocument();
  });

  it('should display status text for poor score', () => {
    render(<ComplianceScoreGauge score={35} />);
    expect(screen.getByText(/Poor/i)).toBeInTheDocument();
  });

  // --------------------------------------------------------------------------
  // TREND INDICATOR TESTS
  // --------------------------------------------------------------------------

  it('should show positive trend when score increased', () => {
    render(<ComplianceScoreGauge score={85} previousScore={80} showTrend={true} />);
    expect(screen.getByText(/\+5\.0% from last audit/i)).toBeInTheDocument();
  });

  it('should show negative trend when score decreased', () => {
    render(<ComplianceScoreGauge score={75} previousScore={80} showTrend={true} />);
    expect(screen.getByText(/-5\.0% from last audit/i)).toBeInTheDocument();
  });

  it('should show no change when score is same', () => {
    render(<ComplianceScoreGauge score={80} previousScore={80} showTrend={true} />);
    expect(screen.getByText(/No change from last audit/i)).toBeInTheDocument();
  });

  it('should not show trend when showTrend is false', () => {
    render(<ComplianceScoreGauge score={85} previousScore={80} showTrend={false} />);
    expect(screen.queryByText(/from last audit/i)).not.toBeInTheDocument();
  });

  it('should not show trend when previousScore is not provided', () => {
    render(<ComplianceScoreGauge score={85} showTrend={true} />);
    expect(screen.queryByText(/from last audit/i)).not.toBeInTheDocument();
  });

  // --------------------------------------------------------------------------
  // THRESHOLD LEGEND TESTS
  // --------------------------------------------------------------------------

  it('should display threshold legend', () => {
    render(<ComplianceScoreGauge score={85} />);
    expect(screen.getByText(/≥90%/i)).toBeInTheDocument();
    expect(screen.getByText(/70-89%/i)).toBeInTheDocument();
    expect(screen.getByText(/<70%/i)).toBeInTheDocument();
  });

  // --------------------------------------------------------------------------
  // EDGE CASES
  // --------------------------------------------------------------------------

  it('should handle score of 0', () => {
    render(<ComplianceScoreGauge score={0} />);
    expect(screen.getByText(/0%/i)).toBeInTheDocument();
    expect(screen.getByText(/Poor/i)).toBeInTheDocument();
  });

  it('should handle score of 100', () => {
    render(<ComplianceScoreGauge score={100} />);
    expect(screen.getByText(/100%/i)).toBeInTheDocument();
    expect(screen.getByText(/Excellent/i)).toBeInTheDocument();
  });

  it('should clamp score above 100 to 100', () => {
    render(<ComplianceScoreGauge score={150} />);
    expect(screen.getByText(/100%/i)).toBeInTheDocument();
  });

  it('should clamp score below 0 to 0', () => {
    render(<ComplianceScoreGauge score={-10} />);
    expect(screen.getByText(/0%/i)).toBeInTheDocument();
  });

  // --------------------------------------------------------------------------
  // CUSTOM CLASS TESTS
  // --------------------------------------------------------------------------

  it('should apply custom className', () => {
    const { container } = render(<ComplianceScoreGauge score={85} className="custom-class" />);
    const element = container.querySelector('.custom-class');
    expect(element).toBeInTheDocument();
  });
});

