/**
 * ============================================================================
 * OA FRAMEWORK - M0 REAL DASHBOARD
 * Governance Dashboard Tests
 * ============================================================================
 * 
 * @fileoverview Tests for the main governance dashboard page
 * @module __tests__/governance-dashboard/GovernanceDashboard.test
 * @version 1.0.0
 * @since 2025-10-22
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2025 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import GovernanceDashboardPage from '@/app/governance-dashboard/page';

// ============================================================================
// MOCK DATA
// ============================================================================

const mockGovernanceData = {
  totalGovernanceComponents: 69,
  healthyComponents: 65,
  errorComponents: 2,
  filteredCount: 69,
  page: 1,
  limit: 50,
  totalPages: 2,
  metrics: {
    complianceScore: 85.5,
    ruleCount: 150,
    violationCount: 5,
    frameworksActive: 12,
    lastAudit: new Date().toISOString(),
  },
  components: [
    {
      id: 'gov-comp-1',
      name: 'Compliance Framework Manager',
      status: 'healthy' as const,
      category: 'governance',
      healthScore: 95,
      lastUpdate: new Date().toISOString(),
      governanceType: 'framework' as const,
      responseTime: 45,
    },
    {
      id: 'gov-comp-2',
      name: 'Rule Engine Core',
      status: 'healthy' as const,
      category: 'governance',
      healthScore: 92,
      lastUpdate: new Date().toISOString(),
      governanceType: 'rule-engine' as const,
      responseTime: 38,
    },
  ],
  query: {},
};

// ============================================================================
// MOCKS
// ============================================================================

// Mock fetch
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({ success: true, data: mockGovernanceData }),
  } as Response)
);

// Mock Next.js Link
jest.mock('next/link', () => {
  return ({ children, href }: { children: React.ReactNode; href: string }) => {
    return <a href={href}>{children}</a>;
  };
});

// ============================================================================
// TESTS
// ============================================================================

describe('GovernanceDashboardPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  // --------------------------------------------------------------------------
  // RENDERING TESTS
  // --------------------------------------------------------------------------

  it('should render loading state initially', () => {
    render(<GovernanceDashboardPage />);
    expect(screen.getByText(/Loading Governance Dashboard/i)).toBeInTheDocument();
  });

  it('should render dashboard after data loads', async () => {
    render(<GovernanceDashboardPage />);

    await waitFor(() => {
      expect(screen.getByText(/Governance Dashboard/i)).toBeInTheDocument();
    });
  });

  it('should display governance overview panel', async () => {
    render(<GovernanceDashboardPage />);

    await waitFor(() => {
      expect(screen.getByText(/Governance Overview/i)).toBeInTheDocument();
    });
  });

  it('should display compliance score gauge', async () => {
    render(<GovernanceDashboardPage />);

    await waitFor(() => {
      expect(screen.getByText(/Compliance Score/i)).toBeInTheDocument();
    });
  });

  it('should display rule engine status', async () => {
    render(<GovernanceDashboardPage />);

    await waitFor(() => {
      expect(screen.getByText(/Rule Engine Status/i)).toBeInTheDocument();
    });
  });

  it('should display framework status grid', async () => {
    render(<GovernanceDashboardPage />);

    await waitFor(() => {
      expect(screen.getByText(/Governance Frameworks/i)).toBeInTheDocument();
    });
  });

  it('should display violations list', async () => {
    render(<GovernanceDashboardPage />);

    await waitFor(() => {
      expect(screen.getByText(/Compliance Violations/i)).toBeInTheDocument();
    });
  });

  it('should display operations panel', async () => {
    render(<GovernanceDashboardPage />);

    await waitFor(() => {
      expect(screen.getByText(/Governance Operations/i)).toBeInTheDocument();
    });
  });

  // --------------------------------------------------------------------------
  // DATA FETCHING TESTS
  // --------------------------------------------------------------------------

  it('should fetch governance data on mount', async () => {
    render(<GovernanceDashboardPage />);

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/m0-governance');
    });
  });

  it('should display correct component counts', async () => {
    render(<GovernanceDashboardPage />);

    await waitFor(() => {
      expect(screen.getByText('69')).toBeInTheDocument(); // Total components
      expect(screen.getByText('65')).toBeInTheDocument(); // Healthy components
    });
  });

  it('should display compliance score', async () => {
    render(<GovernanceDashboardPage />);

    await waitFor(() => {
      expect(screen.getByText(/85\.5%/i)).toBeInTheDocument();
    });
  });

  // --------------------------------------------------------------------------
  // USER INTERACTION TESTS
  // --------------------------------------------------------------------------

  it('should handle refresh button click', async () => {
    render(<GovernanceDashboardPage />);

    await waitFor(() => {
      expect(screen.getByText(/Governance Dashboard/i)).toBeInTheDocument();
    });

    const refreshButton = screen.getByRole('button', { name: /Refresh/i });
    fireEvent.click(refreshButton);

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledTimes(2); // Initial + refresh
    });
  });

  it('should toggle alerts visibility', async () => {
    render(<GovernanceDashboardPage />);

    await waitFor(() => {
      expect(screen.getByText(/Governance Dashboard/i)).toBeInTheDocument();
    });

    const alertButton = screen.getByRole('button', { name: '' }); // Bell icon button
    fireEvent.click(alertButton);

    // Alert visibility should toggle
    expect(alertButton).toBeInTheDocument();
  });

  // --------------------------------------------------------------------------
  // ERROR HANDLING TESTS
  // --------------------------------------------------------------------------

  it('should display error state when fetch fails', async () => {
    (global.fetch as jest.Mock).mockImplementationOnce(() =>
      Promise.resolve({
        ok: false,
        status: 500,
        json: () => Promise.resolve({ success: false, error: 'Server error' }),
      } as Response)
    );

    render(<GovernanceDashboardPage />);

    await waitFor(() => {
      expect(screen.getByText(/Failed to Load Governance Data/i)).toBeInTheDocument();
    });
  });

  it('should allow retry after error', async () => {
    (global.fetch as jest.Mock).mockImplementationOnce(() =>
      Promise.resolve({
        ok: false,
        status: 500,
        json: () => Promise.resolve({ success: false, error: 'Server error' }),
      } as Response)
    );

    render(<GovernanceDashboardPage />);

    await waitFor(() => {
      expect(screen.getByText(/Failed to Load Governance Data/i)).toBeInTheDocument();
    });

    // Reset mock to succeed
    (global.fetch as jest.Mock).mockImplementationOnce(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve({ success: true, data: mockGovernanceData }),
      } as Response)
    );

    const retryButton = screen.getByRole('button', { name: /Retry/i });
    fireEvent.click(retryButton);

    await waitFor(() => {
      expect(screen.getByText(/Governance Dashboard/i)).toBeInTheDocument();
    });
  });

  // --------------------------------------------------------------------------
  // OPERATIONS TESTS
  // --------------------------------------------------------------------------

  it('should display operation buttons', async () => {
    render(<GovernanceDashboardPage />);

    await waitFor(() => {
      expect(screen.getByText(/Run Check/i)).toBeInTheDocument();
      expect(screen.getByText(/Validate Rules/i)).toBeInTheDocument();
      expect(screen.getByText(/Run Audit/i)).toBeInTheDocument();
    });
  });

  // --------------------------------------------------------------------------
  // RESPONSIVE DESIGN TESTS
  // --------------------------------------------------------------------------

  it('should render with responsive layout classes', async () => {
    const { container } = render(<GovernanceDashboardPage />);

    await waitFor(() => {
      expect(screen.getByText(/Governance Dashboard/i)).toBeInTheDocument();
    });

    const mainContent = container.querySelector('main');
    expect(mainContent).toHaveClass('max-w-7xl', 'mx-auto');
  });

  // --------------------------------------------------------------------------
  // ACCESSIBILITY TESTS
  // --------------------------------------------------------------------------

  it('should have proper heading hierarchy', async () => {
    render(<GovernanceDashboardPage />);

    await waitFor(() => {
      const heading = screen.getByRole('heading', { name: /Governance Dashboard/i });
      expect(heading).toBeInTheDocument();
      expect(heading.tagName).toBe('H1');
    });
  });

  it('should have accessible navigation link', async () => {
    render(<GovernanceDashboardPage />);

    await waitFor(() => {
      const backLink = screen.getByRole('link');
      expect(backLink).toHaveAttribute('href', '/');
    });
  });
});

