# M0 Real Component Integration Dashboard - Test Suite

## ⚠️ **IMPORTANT: Working Directory Requirements**

**For Jest Integration Tests** (in `integration/` subdirectory):

✅ **CORRECT - Run from M0 Dashboard directory**:
```bash
cd demos/m0-real-dashboard
npm test                          # Run all Jest tests
npm run test:integration          # Run integration tests only
npm run test:coverage             # Run with coverage
```

✅ **CORRECT - Run from root using npm scripts**:
```bash
npm run test:m0-dashboard         # Run all M0 Dashboard tests
npm run test:m0-dashboard:integration
npm run test:m0-dashboard:coverage
```

❌ **INCORRECT - Don't run from root directly**:
```bash
npm test demos/m0-real-dashboard/__tests__/  # Will use wrong Jest config!
```

**Why?** The M0 Dashboard has its own Jest configuration with specific path aliases (`@/`, `@lib/`, `@components/`) that resolve relative to the dashboard directory. Running from the root uses the wrong configuration and causes module resolution errors.

**See**: `integration/WORKING-DIRECTORY-ISSUE-ANALYSIS.md` for complete analysis.

---

## Overview

This comprehensive test suite validates the M0 Real Component Integration Dashboard, which successfully integrates **87 M0 components** across four categories (governance: 40, tracking: 21, memory-safety: 14, integration: 12).

## Recent Fixes Tested

### ✅ **Refresh Functionality Fix**
- **Issue**: POST /api/m0-components was returning 405 Method Not Allowed
- **Fix**: Added comprehensive POST handler with proper validation
- **Tests**: `refresh-functionality-tests.sh` validates the fix

### ✅ **EnvironmentConstantsCalculator Health Status Fix**
- **Issue**: Component was showing "error" status instead of "healthy"
- **Fix**: Enhanced health check logic to handle multiple response formats
- **Tests**: `health-check-tests.sh` specifically validates this fix

## Test Suite Structure

```
__tests__/
├── run-all-tests.sh                    # Master test runner
├── api-endpoint-tests.sh               # API endpoint validation
├── component-integration-tests.sh      # Component integration testing
├── health-check-tests.sh              # Health checking functionality
├── refresh-functionality-tests.sh     # Refresh functionality testing
├── performance-tests.sh               # Performance and load testing
├── error-handling-tests.sh            # Error scenarios and recovery
└── README.md                          # This documentation
```

## Prerequisites

### Required Software
- **curl** - For HTTP requests
- **jq** - For JSON parsing and validation
- **bash** - Shell environment (version 4.0+)
- **bc** - Basic calculator (for floating point comparisons)

### Server Requirements
- M0 dashboard server running at `http://localhost:3000`
- Start with: `cd demos/m0-real-dashboard && npm run dev`

## Usage

### Run All Tests
```bash
./run-all-tests.sh
```

### Quick Essential Tests
```bash
./run-all-tests.sh --quick
```

### Specific Test Suites
```bash
./run-all-tests.sh --api          # API endpoint tests only
./run-all-tests.sh --components   # Component integration tests only
./run-all-tests.sh --health       # Health check tests only
./run-all-tests.sh --refresh      # Refresh functionality tests only
./run-all-tests.sh --performance  # Performance tests only
./run-all-tests.sh --errors       # Error handling tests only
```

### Multiple Specific Tests
```bash
./run-all-tests.sh --api --health --refresh
```

### Help
```bash
./run-all-tests.sh --help
```

## Individual Test Scripts

### 1. API Endpoint Tests (`api-endpoint-tests.sh`)
**Purpose**: Validates all API endpoints and response formats

**Tests Include**:
- GET /api/m0-components success cases
- POST /api/m0-components refresh functionality
- Invalid action handling (400 errors)
- Malformed JSON handling
- Response format validation
- Required field validation

**Key Validations**:
- HTTP status codes (200, 400, 500)
- JSON structure compliance
- Component count accuracy (21 components)
- Response time thresholds

### 2. Component Integration Tests (`component-integration-tests.sh`)
**Purpose**: Validates integration of all 21 M0 components

**Tests Include**:
- Total component count validation (21 components)
- Category-specific component counts:
  - Governance: 11 components
  - Tracking: 6 components
  - Memory Safety: 1 component
  - Integration: 3 components
- Individual component presence validation
- Component structure validation
- Health status validation

**Key Validations**:
- All expected components are registered
- Component metadata is complete
- Category organization is correct
- 5x expansion from original 4 components

### 3. Health Check Tests (`health-check-tests.sh`)
**Purpose**: Validates health checking functionality and recent fixes

**Tests Include**:
- Overall health metrics (100% health score)
- EnvironmentConstantsCalculator specific health status
- Individual component health scores
- Health consistency after refresh
- System metrics validation
- Component metrics validity
- Health status consistency

**Key Validations**:
- EnvironmentConstantsCalculator shows "healthy" (not "error")
- All 21 components report healthy status
- Health scores are valid (0-100)
- System error rate is 0%

### 4. Refresh Functionality Tests (`refresh-functionality-tests.sh`)
**Purpose**: Validates the recently fixed manual refresh functionality

**Tests Include**:
- Basic refresh functionality (POST with {"action":"refresh"})
- Data consistency after refresh
- Refresh performance (<5 seconds)
- Invalid action handling
- Missing action handling
- Malformed JSON handling
- Multiple consecutive refreshes
- Response format validation
- Timestamp updates

**Key Validations**:
- POST endpoint returns HTTP 200
- Refresh maintains data consistency
- Response includes success message
- Timestamps are updated correctly

### 5. Performance Tests (`performance-tests.sh`)
**Purpose**: Validates performance metrics and load handling

**Tests Include**:
- GET endpoint performance (<2 seconds)
- POST endpoint performance (<5 seconds)
- Dashboard load performance (<3 seconds)
- Concurrent request handling
- API response size analysis
- Component metrics performance
- Memory usage validation
- Load testing (continuous requests)
- Error rate performance

**Key Validations**:
- Response times within thresholds
- Concurrent requests handled properly
- System maintains performance under load
- Memory usage is reasonable

### 6. Error Handling Tests (`error-handling-tests.sh`)
**Purpose**: Validates error scenarios and graceful degradation

**Tests Include**:
- Invalid endpoint access (404 errors)
- Invalid HTTP methods (405 errors)
- Malformed JSON requests
- Empty request bodies
- Oversized requests
- Invalid content types
- Network timeout simulation
- Server unavailable scenarios
- Error response format validation
- Concurrent error requests
- Recovery after errors
- Graceful degradation

**Key Validations**:
- Appropriate HTTP error codes
- Valid JSON error responses
- System recovery after errors
- Graceful handling of edge cases

## Expected Results

### Successful Test Run
```
============================================================================
FINAL TEST SUMMARY
============================================================================
Test Suites Run: 6
Test Suites Passed: 6
Test Suites Failed: 0

🎉 ALL TEST SUITES PASSED! 🎉
The M0 Real Component Integration Dashboard is working perfectly!

Dashboard Status:
✅ 21 M0 Components Integrated (5x expansion)
✅ 100% Health Score (all components healthy)
✅ Refresh Functionality Working
✅ Performance Within Thresholds
✅ Error Handling Robust
✅ Ready for Production Use
```

### Component Integration Validation
- **Total Components**: 21 (5x expansion from original 4)
- **Governance Components**: 11 (GovernanceRuleEngineCore, GovernanceRuleComplianceChecker, etc.)
- **Tracking Components**: 6 (SessionLogTracker, ImplementationProgressTracker, etc.)
- **Memory Safety Components**: 1 (EnvironmentConstantsCalculator - now healthy!)
- **Integration Components**: 3 (GovernanceTrackingBridge, RealtimeEventCoordinator, etc.)

### Performance Benchmarks
- **GET API Response**: <2 seconds average
- **POST API Response**: <5 seconds average
- **Dashboard Load**: <3 seconds average
- **Concurrent Requests**: 5 simultaneous requests handled
- **System Error Rate**: 0%
- **Overall Health Score**: 100%

## Troubleshooting

### Server Not Running
```
[FAIL] Server is not running at http://localhost:3000
Please start the server with: cd demos/m0-real-dashboard && npm run dev
```

### Missing Dependencies
```
[FAIL] Required tool missing: jq
Please install jq to run the tests
```

### Test Failures
1. Check server logs for errors
2. Verify all 21 components are properly initialized
3. Ensure no port conflicts
4. Check system resources (memory, CPU)

## Integration with CI/CD

### GitHub Actions Example
```yaml
- name: Run M0 Dashboard Tests
  run: |
    cd demos/m0-real-dashboard
    npm run dev &
    sleep 10
    ./__tests__/run-all-tests.sh --quick
```

### Jenkins Pipeline Example
```groovy
stage('M0 Dashboard Tests') {
    steps {
        sh 'cd demos/m0-real-dashboard && npm run dev &'
        sh 'sleep 10'
        sh 'cd demos/m0-real-dashboard && ./__tests__/run-all-tests.sh'
    }
}
```

## Contributing

When adding new tests:
1. Follow the existing naming convention
2. Include proper error handling and exit codes
3. Use the established logging functions
4. Add comprehensive documentation
5. Test both positive and negative scenarios
6. Update this README with new test descriptions

## Version History

- **v1.0.0** - Initial comprehensive test suite
  - 6 test scripts covering all functionality
  - Master test runner with multiple execution modes
  - Validation of 21 component integration
  - Testing of recent fixes (refresh functionality, health status)
  - Performance and error handling validation
