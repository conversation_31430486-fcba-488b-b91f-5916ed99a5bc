/**
 * ============================================================================
 * OA FRAMEWORK - BUSINESS ROI CALCULATOR PAGE
 * ROI Calculator Page
 * ============================================================================
 * 
 * @fileoverview ROI Calculator page for business value demonstration
 * @module app/business-roi/page
 * @version 1.0.0
 * @since 2026-02-01
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2026 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

'use client';

import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Container, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  Chip, 
  Button, 
  Alert, 
  LinearProgress, 
  Tabs, 
  Tab, 
  Paper,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Calculator,
  TrendingUp,
  BarChart3,
  Calendar,
  FileText,
  RefreshCw,
  Download,
  Info
} from 'lucide-react';
import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';

// Import components
import CostInputWizard from '../../components/business/CostInputWizard';
import SavingsEstimator from '../../components/business/SavingsEstimator';
import ROITimeline from '../../components/business/ROITimeline';

// Import types and services
import { ROICalculation } from '../../types/business-types';
import { roiCalculator } from '../../lib/business/roi-calculator';

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface ROICalculatorPageProps {}

// ============================================================================
// ROI CALCULATOR PAGE COMPONENT
// ============================================================================

export default function ROICalculatorPage({}: ROICalculatorPageProps): JSX.Element {
  // State management
  const [activeTab, setActiveTab] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [hasCalculated, setHasCalculated] = useState(false);
  
  // Cost and savings state
  const [currentCosts, setCurrentCosts] = useState(0);
  const [annualSavings, setAnnualSavings] = useState(0);
  
  // ROI calculation result
  const [roiCalculation, setRoiCalculation] = useState<ROICalculation | null>(null);
  
  // Wizard state
  const [costs, setCosts] = useState({
    infrastructure: 500000,
    personnel: 2000000,
    software: 300000,
    compliance: 150000,
    training: 50000
  });

  // Handle cost calculation
  const handleCostCalculate = (costInput: any) => {
    setIsLoading(true);
    const total = Object.values(costInput).reduce((sum: number, cost: number) => sum + cost, 0);
    setCurrentCosts(total);
    setCosts(costInput);
    setIsLoading(false);
  };

  // Handle savings update
  const handleSavingsUpdate = (savings: number) => {
    setAnnualSavings(savings);
  };

  // Handle ROI calculation
  const handleCalculateROI = () => {
    if (currentCosts <= 0 || annualSavings <= 0) {
      alert('Please configure both costs and savings before calculating ROI.');
      return;
    }

    setIsLoading(true);
    
    try {
      // Calculate ROI using the calculator engine
      const calculation = roiCalculator.calculateROI(currentCosts, annualSavings);
      
      // Add additional data for timeline
      calculation.implementationTimeline = roiCalculator.calculateImplementationTimeline(currentCosts, 12);
      calculation.costBreakdown = roiCalculator.calculateCostBreakdown(currentCosts);
      calculation.initiativeBenefits = roiCalculator.calculateInitiativeBenefits(calculation);
      calculation.scenarioComparison = roiCalculator.generateScenarioComparison(currentCosts, currentCosts * 0.7, currentCosts * 0.1);
      
      setRoiCalculation(calculation);
      setHasCalculated(true);
      setActiveTab(2); // Switch to timeline tab
    } catch (error) {
      console.error('ROI calculation error:', error);
      alert('Error calculating ROI. Please check your inputs and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle reset
  const handleReset = () => {
    setRoiCalculation(null);
    setHasCalculated(false);
    setCurrentCosts(0);
    setAnnualSavings(0);
    setActiveTab(0);
    setCosts({
      infrastructure: 500000,
      personnel: 2000000,
      software: 300000,
      compliance: 150000,
      training: 50000
    });
  };

  // Export to PDF
  const handleExportPDF = () => {
    if (!roiCalculation) return;

    const doc = new jsPDF();
    
    // Title
    doc.setFontSize(18);
    doc.text('OA Framework ROI Analysis Report', 14, 20);
    
    // Basic info
    doc.setFontSize(12);
    doc.text(`Generated: ${new Date().toLocaleDateString()}`, 14, 30);
    doc.text(`Total Investment: $${currentCosts.toLocaleString()}`, 14, 40);
    doc.text(`Annual Savings: $${annualSavings.toLocaleString()}`, 14, 50);
    doc.text(`ROI Percentage: ${roiCalculation.roiPercentage.toFixed(1)}%`, 14, 60);
    doc.text(`Break-even Point: Month ${roiCalculation.breakEvenPoint}`, 14, 70);

    // Add more detailed sections as needed
    doc.save('oa-framework-roi-analysis.pdf');
  };

  // Tab change handler
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  return (
    <Container maxWidth="xl" className="py-6">
      {/* Header */}
      <Box className="mb-6 text-center">
        <div className="flex items-center justify-center mb-4">
          <div className="bg-gradient-to-r from-blue-500 to-green-500 p-4 rounded-full">
            <Calculate className="w-12 h-12 text-white" />
          </div>
        </div>
        <Typography variant="h2" className="font-bold text-slate-900 mb-2">
          ROI Calculator
        </Typography>
        <Typography variant="h6" className="text-slate-600 mb-4">
          Calculate the return on investment for OA Framework implementation
        </Typography>
        
        {/* Quick Stats */}
        {hasCalculated && roiCalculation && (
          <div className="flex justify-center space-x-6">
            <Chip 
              label={`Investment: $${currentCosts.toLocaleString()}`} 
              color="primary" 
              variant="outlined"
            />
            <Chip 
              label={`Savings: $${annualSavings.toLocaleString()}`} 
              color="success" 
              variant="outlined"
            />
            <Chip 
              label={`ROI: ${roiCalculation.roiPercentage.toFixed(1)}%`} 
              color="secondary" 
              variant="filled"
            />
            <Chip 
              label={`Payback: ${roiCalculation.paybackPeriod} months`} 
              color="warning" 
              variant="outlined"
            />
          </div>
        )}
      </Box>

      {/* Progress Indicator */}
      {hasCalculated && (
        <Card className="mb-6 border border-slate-200">
          <CardContent>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={8}>
                <Typography variant="body2" className="text-slate-600 mb-2">
                  Implementation Progress
                </Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={50} 
                  className="mb-2"
                />
                <Typography variant="caption" className="text-slate-500">
                  Configure costs → Estimate savings → Analyze ROI → Export results
                </Typography>
              </Grid>
              <Grid item xs={12} md={4} className="flex justify-end space-x-2">
                <Button
                  variant="outlined"
                  onClick={handleExportPDF}
                  startIcon={<Download className="w-4 h-4" />}
                  disabled={!roiCalculation}
                >
                  Export PDF
                </Button>
                <Button
                  variant="outlined"
                  onClick={handleReset}
                  startIcon={<RefreshCw className="w-4 h-4" />}
                >
                  Reset
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Main Content */}
      <Card className="border border-slate-200">
        <CardContent>
          {/* Tabs Navigation */}
          <Tabs 
            value={activeTab} 
            onChange={handleTabChange} 
            variant="fullWidth"
            className="mb-6"
          >
            <Tab 
              icon={<Calculate className="w-5 h-5" />}
              label="Cost Input" 
              disabled={isLoading}
            />
            <Tab 
              icon={<TrendingUp className="w-5 h-5" />}
              label="Savings Estimation" 
              disabled={isLoading || currentCosts <= 0}
            />
            <Tab 
              icon={<BarChart3 className="w-5 h-5" />}
              label="ROI Analysis" 
              disabled={isLoading || !hasCalculated}
            />
            <Tab 
              icon={<Calendar className="w-5 h-5" />}
              label="Timeline" 
              disabled={isLoading || !roiCalculation}
            />
            <Tab 
              icon={<FileText className="w-5 h-5" />}
              label="Reports" 
              disabled={isLoading || !roiCalculation}
            />
          </Tabs>

          {/* Tab Content */}
          <Box className="mt-4">
            {/* Tab 1: Cost Input */}
            {activeTab === 0 && (
              <CostInputWizard
                onCalculate={handleCostCalculate}
                onReset={handleReset}
                isLoading={isLoading}
                defaultValues={costs}
              />
            )}

            {/* Tab 2: Savings Estimation */}
            {activeTab === 1 && (
              <SavingsEstimator
                currentCosts={currentCosts}
                onSavingsUpdate={handleSavingsUpdate}
                isLoading={isLoading}
              />
            )}

            {/* Tab 3: ROI Analysis */}
            {activeTab === 2 && roiCalculation && (
              <Box>
                <Grid container spacing={3} className="mb-6">
                  <Grid item xs={12} md={4}>
                    <Card className="border border-slate-200">
                      <CardContent>
                        <div className="flex items-center justify-between">
                          <div>
                            <Typography variant="body2" className="text-slate-600">Total Investment</Typography>
                            <Typography variant="h5" className="font-bold text-slate-800">
                              ${currentCosts.toLocaleString()}
                            </Typography>
                          </div>
                          <div className="bg-blue-100 p-2 rounded-lg">
                            <Calculate className="w-6 h-6 text-blue-600" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Card className="border border-slate-200">
                      <CardContent>
                        <div className="flex items-center justify-between">
                          <div>
                            <Typography variant="body2" className="text-slate-600">Annual Savings</Typography>
                            <Typography variant="h5" className="font-bold text-green-600">
                              ${annualSavings.toLocaleString()}
                            </Typography>
                          </div>
                          <div className="bg-green-100 p-2 rounded-lg">
                            <TrendingUp className="w-6 h-6 text-green-600" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Card className="border border-slate-200">
                      <CardContent>
                        <div className="flex items-center justify-between">
                          <div>
                            <Typography variant="body2" className="text-slate-600">ROI Percentage</Typography>
                            <Typography variant="h5" className="font-bold text-purple-600">
                              {roiCalculation.roiPercentage.toFixed(1)}%
                            </Typography>
                          </div>
                          <div className="bg-purple-100 p-2 rounded-lg">
                            <BarChart3 className="w-6 h-6 text-purple-600" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Card className="border border-slate-200">
                      <CardContent>
                        <Typography variant="h6" className="font-semibold text-slate-800 mb-3">
                          Financial Metrics
                        </Typography>
                        <Grid container spacing={2}>
                          <Grid item xs={6}>
                            <Typography variant="body2" className="text-slate-600">Payback Period</Typography>
                            <Typography variant="body1" className="font-semibold">
                              {roiCalculation.paybackPeriod} months
                            </Typography>
                          </Grid>
                          <Grid item xs={6}>
                            <Typography variant="body2" className="text-slate-600">Break-even Point</Typography>
                            <Typography variant="body1" className="font-semibold">
                              Month {roiCalculation.breakEvenPoint}
                            </Typography>
                          </Grid>
                          <Grid item xs={6}>
                            <Typography variant="body2" className="text-slate-600">Net Present Value</Typography>
                            <Typography variant="body1" className="font-semibold">
                              ${roiCalculation.npv.toLocaleString()}
                            </Typography>
                          </Grid>
                          <Grid item xs={6}>
                            <Typography variant="body2" className="text-slate-600">Internal Rate of Return</Typography>
                            <Typography variant="body1" className="font-semibold">
                              {roiCalculation.irr.toFixed(1)}%
                            </Typography>
                          </Grid>
                        </Grid>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Card className="border border-slate-200">
                      <CardContent>
                        <Typography variant="h6" className="font-semibold text-slate-800 mb-3">
                          Scenario Analysis
                        </Typography>
                        <Grid container spacing={2}>
                          <Grid item xs={4}>
                            <Typography variant="body2" className="text-slate-600">Best Case</Typography>
                            <Typography variant="body1" className="font-semibold text-green-600">
                              {roiCalculation.sensitivityAnalysis?.bestCase?.toFixed(1) || 'N/A'}%
                            </Typography>
                          </Grid>
                          <Grid item xs={4}>
                            <Typography variant="body2" className="text-slate-600">Base Case</Typography>
                            <Typography variant="body1" className="font-semibold text-blue-600">
                              {roiCalculation.sensitivityAnalysis?.baseCase?.toFixed(1) || 'N/A'}%
                            </Typography>
                          </Grid>
                          <Grid item xs={4}>
                            <Typography variant="body2" className="text-slate-600">Worst Case</Typography>
                            <Typography variant="body1" className="font-semibold text-red-600">
                              {roiCalculation.sensitivityAnalysis?.worstCase?.toFixed(1) || 'N/A'}%
                            </Typography>
                          </Grid>
                        </Grid>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>

                <Box className="mt-4 flex justify-end space-x-2">
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => setActiveTab(3)}
                    disabled={isLoading}
                  >
                    View Timeline
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={handleExportPDF}
                    startIcon={<Download className="w-4 h-4" />}
                  >
                    Export Report
                  </Button>
                </Box>
              </Box>
            )}

            {/* Tab 4: Timeline */}
            {activeTab === 3 && roiCalculation && (
              <ROITimeline
                roiCalculation={roiCalculation}
                onExport={handleExportPDF}
                isLoading={isLoading}
              />
            )}

            {/* Tab 5: Reports */}
            {activeTab === 4 && roiCalculation && (
              <Box>
                <Typography variant="h5" className="font-semibold text-slate-800 mb-4">
                  Executive Summary Report
                </Typography>
                
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Card className="border border-slate-200">
                      <CardContent>
                        <Typography variant="h6" className="font-semibold text-slate-800 mb-3">
                          Investment Summary
                        </Typography>
                        <Typography variant="body2" className="text-slate-600 mb-2">
                          <strong>Total Investment:</strong> ${currentCosts.toLocaleString()}
                        </Typography>
                        <Typography variant="body2" className="text-slate-600 mb-2">
                          <strong>Implementation Period:</strong> 12 months
                        </Typography>
                        <Typography variant="body2" className="text-slate-600 mb-2">
                          <strong>Break-even Point:</strong> Month {roiCalculation.breakEvenPoint}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Card className="border border-slate-200">
                      <CardContent>
                        <Typography variant="h6" className="font-semibold text-slate-800 mb-3">
                          Benefits Summary
                        </Typography>
                        <Typography variant="body2" className="text-slate-600 mb-2">
                          <strong>Annual Savings:</strong> ${annualSavings.toLocaleString()}
                        </Typography>
                        <Typography variant="body2" className="text-slate-600 mb-2">
                          <strong>ROI:</strong> {roiCalculation.roiPercentage.toFixed(1)}%
                        </Typography>
                        <Typography variant="body2" className="text-slate-600 mb-2">
                          <strong>Net Present Value:</strong> ${roiCalculation.npv.toLocaleString()}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>

                <Card className="mt-4 border border-slate-200">
                  <CardContent>
                    <Typography variant="h6" className="font-semibold text-slate-800 mb-3">
                      Key Recommendations
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" className="text-slate-600">
                          <strong>Implementation Priority:</strong> Focus on performance optimization and process automation for maximum early returns.
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" className="text-slate-600">
                          <strong>Risk Mitigation:</strong> Implement in phases to minimize operational disruption and validate ROI assumptions.
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" className="text-slate-600">
                          <strong>Success Metrics:</strong> Track monthly savings, system performance improvements, and user satisfaction scores.
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" className="text-slate-600">
                          <strong>Next Steps:</strong> Begin with pilot implementation in high-impact areas to demonstrate value quickly.
                        </Typography>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>

                <Box className="mt-4 flex justify-end space-x-2">
                  <Button
                    variant="outlined"
                    onClick={handleExportPDF}
                    startIcon={<Download className="w-4 h-4" />}
                  >
                    Download Full Report
                  </Button>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleReset}
                    startIcon={<RefreshCw className="w-4 h-4" />}
                  >
                    Start New Analysis
                  </Button>
                </Box>
              </Box>
            )}
          </Box>
        </CardContent>
      </Card>
    </Container>
  );
}