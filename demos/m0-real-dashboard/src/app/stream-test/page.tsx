/**
 * ============================================================================
 * M0 REAL-TIME STREAMING TEST PAGE
 * ============================================================================
 * 
 * Priority 2 - Phase 3: Real-Time Data Streaming Test Interface
 * 
 * Interactive test page for SSE endpoint demonstrating:
 * - Real-time component status updates
 * - Health score change notifications
 * - Error and warning detection
 * - System metrics streaming
 * - Connection health monitoring
 * 
 * @route /stream-test - SSE testing interface
 * 
 * Author: AI Assistant (Priority 2 Phase 3 Implementation)
 * Created: 2025-10-20
 * Authority: President & CEO, E.Z. Consultancy
 * ============================================================================
 */

'use client';

import { useEffect, useState, useRef } from 'react';

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

interface IStreamEvent {
  type: string;
  timestamp: string;
  data: unknown;
}

interface IEventLog {
  id: number;
  event: IStreamEvent;
  receivedAt: string;
}

interface ISystemMetrics {
  overallHealthScore: number;
  totalComponents: number;
  healthyComponents: number;
  errorComponents: number;
  averageResponseTime: number;
}

// ============================================================================
// STREAM TEST PAGE COMPONENT
// ============================================================================

export default function StreamTestPage() {
  const [isConnected, setIsConnected] = useState(false);
  const [eventLogs, setEventLogs] = useState<IEventLog[]>([]);
  const [eventCounts, setEventCounts] = useState<Record<string, number>>({});
  const [lastHeartbeat, setLastHeartbeat] = useState<string>('');
  const [systemMetrics, setSystemMetrics] = useState<ISystemMetrics | null>(null);
  
  const eventSourceRef = useRef<EventSource | null>(null);
  const eventIdCounter = useRef(0);

  // ============================================================================
  // SSE CONNECTION MANAGEMENT
  // ============================================================================

  const connectToStream = () => {
    if (eventSourceRef.current) {
      console.log('Already connected to stream');
      return;
    }

    console.log('🔄 Connecting to M0 stream...');
    const eventSource = new EventSource('/api/m0-stream');
    eventSourceRef.current = eventSource;

    // Connection opened
    eventSource.onopen = () => {
      console.log('✅ SSE connection established');
      setIsConnected(true);
    };

    // Connection error
    eventSource.onerror = (error) => {
      console.error('❌ SSE connection error:', error);
      setIsConnected(false);
      
      // EventSource will automatically try to reconnect
      if (eventSource.readyState === EventSource.CLOSED) {
        console.log('🔌 Connection closed');
        eventSourceRef.current = null;
      }
    };

    // Listen for all event types
    const eventTypes = [
      'component-status-change',
      'health-score-change',
      'error-detected',
      'warning-detected',
      'system-metric-update',
      'heartbeat'
    ];

    eventTypes.forEach(eventType => {
      eventSource.addEventListener(eventType, (event: MessageEvent) => {
        const data = JSON.parse(event.data);
        
        const streamEvent: IStreamEvent = {
          type: eventType,
          timestamp: data.timestamp || new Date().toISOString(),
          data
        };

        // Add to event log
        const logEntry: IEventLog = {
          id: eventIdCounter.current++,
          event: streamEvent,
          receivedAt: new Date().toISOString()
        };

        setEventLogs(prev => [logEntry, ...prev].slice(0, 100)); // Keep last 100 events

        // Update event counts
        setEventCounts(prev => ({
          ...prev,
          [eventType]: (prev[eventType] || 0) + 1
        }));

        // Update specific state based on event type
        if (eventType === 'heartbeat') {
          setLastHeartbeat(new Date().toISOString());
        } else if (eventType === 'system-metric-update' && data.systemMetrics) {
          setSystemMetrics(data.systemMetrics);
        }

        console.log(`📡 Received ${eventType}:`, data);
      });
    });
  };

  const disconnectFromStream = () => {
    if (eventSourceRef.current) {
      console.log('🔌 Disconnecting from stream...');
      eventSourceRef.current.close();
      eventSourceRef.current = null;
      setIsConnected(false);
    }
  };

  const clearEventLog = () => {
    setEventLogs([]);
    setEventCounts({});
  };

  // Auto-connect on mount
  useEffect(() => {
    connectToStream();

    return () => {
      disconnectFromStream();
    };
  }, []);

  // ============================================================================
  // RENDER HELPERS
  // ============================================================================

  const getEventTypeColor = (type: string): string => {
    switch (type) {
      case 'error-detected': return 'text-red-600 bg-red-50';
      case 'warning-detected': return 'text-yellow-600 bg-yellow-50';
      case 'component-status-change': return 'text-blue-600 bg-blue-50';
      case 'health-score-change': return 'text-purple-600 bg-purple-50';
      case 'system-metric-update': return 'text-green-600 bg-green-50';
      case 'heartbeat': return 'text-gray-600 bg-gray-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getSeverityBadge = (severity?: string) => {
    if (!severity) return null;
    
    const colors = {
      high: 'bg-red-100 text-red-800',
      medium: 'bg-yellow-100 text-yellow-800',
      low: 'bg-green-100 text-green-800'
    };

    return (
      <span className={`px-2 py-1 rounded text-xs font-medium ${colors[severity as keyof typeof colors]}`}>
        {severity.toUpperCase()}
      </span>
    );
  };

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            M0 Real-Time Streaming Test
          </h1>
          <p className="text-gray-600">
            Server-Sent Events (SSE) endpoint testing interface
          </p>
        </div>

        {/* Connection Status */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
              <span className="text-lg font-semibold">
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
              {lastHeartbeat && (
                <span className="text-sm text-gray-500">
                  Last heartbeat: {new Date(lastHeartbeat).toLocaleTimeString()}
                </span>
              )}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={connectToStream}
                disabled={isConnected}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                Connect
              </button>
              <button
                onClick={disconnectFromStream}
                disabled={!isConnected}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                Disconnect
              </button>
              <button
                onClick={clearEventLog}
                className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
              >
                Clear Log
              </button>
            </div>
          </div>
        </div>

        {/* System Metrics */}
        {systemMetrics && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-bold mb-4">System Metrics</h2>
            <div className="grid grid-cols-5 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{systemMetrics.totalComponents}</div>
                <div className="text-sm text-gray-600">Total Components</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{systemMetrics.healthyComponents}</div>
                <div className="text-sm text-gray-600">Healthy</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{systemMetrics.errorComponents}</div>
                <div className="text-sm text-gray-600">Errors</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{systemMetrics.overallHealthScore}%</div>
                <div className="text-sm text-gray-600">Health Score</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{systemMetrics.averageResponseTime.toFixed(2)}ms</div>
                <div className="text-sm text-gray-600">Avg Response</div>
              </div>
            </div>
          </div>
        )}

        {/* Event Counts */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-bold mb-4">Event Statistics</h2>
          <div className="grid grid-cols-6 gap-4">
            {Object.entries(eventCounts).map(([type, count]) => (
              <div key={type} className="text-center">
                <div className="text-2xl font-bold text-gray-900">{count}</div>
                <div className="text-xs text-gray-600">{type}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Event Log */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold mb-4">Event Log ({eventLogs.length})</h2>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {eventLogs.length === 0 ? (
              <div className="text-center text-gray-500 py-8">
                No events received yet. Waiting for updates...
              </div>
            ) : (
              eventLogs.map(log => (
                <div
                  key={log.id}
                  className={`p-4 rounded-lg border ${getEventTypeColor(log.event.type)}`}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="font-semibold">{log.event.type}</span>
                      {getSeverityBadge(log.event.data.severity)}
                    </div>
                    <span className="text-xs text-gray-500">
                      {new Date(log.receivedAt).toLocaleTimeString()}
                    </span>
                  </div>
                  {log.event.data.message && (
                    <div className="text-sm mb-2">{log.event.data.message}</div>
                  )}
                  {log.event.data.componentName && (
                    <div className="text-xs text-gray-600">
                      Component: {log.event.data.componentName} ({log.event.data.category})
                    </div>
                  )}
                  {log.event.data.oldValue !== undefined && log.event.data.newValue !== undefined && (
                    <div className="text-xs text-gray-600">
                      Change: {log.event.data.oldValue} → {log.event.data.newValue}
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

