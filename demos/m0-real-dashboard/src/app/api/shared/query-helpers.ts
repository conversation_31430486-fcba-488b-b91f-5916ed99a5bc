/**
 * ============================================================================
 * Shared Query Helper Functions for M0 API Routes
 * ============================================================================
 * 
 * Priority 2 - Phase 2: Advanced Filtering & Querying
 * 
 * Reusable helper functions for parsing query parameters, filtering,
 * sorting, and paginating component data across all category-specific
 * API endpoints.
 * 
 * Features:
 * - Query parameter parsing and validation
 * - Component filtering (status, health score, search)
 * - Component sorting (name, healthScore, responseTime, lastUpdate)
 * - Pagination with configurable limits
 * 
 * Author: AI Assistant (Priority 2 Implementation)
 * Created: 2025-10-20
 * Authority: President & CEO, E.Z. Consultancy
 * ============================================================================
 */

import { NextRequest } from 'next/server';
import type { IM0ComponentStatus } from '../../../lib/M0ComponentManager';

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export interface IQueryParams {
  status?: 'healthy' | 'warning' | 'error' | 'offline';
  minHealth?: number;
  maxHealth?: number;
  search?: string;
  sortBy?: 'name' | 'healthScore' | 'responseTime' | 'lastUpdate';
  order?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface IPaginationResult {
  components: IM0ComponentStatus[];
  page: number;
  limit: number;
  totalPages: number;
}

// ============================================================================
// QUERY PARAMETER PARSING
// ============================================================================

/**
 * Parse and validate query parameters from request
 */
export function parseQueryParams(request: NextRequest): IQueryParams {
  const searchParams = request.nextUrl.searchParams;
  const params: IQueryParams = {};
  
  // Status filter
  const status = searchParams.get('status');
  if (status && ['healthy', 'warning', 'error', 'offline'].includes(status)) {
    params.status = status as IQueryParams['status'];
  }
  
  // Health score filters
  const minHealth = searchParams.get('minHealth');
  if (minHealth) {
    const parsed = parseInt(minHealth, 10);
    if (!isNaN(parsed) && parsed >= 0 && parsed <= 100) {
      params.minHealth = parsed;
    }
  }
  
  const maxHealth = searchParams.get('maxHealth');
  if (maxHealth) {
    const parsed = parseInt(maxHealth, 10);
    if (!isNaN(parsed) && parsed >= 0 && parsed <= 100) {
      params.maxHealth = parsed;
    }
  }
  
  // Search filter
  const search = searchParams.get('search');
  if (search && search.trim()) {
    params.search = search.trim();
  }
  
  // Sort parameters
  const sortBy = searchParams.get('sortBy');
  if (sortBy && ['name', 'healthScore', 'responseTime', 'lastUpdate'].includes(sortBy)) {
    params.sortBy = sortBy as IQueryParams['sortBy'];
  }
  
  const order = searchParams.get('order');
  if (order && ['asc', 'desc'].includes(order)) {
    params.order = order as IQueryParams['order'];
  }
  
  // Pagination
  const page = searchParams.get('page');
  if (page) {
    const parsed = parseInt(page, 10);
    if (!isNaN(parsed) && parsed > 0) {
      params.page = parsed;
    }
  }
  
  const limit = searchParams.get('limit');
  if (limit) {
    const parsed = parseInt(limit, 10);
    if (!isNaN(parsed) && parsed > 0 && parsed <= 100) {
      params.limit = parsed;
    }
  }
  
  return params;
}

// ============================================================================
// FILTERING
// ============================================================================

/**
 * Filter components based on query parameters
 */
export function filterComponents(
  components: IM0ComponentStatus[],
  params: IQueryParams
): IM0ComponentStatus[] {
  let filtered = [...components];
  
  // Filter by status
  if (params.status) {
    filtered = filtered.filter(c => c.status === params.status);
  }
  
  // Filter by minimum health score
  if (params.minHealth !== undefined) {
    filtered = filtered.filter(c => c.healthScore >= params.minHealth!);
  }
  
  // Filter by maximum health score
  if (params.maxHealth !== undefined) {
    filtered = filtered.filter(c => c.healthScore <= params.maxHealth!);
  }
  
  // Filter by search term (searches in name and id)
  if (params.search) {
    const searchLower = params.search.toLowerCase();
    filtered = filtered.filter(c => 
      c.name.toLowerCase().includes(searchLower) ||
      c.id.toLowerCase().includes(searchLower)
    );
  }
  
  return filtered;
}

// ============================================================================
// SORTING
// ============================================================================

/**
 * Sort components based on query parameters
 */
export function sortComponents(
  components: IM0ComponentStatus[],
  params: IQueryParams
): IM0ComponentStatus[] {
  if (!params.sortBy) {
    return components;
  }
  
  const sorted = [...components];
  const order = params.order || 'asc';
  const multiplier = order === 'asc' ? 1 : -1;
  
  sorted.sort((a, b) => {
    switch (params.sortBy) {
      case 'name':
        return a.name.toLowerCase().localeCompare(b.name.toLowerCase()) * multiplier;
      
      case 'healthScore':
        return (a.healthScore - b.healthScore) * multiplier;
      
      case 'responseTime':
        return (a.metrics.responseTime - b.metrics.responseTime) * multiplier;
      
      case 'lastUpdate':
        return (a.lastUpdate.getTime() - b.lastUpdate.getTime()) * multiplier;
      
      default:
        return 0;
    }
  });
  
  return sorted;
}

// ============================================================================
// PAGINATION
// ============================================================================

/**
 * Paginate components with configurable page size
 */
export function paginateComponents(
  components: IM0ComponentStatus[],
  params: IQueryParams
): IPaginationResult {
  const page = params.page || 1;
  const limit = params.limit || 50;
  
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  
  const paginatedComponents = components.slice(startIndex, endIndex);
  const totalPages = Math.ceil(components.length / limit);
  
  return {
    components: paginatedComponents,
    page,
    limit,
    totalPages
  };
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Apply all query operations (filter, sort, paginate) in sequence
 */
export function applyQueryOperations(
  components: IM0ComponentStatus[],
  params: IQueryParams
): {
  filtered: IM0ComponentStatus[];
  paginated: IPaginationResult;
  totalCount: number;
  filteredCount: number;
} {
  const totalCount = components.length;
  
  // Apply filters
  const filtered = filterComponents(components, params);
  const filteredCount = filtered.length;
  
  // Apply sorting
  const sorted = sortComponents(filtered, params);
  
  // Apply pagination
  const paginated = paginateComponents(sorted, params);
  
  return {
    filtered,
    paginated,
    totalCount,
    filteredCount
  };
}

/**
 * Build standard API response headers
 */
export function getAPIHeaders(): HeadersInit {
  return {
    'Content-Type': 'application/json',
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  };
}

/**
 * Build standard error response
 */
export function buildErrorResponse(
  error: unknown,
  message: string = 'API request failed'
): {
  success: false;
  error: string;
  details: string;
  timestamp: string;
} {
  return {
    success: false,
    error: message,
    details: error instanceof Error ? error.message : String(error),
    timestamp: new Date().toISOString()
  };
}

