/**
 * ============================================================================
 * M0.1 Enhancements Overview API Route
 * ============================================================================
 * 
 * This API route provides comprehensive overview data for M0.1 milestone
 * enhancements, including milestone statistics, task completion, and
 * performance metrics.
 * 
 * Features:
 * - Milestone statistics (50 tasks, 100% completion)
 * - Task breakdown by category
 * - Performance metrics and improvements
 * - Quality metrics and test coverage
 * - RESTful API for dashboard consumption
 * 
 * Author: AI Assistant (Phase 1 Day 1 Implementation)
 * Created: 2025-12-31
 * ============================================================================
 */

import { NextResponse } from 'next/server';
import { getM0ComponentManager } from '../../../../lib/M0ComponentManager';

/**
 * GET /api/m01-enhancements/overview
 * Returns comprehensive M0.1 milestone overview data
 */
export async function GET(): Promise<NextResponse> {
  try {
    console.log('📊 M0.1 Overview API: Processing GET request...');

    const manager = await getM0ComponentManager();
    
    // Check if M0.1 is initialized
    if (!manager.isM01Initialized()) {
      return NextResponse.json({
        success: false,
        error: 'M0.1 components not initialized',
        message: 'M0.1 enhanced components are not yet initialized',
        timestamp: new Date().toISOString()
      }, { status: 503 });
    }

    const m01Data = manager.getM01DashboardData();
    const stats = manager.getM01MilestoneStats();

    if (!m01Data) {
      return NextResponse.json({
        success: false,
        error: 'M0.1 data not available',
        message: 'M0.1 dashboard data is not available',
        timestamp: new Date().toISOString()
      }, { status: 503 });
    }

    // Build overview response
    const overviewData = {
      milestoneStats: stats,
      performanceMetrics: m01Data.performanceMetrics,
      comparisonData: m01Data.comparisonData,
      summary: {
        totalTasks: stats.totalTasks,
        completedTasks: stats.completedTasks,
        completionPercentage: stats.completionPercentage,
        totalLOC: stats.totalLOC,
        totalTestLOC: stats.totalTestLOC,
        averageCoverage: stats.averageCoverage,
        enhancedComponents: m01Data.enhancedComponents.length
      },
      categories: Object.entries(stats.categories).map(([name, data]) => ({
        name,
        ...data
      }))
    };

    console.log(`✅ M0.1 Overview API: Returning overview data (${stats.totalTasks} tasks, ${stats.completionPercentage}% complete)`);

    return NextResponse.json({
      success: true,
      data: overviewData,
      timestamp: new Date().toISOString()
    }, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=300', // Cache for 5 minutes
        'Pragma': 'cache',
      }
    });

  } catch (error) {
    console.error('❌ M0.1 Overview API: Error processing request:', error);

    return NextResponse.json({
      success: false,
      error: 'Failed to fetch M0.1 overview data',
      message: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * POST /api/m01-enhancements/overview
 * Refresh M0.1 overview data (future enhancement)
 */
export async function POST(): Promise<NextResponse> {
  try {
    console.log('🔄 M0.1 Overview API: Processing POST request (refresh)...');

    const manager = await getM0ComponentManager();
    const m01Data = manager.getM01DashboardData();

    if (!m01Data) {
      return NextResponse.json({
        success: false,
        error: 'M0.1 data not available',
        message: 'M0.1 dashboard data is not available',
        timestamp: new Date().toISOString()
      }, { status: 503 });
    }

    console.log('✅ M0.1 Overview API: Refresh completed');

    return NextResponse.json({
      success: true,
      data: m01Data,
      message: 'M0.1 overview data refreshed successfully',
      timestamp: new Date().toISOString()
    }, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

  } catch (error) {
    console.error('❌ M0.1 Overview API: Error processing refresh request:', error);

    return NextResponse.json({
      success: false,
      error: 'Failed to refresh M0.1 overview data',
      message: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

