/**
 * ============================================================================
 * M0.1 Enhanced Components API Route
 * ============================================================================
 * 
 * This API route provides detailed information about the 6 M0.1 enhanced
 * components, including features, metrics, and performance data.
 * 
 * Features:
 * - All 6 enhanced component specifications
 * - Component features and capabilities
 * - Performance metrics and test coverage
 * - Category-based filtering
 * - RESTful API for dashboard consumption
 * 
 * Author: AI Assistant (Phase 1 Day 1 Implementation)
 * Created: 2025-12-31
 * ============================================================================
 */

import { NextRequest, NextResponse } from 'next/server';
import { getM0ComponentManager } from '../../../../lib/M0ComponentManager';

/**
 * GET /api/m01-enhancements/components
 * Returns all M0.1 enhanced components or filtered by category
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    console.log('🔧 M0.1 Components API: Processing GET request...');

    const manager = await getM0ComponentManager();
    
    // Check if M0.1 is initialized
    if (!manager.isM01Initialized()) {
      return NextResponse.json({
        success: false,
        error: 'M0.1 components not initialized',
        message: 'M0.1 enhanced components are not yet initialized',
        timestamp: new Date().toISOString()
      }, { status: 503 });
    }

    const components = manager.getM01EnhancedComponents();
    
    // Get query parameters for filtering
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const componentId = searchParams.get('id');

    let filteredComponents = components;

    // Filter by component ID if provided
    if (componentId) {
      filteredComponents = components.filter(c => c.id === componentId);
      
      if (filteredComponents.length === 0) {
        return NextResponse.json({
          success: false,
          error: 'Component not found',
          message: `No component found with ID: ${componentId}`,
          timestamp: new Date().toISOString()
        }, { status: 404 });
      }
    }

    // Filter by category if provided
    if (category) {
      filteredComponents = filteredComponents.filter(c => c.category === category);
    }

    // Build response with component details
    const responseData = {
      totalComponents: components.length,
      filteredComponents: filteredComponents.length,
      components: filteredComponents.map(component => ({
        ...component,
        enhancementSummary: {
          featuresCount: component.features.length,
          linesOfCode: component.metrics.linesOfCode,
          testCoverage: component.metrics.testCoverage,
          performance: component.metrics.performance
        }
      })),
      categories: [...new Set(components.map(c => c.category))]
    };

    console.log(`✅ M0.1 Components API: Returning ${filteredComponents.length} components`);

    return NextResponse.json({
      success: true,
      data: responseData,
      timestamp: new Date().toISOString()
    }, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=300', // Cache for 5 minutes
        'Pragma': 'cache',
      }
    });

  } catch (error) {
    console.error('❌ M0.1 Components API: Error processing request:', error);

    return NextResponse.json({
      success: false,
      error: 'Failed to fetch M0.1 components data',
      message: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * POST /api/m01-enhancements/components
 * Get component health status (future enhancement)
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    console.log('🔍 M0.1 Components API: Processing POST request (health check)...');

    const body = await request.json();
    const { componentId } = body;

    if (!componentId) {
      return NextResponse.json({
        success: false,
        error: 'Missing component ID',
        message: 'componentId is required in request body',
        timestamp: new Date().toISOString()
      }, { status: 400 });
    }

    const manager = await getM0ComponentManager();
    const status = manager.getM01ComponentStatus(componentId);

    if (!status) {
      return NextResponse.json({
        success: false,
        error: 'Component not found',
        message: `No component found with ID: ${componentId}`,
        timestamp: new Date().toISOString()
      }, { status: 404 });
    }

    console.log(`✅ M0.1 Components API: Health check completed for ${componentId}`);

    return NextResponse.json({
      success: true,
      data: status,
      timestamp: new Date().toISOString()
    }, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

  } catch (error) {
    console.error('❌ M0.1 Components API: Error processing health check request:', error);

    return NextResponse.json({
      success: false,
      error: 'Failed to check component health',
      message: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

