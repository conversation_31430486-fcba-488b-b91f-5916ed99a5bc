/**
 * ============================================================================
 * M0 vs M0.1 Comparison API Route
 * ============================================================================
 * 
 * This API route provides side-by-side comparison data between M0 base
 * components and M0.1 enhanced components, highlighting improvements and
 * new features.
 * 
 * Features:
 * - M0 vs M0.1 component comparison
 * - Performance improvement metrics
 * - Feature enhancement comparison
 * - Test coverage comparison
 * - RESTful API for dashboard consumption
 * 
 * Author: AI Assistant (Phase 1 Day 1 Implementation)
 * Created: 2025-12-31
 * ============================================================================
 */

import { NextRequest, NextResponse } from 'next/server';
import { getM0ComponentManager } from '../../../../lib/M0ComponentManager';

/**
 * GET /api/m01-enhancements/comparison
 * Returns comprehensive M0 vs M0.1 comparison data
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    console.log('⚖️ M0.1 Comparison API: Processing GET request...');

    const manager = await getM0ComponentManager();
    
    // Check if M0.1 is initialized
    if (!manager.isM01Initialized()) {
      return NextResponse.json({
        success: false,
        error: 'M0.1 components not initialized',
        message: 'M0.1 enhanced components are not yet initialized',
        timestamp: new Date().toISOString()
      }, { status: 503 });
    }

    const m0Data = manager.getDashboardData();
    const m01Data = manager.getM01DashboardData();
    const components = manager.getM01EnhancedComponents();

    if (!m01Data) {
      return NextResponse.json({
        success: false,
        error: 'M0.1 data not available',
        message: 'M0.1 dashboard data is not available',
        timestamp: new Date().toISOString()
      }, { status: 503 });
    }

    // Get query parameters for filtering
    const { searchParams } = new URL(request.url);
    const componentId = searchParams.get('component');

    // Build component-level comparison
    const componentComparisons = components.map(enhancedComp => {
      // Find base component in M0 data
      const baseComponent = m0Data.categories.memorySafety.find(
        c => c.name === enhancedComp.baseComponent
      );

      return {
        componentId: enhancedComp.id,
        name: enhancedComp.name,
        baseComponent: enhancedComp.baseComponent,
        enhancements: {
          newFeatures: enhancedComp.features.length,
          features: enhancedComp.features,
          performanceImprovement: enhancedComp.metrics.performance,
          linesOfCode: enhancedComp.metrics.linesOfCode,
          testCoverage: enhancedComp.metrics.testCoverage
        },
        baseMetrics: baseComponent ? {
          healthScore: baseComponent.healthScore,
          responseTime: baseComponent.metrics.responseTime,
          status: baseComponent.status
        } : null,
        improvementSummary: {
          featuresAdded: enhancedComp.features.length,
          performanceGain: enhancedComp.metrics.performance,
          category: enhancedComp.category
        }
      };
    });

    // Filter by component if specified
    const filteredComparisons = componentId
      ? componentComparisons.filter(c => c.componentId === componentId)
      : componentComparisons;

    // Build overall comparison summary
    const comparisonSummary = {
      m0Stats: {
        totalComponents: m0Data.totalComponents,
        healthyComponents: m0Data.healthyComponents,
        overallHealthScore: m0Data.overallHealthScore,
        averageResponseTime: m0Data.systemMetrics.averageResponseTime
      },
      m01Stats: {
        totalTasks: m01Data.milestoneStats.totalTasks,
        completedTasks: m01Data.milestoneStats.completedTasks,
        completionPercentage: m01Data.milestoneStats.completionPercentage,
        enhancedComponents: components.length,
        averagePerformance: m01Data.performanceMetrics.averagePerformanceGain,
        averageCoverage: m01Data.performanceMetrics.averageCoverage
      },
      improvements: {
        newFeatures: m01Data.comparisonData.newFeatures,
        performanceImprovements: m01Data.comparisonData.performanceImprovements,
        totalLOC: m01Data.performanceMetrics.totalLOC,
        totalTestLOC: m01Data.performanceMetrics.totalTestLOC
      },
      componentComparisons: filteredComparisons
    };

    console.log(`✅ M0.1 Comparison API: Returning comparison data (${filteredComparisons.length} components)`);

    return NextResponse.json({
      success: true,
      data: comparisonSummary,
      timestamp: new Date().toISOString()
    }, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=300', // Cache for 5 minutes
        'Pragma': 'cache',
      }
    });

  } catch (error) {
    console.error('❌ M0.1 Comparison API: Error processing request:', error);

    return NextResponse.json({
      success: false,
      error: 'Failed to fetch M0.1 comparison data',
      message: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

