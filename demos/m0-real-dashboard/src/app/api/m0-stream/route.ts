/**
 * ============================================================================
 * M0 REAL-TIME STREAMING API ROUTE (SSE)
 * ============================================================================
 * 
 * Priority 2 - Phase 3: Real-Time Data Streaming
 * 
 * Server-Sent Events (SSE) endpoint for real-time M0 component updates
 * Provides live component status changes, health score updates, and system events
 * 
 * Event Types:
 * - component-status-change: Component status transitions (healthy → error, etc.)
 * - health-score-change: Significant health score changes (threshold: ±5 points)
 * - error-detected: Immediate error notifications
 * - warning-detected: Warning notifications
 * - system-metric-update: Periodic system-wide metrics
 * - heartbeat: Connection health check (every 30 seconds)
 * 
 * Usage:
 * ```javascript
 * const eventSource = new EventSource('/api/m0-stream');
 * eventSource.addEventListener('component-status-change', (event) => {
 *   const data = JSON.parse(event.data);
 *   console.log('Status changed:', data);
 * });
 * ```
 * 
 * @route GET /api/m0-stream - Establish SSE connection for real-time updates
 * 
 * Author: AI Assistant (Priority 2 Phase 3 Implementation)
 * Created: 2025-10-20
 * Authority: President & CEO, E.Z. Consultancy
 * ============================================================================
 */

import { NextRequest } from 'next/server';
import { getM0ComponentManager } from '../../../lib/M0ComponentManager';
import type { IM0ComponentStatus } from '../../../lib/M0ComponentManager';

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export interface IStreamEvent {
  type: 'component-status-change' | 'health-score-change' | 'error-detected' | 'warning-detected' | 'system-metric-update' | 'heartbeat';
  timestamp: string;
  data: {
    componentId?: string;
    componentName?: string;
    category?: string;
    oldValue?: any;
    newValue?: any;
    message?: string;
    severity?: 'low' | 'medium' | 'high';
    systemMetrics?: {
      totalComponents: number;
      healthyComponents: number;
      errorComponents: number;
      overallHealthScore: number;
      averageResponseTime: number;
    };
  };
}

interface IComponentSnapshot {
  status: string;
  healthScore: number;
  errorRate: number;
}

// ============================================================================
// GLOBAL STATE FOR EVENT STREAMING
// ============================================================================

// Track component snapshots for change detection
const componentSnapshots = new Map<string, IComponentSnapshot>();

// Track active connections for cleanup
const activeConnections = new Set<ReadableStreamDefaultController>();

// Update interval (5 seconds)
const UPDATE_INTERVAL_MS = 5000;

// Heartbeat interval (30 seconds)
const HEARTBEAT_INTERVAL_MS = 30000;

// Health score change threshold
const HEALTH_SCORE_THRESHOLD = 5;

// ============================================================================
// SSE HELPER FUNCTIONS
// ============================================================================

/**
 * Format SSE message according to specification
 */
function formatSSEMessage(event: IStreamEvent): string {
  return `event: ${event.type}\ndata: ${JSON.stringify(event.data)}\nid: ${Date.now()}\n\n`;
}

/**
 * Send SSE event to client
 */
function sendEvent(controller: ReadableStreamDefaultController, event: IStreamEvent): void {
  try {
    const message = formatSSEMessage(event);
    controller.enqueue(new TextEncoder().encode(message));
  } catch (error) {
    console.error('❌ Error sending SSE event:', error);
  }
}

/**
 * Detect component status changes
 */
function detectStatusChange(componentId: string, current: IM0ComponentStatus): IStreamEvent | null {
  const snapshot = componentSnapshots.get(componentId);
  
  if (!snapshot) {
    // First time seeing this component - store snapshot
    componentSnapshots.set(componentId, {
      status: current.status,
      healthScore: current.healthScore,
      errorRate: current.metrics.errorRate
    });
    return null;
  }
  
  // Check for status change
  if (snapshot.status !== current.status) {
    const event: IStreamEvent = {
      type: 'component-status-change',
      timestamp: new Date().toISOString(),
      data: {
        componentId: current.id,
        componentName: current.name,
        category: current.category,
        oldValue: snapshot.status,
        newValue: current.status,
        message: `Component ${current.name} status changed from ${snapshot.status} to ${current.status}`,
        severity: current.status === 'error' ? 'high' : current.status === 'warning' ? 'medium' : 'low'
      }
    };
    
    // Update snapshot
    snapshot.status = current.status;
    
    return event;
  }
  
  return null;
}

/**
 * Detect health score changes
 */
function detectHealthScoreChange(componentId: string, current: IM0ComponentStatus): IStreamEvent | null {
  const snapshot = componentSnapshots.get(componentId);
  
  if (!snapshot) return null;
  
  const scoreDiff = Math.abs(current.healthScore - snapshot.healthScore);
  
  if (scoreDiff >= HEALTH_SCORE_THRESHOLD) {
    const event: IStreamEvent = {
      type: 'health-score-change',
      timestamp: new Date().toISOString(),
      data: {
        componentId: current.id,
        componentName: current.name,
        category: current.category,
        oldValue: snapshot.healthScore,
        newValue: current.healthScore,
        message: `Component ${current.name} health score changed from ${snapshot.healthScore} to ${current.healthScore}`,
        severity: current.healthScore < 70 ? 'high' : current.healthScore < 90 ? 'medium' : 'low'
      }
    };
    
    // Update snapshot
    snapshot.healthScore = current.healthScore;
    
    return event;
  }
  
  return null;
}

/**
 * Detect errors
 */
function detectError(current: IM0ComponentStatus): IStreamEvent | null {
  if (current.status === 'error') {
    return {
      type: 'error-detected',
      timestamp: new Date().toISOString(),
      data: {
        componentId: current.id,
        componentName: current.name,
        category: current.category,
        message: `Error detected in component ${current.name}`,
        severity: 'high'
      }
    };
  }
  
  return null;
}

/**
 * Detect warnings
 */
function detectWarning(current: IM0ComponentStatus): IStreamEvent | null {
  if (current.status === 'warning') {
    return {
      type: 'warning-detected',
      timestamp: new Date().toISOString(),
      data: {
        componentId: current.id,
        componentName: current.name,
        category: current.category,
        message: `Warning detected in component ${current.name}`,
        severity: 'medium'
      }
    };
  }
  
  return null;
}

/**
 * Create system metrics update event
 */
function createSystemMetricsEvent(manager: any): IStreamEvent {
  const dashboardData = manager.getDashboardData();
  
  return {
    type: 'system-metric-update',
    timestamp: new Date().toISOString(),
    data: {
      message: 'System metrics updated',
      severity: 'low',
      systemMetrics: {
        totalComponents: dashboardData.totalComponents,
        healthyComponents: dashboardData.healthyComponents,
        errorComponents: dashboardData.errorComponents,
        overallHealthScore: dashboardData.overallHealthScore,
        averageResponseTime: dashboardData.systemMetrics.averageResponseTime
      }
    }
  };
}

/**
 * Create heartbeat event
 */
function createHeartbeatEvent(): IStreamEvent {
  return {
    type: 'heartbeat',
    timestamp: new Date().toISOString(),
    data: {
      message: 'Connection alive',
      severity: 'low'
    }
  };
}

/**
 * Process component updates and detect changes
 */
async function processComponentUpdates(controller: ReadableStreamDefaultController): Promise<void> {
  try {
    const manager = await getM0ComponentManager();
    const components = manager.getAllComponents();
    
    // Check each component for changes
    for (const component of components) {
      // Detect status changes
      const statusChange = detectStatusChange(component.id, component);
      if (statusChange) {
        sendEvent(controller, statusChange);
      }
      
      // Detect health score changes
      const healthChange = detectHealthScoreChange(component.id, component);
      if (healthChange) {
        sendEvent(controller, healthChange);
      }
      
      // Detect errors (only send if not already sent via status change)
      if (!statusChange && component.status === 'error') {
        const errorEvent = detectError(component);
        if (errorEvent) {
          sendEvent(controller, errorEvent);
        }
      }
      
      // Detect warnings (only send if not already sent via status change)
      if (!statusChange && component.status === 'warning') {
        const warningEvent = detectWarning(component);
        if (warningEvent) {
          sendEvent(controller, warningEvent);
        }
      }
    }
    
    // Send system metrics update
    const metricsEvent = createSystemMetricsEvent(manager);
    sendEvent(controller, metricsEvent);
    
  } catch (error) {
    console.error('❌ Error processing component updates:', error);
  }
}

// ============================================================================
// API HANDLER
// ============================================================================

/**
 * GET /api/m0-stream
 * Establish SSE connection for real-time M0 component updates
 */
export async function GET(request: NextRequest) {
  console.log('🔄 M0 Stream API: New SSE connection request');
  
  // Create readable stream for SSE
  const stream = new ReadableStream({
    async start(controller) {
      console.log('✅ M0 Stream API: SSE connection established');
      
      // Add to active connections
      activeConnections.add(controller);
      
      // Send initial connection event
      sendEvent(controller, {
        type: 'system-metric-update',
        timestamp: new Date().toISOString(),
        data: {
          message: 'SSE connection established',
          severity: 'low'
        }
      });
      
      // Set up update interval
      const updateInterval = setInterval(async () => {
        try {
          await processComponentUpdates(controller);
        } catch (error) {
          console.error('❌ Update interval error:', error);
        }
      }, UPDATE_INTERVAL_MS);
      
      // Set up heartbeat interval
      const heartbeatInterval = setInterval(() => {
        try {
          sendEvent(controller, createHeartbeatEvent());
        } catch (error) {
          console.error('❌ Heartbeat error:', error);
        }
      }, HEARTBEAT_INTERVAL_MS);
      
      // Handle cleanup on connection close
      request.signal.addEventListener('abort', () => {
        console.log('🔌 M0 Stream API: Client disconnected');
        clearInterval(updateInterval);
        clearInterval(heartbeatInterval);
        activeConnections.delete(controller);
        
        try {
          controller.close();
        } catch (error) {
          // Controller may already be closed
        }
      });
    },
    
    cancel() {
      console.log('❌ M0 Stream API: Stream cancelled');
    }
  });
  
  // Return SSE response with proper headers
  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache, no-transform',
      'Connection': 'keep-alive',
      'X-Accel-Buffering': 'no' // Disable nginx buffering
    }
  });
}

