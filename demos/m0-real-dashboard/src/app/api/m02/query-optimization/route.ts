/**
 * ============================================================================
 * QUERY OPTIMIZATION API ROUTE - M0.2 FEATURE 1.1
 * ============================================================================
 * 
 * Real PostgreSQL query optimization endpoint
 * 
 * Features:
 * - Real database query execution
 * - EXPLAIN ANALYZE for performance metrics
 * - Query optimization recommendations
 * - Execution plan visualization data
 * 
 * Authority: President & CEO, E.Z. Consultancy
 * Created: 2026-01-09
 * Milestone: M0.2 Demo - Feature 1.1 (Real Database Integration)
 * ============================================================================
 */

import { NextRequest, NextResponse } from 'next/server';
import { 
  getExecutionPlan, 
  analyzeQueryPerformance, 
  testConnection,
  executeQuery 
} from '@/lib/m02/db-connection';
import { 
  IQueryOptimizationRequest, 
  IQueryOptimizationResult 
} from '@/lib/m02/query-optimization-api';

/**
 * POST /api/m02/query-optimization
 * Analyze and optimize SQL queries using real PostgreSQL database
 */
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json() as IQueryOptimizationRequest;
    const { query } = body;

    if (!query || typeof query !== 'string') {
      return NextResponse.json(
        { error: 'Invalid query parameter' },
        { status: 400 }
      );
    }

    // Test database connection
    const isConnected = await testConnection();
    if (!isConnected) {
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 503 }
      );
    }

    // Get execution plan and performance metrics
    const performanceData = await analyzeQueryPerformance(query);
    const plan = performanceData.plan[0] as {
      Plan?: {
        'Node Type'?: string;
        'Total Cost'?: number;
        'Plan Rows'?: number;
        Plans?: unknown[];
      };
      'Execution Time'?: number;
      'Planning Time'?: number;
    };

    // Extract execution plan tree
    const executionPlan = convertPlanToTree(plan.Plan);

    // Generate optimization recommendations
    const recommendations = generateRecommendations(query, plan);

    // Calculate metrics
    const estimatedCost = plan.Plan?.['Total Cost'] || 0;
    const estimatedRows = plan.Plan?.['Plan Rows'] || 0;
    const executionTime = performanceData.executionTime;
    const planningTime = performanceData.planningTime;

    // Determine complexity
    const complexity = estimatedCost < 100 ? 'low' : estimatedCost < 1000 ? 'medium' : 'high';

    // Build response
    const result: IQueryOptimizationResult = {
      originalQuery: query,
      executionPlan,
      metrics: {
        estimatedCost,
        estimatedRows,
        estimatedTime: executionTime + planningTime,
        complexity,
      },
      recommendations,
      performance: {
        before: {
          executionTime,
          cpuUsage: 0, // PostgreSQL doesn't provide CPU usage directly
          memoryUsage: 0, // PostgreSQL doesn't provide memory usage directly
        },
      },
    };

    return NextResponse.json(result);
  } catch (error) {
    console.error('Query optimization error:', error);
    return NextResponse.json(
      { 
        error: 'Query optimization failed', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/m02/query-optimization
 * Test database connection
 */
export async function GET() {
  try {
    const isConnected = await testConnection();
    return NextResponse.json({ 
      connected: isConnected,
      message: isConnected ? 'Database connected' : 'Database connection failed'
    });
  } catch (error) {
    return NextResponse.json(
      { 
        connected: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

/**
 * Convert PostgreSQL execution plan to tree structure
 */
function convertPlanToTree(plan: unknown, parentId = 'root', counter = { value: 0 }): {
  id: string;
  type: string;
  operation: string;
  cost: number;
  rows: number;
  children?: unknown[];
  details?: Record<string, unknown>;
} {
  if (!plan || typeof plan !== 'object') {
    return {
      id: parentId,
      type: 'Unknown',
      operation: 'Unknown',
      cost: 0,
      rows: 0,
    };
  }

  const planObj = plan as {
    'Node Type'?: string;
    'Total Cost'?: number;
    'Plan Rows'?: number;
    'Relation Name'?: string;
    'Index Name'?: string;
    'Startup Cost'?: number;
    Plans?: unknown[];
    [key: string]: unknown;
  };

  const nodeId = `node-${counter.value++}`;
  const nodeType = planObj['Node Type'] || 'Unknown';
  const relationName = planObj['Relation Name'] ? ` on ${planObj['Relation Name']}` : '';
  const indexName = planObj['Index Name'] ? ` using ${planObj['Index Name']}` : '';

  const node = {
    id: nodeId,
    type: nodeType,
    operation: `${nodeType}${relationName}${indexName}`,
    cost: planObj['Total Cost'] || 0,
    rows: planObj['Plan Rows'] || 0,
    details: {
      startupCost: planObj['Startup Cost'],
      totalCost: planObj['Total Cost'],
      planRows: planObj['Plan Rows'],
      relationName: planObj['Relation Name'],
      indexName: planObj['Index Name'],
    },
    children: [] as unknown[],
  };

  // Process child plans
  if (planObj.Plans && Array.isArray(planObj.Plans)) {
    node.children = planObj.Plans.map((childPlan) =>
      convertPlanToTree(childPlan, nodeId, counter)
    );
  }

  return node;
}

/**
 * Generate optimization recommendations based on execution plan
 */
function generateRecommendations(query: string, plan: unknown): Array<{
  id: string;
  type: 'index' | 'rewrite' | 'schema' | 'configuration';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  impact: {
    performanceGain: number;
    estimatedImprovement: string;
  };
  implementation?: string;
}> {
  const recommendations: Array<{
    id: string;
    type: 'index' | 'rewrite' | 'schema' | 'configuration';
    priority: 'high' | 'medium' | 'low';
    title: string;
    description: string;
    impact: {
      performanceGain: number;
      estimatedImprovement: string;
    };
    implementation?: string;
  }> = [];

  const planObj = plan as {
    Plan?: {
      'Node Type'?: string;
      'Relation Name'?: string;
      'Total Cost'?: number;
      Plans?: unknown[];
    };
  };

  const nodeType = planObj.Plan?.['Node Type'];
  const relationName = planObj.Plan?.['Relation Name'];
  const totalCost = planObj.Plan?.['Total Cost'] || 0;

  // Check for sequential scans (potential index opportunity)
  if (nodeType === 'Seq Scan' && relationName) {
    recommendations.push({
      id: 'rec-1',
      type: 'index',
      priority: 'high',
      title: 'Add Index for Sequential Scan',
      description: `Sequential scan detected on table "${relationName}". Consider adding an index to improve query performance.`,
      impact: {
        performanceGain: 60,
        estimatedImprovement: 'Up to 60% faster query execution',
      },
      implementation: `CREATE INDEX idx_${relationName}_column ON ${relationName}(column_name);`,
    });
  }

  // Check for high cost queries
  if (totalCost > 1000) {
    recommendations.push({
      id: 'rec-2',
      type: 'rewrite',
      priority: 'high',
      title: 'Optimize Query Structure',
      description: 'High query cost detected. Consider rewriting the query to reduce complexity.',
      impact: {
        performanceGain: 40,
        estimatedImprovement: 'Up to 40% reduction in query cost',
      },
      implementation: 'Review WHERE clauses, JOIN conditions, and consider using CTEs or subqueries.',
    });
  }

  // Check for missing WHERE clause
  if (!query.toLowerCase().includes('where') && !query.toLowerCase().includes('limit')) {
    recommendations.push({
      id: 'rec-3',
      type: 'rewrite',
      priority: 'medium',
      title: 'Add WHERE Clause or LIMIT',
      description: 'Query may return all rows. Consider adding a WHERE clause or LIMIT to reduce data transfer.',
      impact: {
        performanceGain: 30,
        estimatedImprovement: 'Reduced network transfer and faster response',
      },
      implementation: 'Add WHERE clause to filter results or LIMIT to restrict row count.',
    });
  }

  // General configuration recommendation
  if (totalCost > 500) {
    recommendations.push({
      id: 'rec-4',
      type: 'configuration',
      priority: 'low',
      title: 'Review Database Configuration',
      description: 'Consider tuning PostgreSQL configuration parameters for better performance.',
      impact: {
        performanceGain: 15,
        estimatedImprovement: 'Overall database performance improvement',
      },
      implementation: 'Review shared_buffers, work_mem, and effective_cache_size settings.',
    });
  }

  return recommendations;
}

