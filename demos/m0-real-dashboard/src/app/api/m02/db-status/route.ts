/**
 * ============================================================================
 * DATABASE STATUS API ROUTE - M0.2 FEATURE 1.1
 * ============================================================================
 * 
 * Database connection status and statistics endpoint
 * 
 * Features:
 * - Connection health check
 * - Database statistics
 * - Version information
 * 
 * Authority: President & CEO, E.Z. Consultancy
 * Created: 2026-01-09
 * Milestone: M0.2 Demo - Feature 1.1 (Real Database Integration)
 * ============================================================================
 */

import { NextResponse } from 'next/server';
import { testConnection, getDatabaseStats } from '@/lib/m02/db-connection';

/**
 * GET /api/m02/db-status
 * Get database connection status and statistics
 */
export async function GET() {
  try {
    // Test connection
    const isConnected = await testConnection();

    if (!isConnected) {
      return NextResponse.json({
        connected: false,
        message: 'Database connection failed',
        timestamp: new Date().toISOString(),
      });
    }

    // Get database statistics
    const stats = await getDatabaseStats();

    return NextResponse.json({
      connected: true,
      message: 'Database connected successfully',
      timestamp: new Date().toISOString(),
      stats: {
        version: stats.version,
        database: stats.currentDatabase,
        user: stats.currentUser,
        activeConnections: stats.activeConnections,
      },
    });
  } catch (error) {
    console.error('Database status check failed:', error);
    return NextResponse.json(
      {
        connected: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

