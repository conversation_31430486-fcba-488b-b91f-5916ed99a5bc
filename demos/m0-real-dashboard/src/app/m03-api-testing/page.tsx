'use client';

/**
 * ============================================================================
 * M0.3 API TESTING PAGE
 * ============================================================================
 * 
 * @fileoverview Page route for M0.3 API Testing Interface
 * @filepath demos/m0-real-dashboard/src/app/m03-api-testing/page.tsx
 * @authority President & CEO, E<PERSON>Z<PERSON> Consultancy
 * @created 2026-01-29
 * @status ACTIVE - M0.3 Feature 3.2 Implementation
 */

import React from 'react';
import { Box, Container } from '@mui/material';
import { APITestingInterface } from '../../components/m03';

export default function M03APITestingPage() {
  return (
    <Box sx={{ minHeight: '100vh', py: 4, backgroundColor: '#f5f5f5' }}>
      <Container maxWidth="xl">
        <APITestingInterface />
      </Container>
    </Box>
  );
}
