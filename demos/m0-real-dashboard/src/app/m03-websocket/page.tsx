'use client';

/**
 * ============================================================================
 * M0.3 WebSocket Real-Time Monitor Page
 * 
 * Feature 2.4: WebSocket Real-Time Monitor
 * Purpose: Dedicated monitor for WebSocket infrastructure showing connection
 * status, event payloads, client distribution, and broadcast latency metrics
 * 
 * Authority: President & CEO, E<PERSON>Z. Consultancy
 * ============================================================================
 */

import React from 'react';
import { Container, Box, Typography, Paper } from '@mui/material';
import WebSocketMonitor from '@/components/m03/WebSocketMonitor';

export default function M03WebSocketPage() {
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Paper sx={{ p: 3, mb: 4, background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          🔌 WebSocket Real-Time Monitor
        </Typography>
        <Typography variant="body1">
          Dedicated monitor for WebSocket infrastructure with connection status, event payloads, client distribution, and broadcast latency metrics
        </Typography>
      </Paper>

      <Box sx={{ mt: 4 }}>
        <WebSocketMonitor autoConnect={true} maxEvents={100} />
      </Box>
    </Container>
  );
}
