/**
 * ============================================================================
 * TRACKING DASHBOARD - Main Page
 * ============================================================================
 *
 * Comprehensive tracking dashboard integrating all tracking components with
 * real-time monitoring, operations, and alert management.
 *
 * Features:
 * - Real-time data updates (30-second auto-refresh)
 * - 9 visualization components
 * - Tracking operations (session-analysis, component-health-check, event-timeline)
 * - Alert management with notifications
 * - Responsive grid layout
 * - Loading and error states
 *
 * Authority: President & CEO, E.Z. Consultancy
 * Created: 2025-10-22
 * ============================================================================
 */

'use client';

import React, { useState, useMemo } from 'react';
import Link from 'next/link';
import { ArrowLeft, RefreshCw, Bell, BellOff } from 'lucide-react';
import { useTrackingData } from '../../hooks/useTrackingData';
import { useTrackingOperations } from '../../hooks/useTrackingOperations';
import { useTrackingAlerts } from '../../hooks/useTrackingAlerts';
import { TrackingOverviewPanel } from '../../components/tracking/TrackingOverviewPanel';
import { SessionAnalyticsChart } from '../../components/tracking/SessionAnalyticsChart';
import { ComponentHealthMonitor } from '../../components/tracking/ComponentHealthMonitor';
import { EventTimelineChart } from '../../components/tracking/EventTimelineChart';
import { ComponentStatusGrid } from '../../components/tracking/ComponentStatusGrid';
import { TrackingOperationsPanel } from '../../components/tracking/TrackingOperationsPanel';
import { OperationResultsDisplay } from '../../components/tracking/OperationResultsDisplay';
import { AlertNotification } from '../../components/tracking/AlertNotification';
import { AlertHistoryPanel } from '../../components/tracking/AlertHistoryPanel';
import type { IEventTimelineEntry } from '../../types/tracking-types';

export default function TrackingDashboardPage(): JSX.Element {
  const [showAlerts, setShowAlerts] = useState(true);

  // Data fetching with auto-refresh
  const { data, loading, error, lastUpdate, refresh, isRefreshing } = useTrackingData(30000, true);

  // Operations management
  const {
    operationStatus,
    operationResult,
    runSessionAnalysis,
    runComponentHealthCheck,
    runEventTimeline,
  } = useTrackingOperations();

  // Alert management
  const { alerts, unacknowledgedCount, acknowledgeAlert, clearAlert, clearAll } = useTrackingAlerts(data);

  /**
   * Generate mock event timeline data
   * In production, this would come from real event data
   */
  const eventTimelineData = useMemo((): IEventTimelineEntry[] => {
    if (!data) return [];

    const events: IEventTimelineEntry[] = [];
    const now = Date.now();

    // Generate events based on component status
    data.components.slice(0, 10).forEach((component, index) => {
      const timestamp = new Date(now - index * 5 * 60 * 1000).toISOString();

      if (component.status === 'error') {
        events.push({
          id: `event-${component.id}-error`,
          timestamp,
          eventType: 'error',
          componentId: component.id,
          componentName: component.name,
          message: `Component error detected: ${component.name}`,
          severity: 'critical',
          metadata: { healthScore: component.healthScore },
        });
      } else if (component.status === 'warning') {
        events.push({
          id: `event-${component.id}-warning`,
          timestamp,
          eventType: 'warning',
          componentId: component.id,
          componentName: component.name,
          message: `Component warning: ${component.name}`,
          severity: 'warning',
          metadata: { healthScore: component.healthScore },
        });
      } else {
        events.push({
          id: `event-${component.id}-info`,
          timestamp,
          eventType: 'info',
          componentId: component.id,
          componentName: component.name,
          message: `Component healthy: ${component.name}`,
          severity: 'info',
          metadata: { healthScore: component.healthScore },
        });
      }
    });

    return events;
  }, [data]);

  /**
   * Handle manual refresh
   */
  const handleRefresh = async () => {
    await refresh();
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link
                href="/"
                className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Tracking Dashboard
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Real-time tracking component monitoring and analytics
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              {/* Alert Toggle */}
              <button
                onClick={() => setShowAlerts(!showAlerts)}
                className="relative p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                aria-label="Toggle alerts"
              >
                {showAlerts ? <Bell className="w-5 h-5" /> : <BellOff className="w-5 h-5" />}
                {unacknowledgedCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-600 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center">
                    {unacknowledgedCount}
                  </span>
                )}
              </button>

              {/* Refresh Button */}
              <button
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg text-sm font-medium transition-colors"
              >
                <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                Refresh
              </button>

              {/* Last Update */}
              <div className="text-xs text-gray-600 dark:text-gray-400">
                Last update: {lastUpdate.toLocaleTimeString()}
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Error State */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-500 rounded-lg p-4 mb-6">
            <h3 className="text-sm font-semibold text-red-900 dark:text-red-100 mb-1">
              Error Loading Data
            </h3>
            <p className="text-xs text-red-700 dark:text-red-300">{error}</p>
          </div>
        )}

        {/* Alert Notifications */}
        {showAlerts && alerts.filter(a => !a.acknowledged).length > 0 && (
          <div className="mb-6 space-y-3">
            {alerts
              .filter(a => !a.acknowledged)
              .slice(0, 3)
              .map(alert => (
                <AlertNotification
                  key={alert.id}
                  alert={alert}
                  onAcknowledge={acknowledgeAlert}
                  onDismiss={clearAlert}
                />
              ))}
          </div>
        )}

        {/* Dashboard Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Overview Panel */}
            <TrackingOverviewPanel data={data} loading={loading} />

            {/* Session Analytics Chart */}
            {data && <SessionAnalyticsChart metrics={data.metrics} />}

            {/* Component Health Monitor */}
            {data && <ComponentHealthMonitor components={data.components} />}

            {/* Event Timeline Chart */}
            <EventTimelineChart events={eventTimelineData} />

            {/* Component Status Grid */}
            <ComponentStatusGrid components={data?.components || []} loading={loading} />
          </div>

          {/* Right Column - Operations & Alerts */}
          <div className="space-y-6">
            {/* Tracking Operations Panel */}
            <TrackingOperationsPanel
              onRunSessionAnalysis={runSessionAnalysis}
              onRunComponentHealthCheck={runComponentHealthCheck}
              onRunEventTimeline={runEventTimeline}
              operationStatus={operationStatus}
            />

            {/* Operation Results Display */}
            <OperationResultsDisplay result={operationResult} status={operationStatus} />

            {/* Alert History Panel */}
            <AlertHistoryPanel
              alerts={alerts}
              onAcknowledge={acknowledgeAlert}
              onClear={clearAlert}
              onClearAll={clearAll}
            />
          </div>
        </div>
      </main>
    </div>
  );
}

