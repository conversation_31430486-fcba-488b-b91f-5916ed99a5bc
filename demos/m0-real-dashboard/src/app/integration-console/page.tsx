/**
 * ============================================================================
 * INTEGRATION CONSOLE PAGE
 * ============================================================================
 *
 * Main integration console page integrating all integration components
 *
 * Features:
 * - Real-time integration monitoring
 * - Cross-component testing
 * - Dependency visualization
 * - Integration operations
 * - Auto-refresh (30-second interval)
 * - Responsive grid layout
 *
 * Author: AI Assistant (Phase 3D Implementation)
 * Created: 2025-10-22
 * Authority: President & CEO, E.Z. Consultancy
 * ============================================================================
 */

'use client';

import React, { useState } from 'react';
import { RefreshCw } from 'lucide-react';
import { useIntegrationData } from '../../hooks/useIntegrationData';
import { useIntegrationOperations } from '../../hooks/useIntegrationOperations';
import { IntegrationOverviewPanel } from '../../components/integration/IntegrationOverviewPanel';
import { CrossComponentTestPanel } from '../../components/integration/CrossComponentTestPanel';
import { DependencyGraph } from '../../components/integration/DependencyGraph';
import { IntegrationStatusGrid } from '../../components/integration/IntegrationStatusGrid';
import { TestExecutionPanel } from '../../components/integration/TestExecutionPanel';
import { TestResultsDisplay } from '../../components/integration/TestResultsDisplay';
import type { TIntegrationOperation } from '../../types/integration-types';

// ============================================================================
// PAGE COMPONENT
// ============================================================================

export default function IntegrationConsolePage(): React.ReactElement {
  // Data fetching
  const { data, loading, error, refetch } = useIntegrationData();

  // Operations management
  const {
    executeOperation,
    operationStatus,
    operationResult,
    operationError,
  } = useIntegrationOperations();

  // Track current operation
  const [currentOperation, setCurrentOperation] = useState<TIntegrationOperation | undefined>();
  const [operationDuration, setOperationDuration] = useState<number | undefined>();

  /**
   * Handle operation execution
   */
  const handleExecuteOperation = async (operation: TIntegrationOperation): Promise<void> => {
    const startTime = Date.now();
    setCurrentOperation(operation);

    await executeOperation(operation);

    const duration = Date.now() - startTime;
    setOperationDuration(duration);
  };

  /**
   * Handle manual refresh
   */
  const handleRefresh = async (): Promise<void> => {
    await refetch();
  };

  /**
   * Get last update time
   */
  const getLastUpdateTime = (): string => {
    if (!data) return 'Never';
    const now = new Date();
    return now.toLocaleTimeString();
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4 md:p-6 lg:p-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              Integration Console
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Monitor integration health and execute cross-component tests
            </p>
          </div>

          {/* Refresh Button */}
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>

        {/* Last Update */}
        <div className="text-sm text-gray-600 dark:text-gray-400">
          Last updated: {getLastUpdateTime()}
        </div>
      </div>

      {/* Loading State */}
      {loading && !data && (
        <div className="flex items-center justify-center py-20">
          <div className="text-center">
            <RefreshCw className="w-12 h-12 text-purple-600 animate-spin mx-auto mb-4" />
            <div className="text-gray-600 dark:text-gray-400">Loading integration data...</div>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && !data && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
          <div className="text-red-900 dark:text-red-100 font-semibold mb-2">
            Error Loading Integration Data
          </div>
          <div className="text-red-700 dark:text-red-300 text-sm">
            {error}
          </div>
        </div>
      )}

      {/* Main Content */}
      {data && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Overview Panel */}
            <IntegrationOverviewPanel data={data} />

            {/* Cross-Component Test Panel */}
            <CrossComponentTestPanel />

            {/* Dependency Graph */}
            <DependencyGraph components={data.components} />

            {/* Integration Status Grid */}
            <IntegrationStatusGrid components={data.components} />
          </div>

          {/* Right Column - Operations & Results */}
          <div className="space-y-6">
            {/* Test Execution Panel */}
            <TestExecutionPanel
              onExecute={handleExecuteOperation}
              operationStatus={operationStatus}
              currentOperation={currentOperation}
            />

            {/* Test Results Display */}
            {(operationResult || operationError) && currentOperation && (
              <TestResultsDisplay
                operation={currentOperation}
                result={operationResult}
                error={operationError}
                duration={operationDuration}
              />
            )}
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="mt-8 pt-8 border-t border-gray-200 dark:border-gray-700">
        <div className="text-center text-sm text-gray-600 dark:text-gray-400">
          <p>Integration Console - M0 Real Dashboard</p>
          <p className="mt-1">Auto-refresh enabled (30-second interval)</p>
        </div>
      </div>
    </div>
  );
}

