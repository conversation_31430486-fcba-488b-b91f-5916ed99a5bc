'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { ComponentPlayground } from '../../components/m01/ComponentPlayground';

interface ComponentDemo {
  id: string;
  name: string;
  icon: string;
  category: string;
  description: string;
  features: string[];
  metrics: {
    performance: string;
    coverage: string;
    reliability: string;
  };
}

const componentDemos: ComponentDemo[] = [
  {
    id: 'memory-safe-resource-manager',
    name: 'MemorySafeResourceManager',
    icon: '🛡️',
    category: 'Memory Safety',
    description: 'Enterprise-grade memory management with automatic leak detection and resource pooling',
    features: ['Automatic Leak Detection', 'Resource Pooling', 'Memory Limits', 'Cleanup Automation'],
    metrics: { performance: '99.9%', coverage: '100%', reliability: '99.99%' }
  },
  {
    id: 'event-handler-registry',
    name: 'EventHandlerRegistry',
    icon: '📡',
    category: 'Event Management',
    description: 'Type-safe event handling with automatic cleanup and memory leak prevention',
    features: ['Type Safety', 'Auto Cleanup', 'Event Filtering', 'Priority Handling'],
    metrics: { performance: '99.8%', coverage: '100%', reliability: '99.95%' }
  },
  {
    id: 'timer-coordination',
    name: 'TimerCoordinationService',
    icon: '⏱️',
    category: 'Timing & Coordination',
    description: 'Resilient timer management with automatic recovery and coordination',
    features: ['Auto Recovery', 'Timer Pooling', 'Drift Correction', 'Resource Limits'],
    metrics: { performance: '99.7%', coverage: '100%', reliability: '99.98%' }
  },
  {
    id: 'atomic-circular-buffer',
    name: 'AtomicCircularBuffer',
    icon: '🔄',
    category: 'Data Structures',
    description: 'Lock-free circular buffer with atomic operations and overflow protection',
    features: ['Lock-Free', 'Atomic Ops', 'Overflow Protection', 'High Throughput'],
    metrics: { performance: '99.9%', coverage: '100%', reliability: '99.99%' }
  },
  {
    id: 'memory-pool-manager',
    name: 'MemoryPoolManager',
    icon: '💾',
    category: 'Memory Management',
    description: 'Efficient memory pooling with automatic allocation and deallocation strategies',
    features: ['Pool Strategies', 'Auto Allocation', 'Fragmentation Prevention', 'Usage Analytics'],
    metrics: { performance: '99.8%', coverage: '100%', reliability: '99.97%' }
  },
  {
    id: 'resource-coordinator',
    name: 'ResourceCoordinator',
    icon: '🎯',
    category: 'Resource Management',
    description: 'Centralized resource coordination with priority management and conflict resolution',
    features: ['Priority Management', 'Conflict Resolution', 'Resource Tracking', 'Load Balancing'],
    metrics: { performance: '99.6%', coverage: '100%', reliability: '99.96%' }
  }
];

export default function M01ComponentsPage() {
  const [selectedComponent, setSelectedComponent] = useState<ComponentDemo | null>(null);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-950 via-teal-950 to-emerald-950">
      {/* Animated Background */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-emerald-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-teal-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <div className="relative z-10 container mx-auto px-6 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link href="/" className="inline-flex items-center text-emerald-400 hover:text-emerald-300 mb-4 transition-colors">
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Dashboard
          </Link>
          
          <div className="bg-gradient-to-br from-teal-900/40 to-emerald-900/40 backdrop-blur-xl border border-emerald-500/30 rounded-2xl p-8 shadow-2xl">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-5xl font-bold bg-gradient-to-r from-emerald-400 to-teal-300 bg-clip-text text-transparent mb-3">
                  🎨 Components Gallery
                </h1>
                <p className="text-emerald-300 text-xl">
                  Interactive demonstrations of 6 enhanced M0.1 components
                </p>
                <p className="text-teal-400 mt-2">
                  Explore enterprise-grade features with live simulations and real-time metrics
                </p>
              </div>
              <div className="text-right">
                <div className="text-4xl font-bold text-emerald-400">6</div>
                <div className="text-sm text-teal-300">Components</div>
                <div className="text-xs text-emerald-500 mt-1">100% Coverage</div>
              </div>
            </div>
          </div>
        </div>

        {/* Component Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {componentDemos.map((component) => (
            <div
              key={component.id}
              onClick={() => setSelectedComponent(component)}
              className="group cursor-pointer"
            >
              <div className="bg-gradient-to-br from-teal-900/30 to-emerald-900/30 backdrop-blur-lg border border-emerald-500/20 rounded-xl p-6 hover:border-emerald-400/50 hover:shadow-2xl hover:shadow-emerald-500/20 transition-all duration-300 h-full">
                {/* Icon and Category */}
                <div className="flex items-center justify-between mb-4">
                  <span className="text-5xl group-hover:scale-110 transition-transform">{component.icon}</span>
                  <span className="text-xs px-3 py-1 bg-emerald-500/20 text-emerald-300 rounded-full border border-emerald-500/30">
                    {component.category}
                  </span>
                </div>

                {/* Component Name */}
                <h3 className="text-xl font-bold text-white mb-2 group-hover:text-emerald-300 transition-colors">
                  {component.name}
                </h3>

                {/* Description */}
                <p className="text-sm text-teal-300 mb-4 line-clamp-2">
                  {component.description}
                </p>

                {/* Metrics */}
                <div className="grid grid-cols-3 gap-2 mb-4">
                  <div className="bg-emerald-500/10 rounded-lg p-2 text-center border border-emerald-500/20">
                    <div className="text-xs text-emerald-400 font-semibold">{component.metrics.performance}</div>
                    <div className="text-xs text-teal-400">Performance</div>
                  </div>
                  <div className="bg-teal-500/10 rounded-lg p-2 text-center border border-teal-500/20">
                    <div className="text-xs text-teal-300 font-semibold">{component.metrics.coverage}</div>
                    <div className="text-xs text-teal-400">Coverage</div>
                  </div>
                  <div className="bg-emerald-500/10 rounded-lg p-2 text-center border border-emerald-500/20">
                    <div className="text-xs text-emerald-400 font-semibold">{component.metrics.reliability}</div>
                    <div className="text-xs text-teal-400">Uptime</div>
                  </div>
                </div>

                {/* Features */}
                <div className="space-y-1 mb-4">
                  {component.features.slice(0, 3).map((feature, idx) => (
                    <div key={idx} className="flex items-center text-xs text-emerald-300">
                      <svg className="w-3 h-3 mr-2 text-emerald-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      {feature}
                    </div>
                  ))}
                </div>

                {/* View Demo Button */}
                <div className="flex items-center justify-between text-xs">
                  <span className="text-emerald-400 font-semibold group-hover:text-emerald-300">
                    Click to explore
                  </span>
                  <svg className="w-4 h-4 text-emerald-500 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Selected Component Detail Modal */}
        {selectedComponent && (
          <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4 overflow-y-auto" onClick={() => setSelectedComponent(null)}>
            <div className="bg-gradient-to-br from-teal-900/90 to-emerald-900/90 backdrop-blur-xl border border-emerald-500/30 rounded-2xl p-6 max-w-6xl w-full shadow-2xl my-8 max-h-[90vh] overflow-y-auto" onClick={(e) => e.stopPropagation()}>
              {/* Modal Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <span className="text-4xl">{selectedComponent.icon}</span>
                  <div>
                    <h2 className="text-2xl font-bold text-white mb-1">{selectedComponent.name}</h2>
                    <span className="text-xs px-2 py-1 bg-emerald-500/20 text-emerald-300 rounded-full border border-emerald-500/30">
                      {selectedComponent.category}
                    </span>
                  </div>
                </div>
                <button
                  onClick={() => setSelectedComponent(null)}
                  className="text-emerald-400 hover:text-emerald-300 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Description */}
              <p className="text-teal-300 text-base mb-4">{selectedComponent.description}</p>

              {/* Metrics Grid */}
              <div className="grid grid-cols-3 gap-3 mb-4">
                <div className="bg-emerald-500/10 rounded-lg p-3 text-center border border-emerald-500/30">
                  <div className="text-2xl font-bold text-emerald-400 mb-1">{selectedComponent.metrics.performance}</div>
                  <div className="text-xs text-teal-300">Performance Score</div>
                </div>
                <div className="bg-teal-500/10 rounded-lg p-3 text-center border border-teal-500/30">
                  <div className="text-2xl font-bold text-teal-300 mb-1">{selectedComponent.metrics.coverage}</div>
                  <div className="text-xs text-teal-300">Test Coverage</div>
                </div>
                <div className="bg-emerald-500/10 rounded-lg p-3 text-center border border-emerald-500/30">
                  <div className="text-2xl font-bold text-emerald-400 mb-1">{selectedComponent.metrics.reliability}</div>
                  <div className="text-xs text-teal-300">Reliability</div>
                </div>
              </div>

              {/* Features List */}
              <div className="mb-4">
                <h3 className="text-lg font-bold text-white mb-2">Key Features</h3>
                <div className="grid grid-cols-2 gap-2">
                  {selectedComponent.features.map((feature, idx) => (
                    <div key={idx} className="flex items-center bg-emerald-500/5 rounded-lg p-2 border border-emerald-500/20">
                      <svg className="w-4 h-4 mr-2 text-emerald-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span className="text-sm text-emerald-300">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Live Interactive Playground */}
              <div className="bg-gradient-to-br from-slate-900/50 to-teal-900/50 rounded-xl p-4 border border-emerald-500/20">
                <ComponentPlayground componentId={selectedComponent.id} />
              </div>
            </div>
          </div>
        )}

        {/* Footer Stats */}
        <div className="bg-gradient-to-br from-teal-900/40 to-emerald-900/40 backdrop-blur-xl border border-emerald-500/30 rounded-2xl p-6 shadow-2xl">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
            <div>
              <div className="text-3xl font-bold text-emerald-400 mb-1">6</div>
              <div className="text-sm text-teal-300">Components</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-emerald-400 mb-1">100%</div>
              <div className="text-sm text-teal-300">Test Coverage</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-emerald-400 mb-1">99.8%</div>
              <div className="text-sm text-teal-300">Avg Performance</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-emerald-400 mb-1">24</div>
              <div className="text-sm text-teal-300">Enterprise Features</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

