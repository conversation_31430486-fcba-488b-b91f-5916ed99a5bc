'use client';

/**
 * M0 Real Dashboard - Main Page
 *
 * Enterprise-grade dashboard displaying real-time status of 136 M0 components
 * with 100% authentic integration (zero simulation/mocking)
 *
 * Authority: President & CEO, E<PERSON><PERSON>. Consultancy
 * Purpose: Demonstrate operational M0 system capabilities
 * Status: Phase 4 - Dashboard UI Development (Complete)
 *
 * Component Integration Status:
 * - Governance: 69/73 (94.5%) - 4 deferred (security infrastructure)
 * - Tracking: 33/25 (132%) - All components integrated
 * - Memory Safety: 19/19 (100%) - All components integrated
 * - Integration: 15/8 (188%) - All components integrated
 * - TOTAL: 136 components with 100% health score
 */

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import {
  m0ApiService,
  IM0DashboardData,
  IM0ComponentStatus,
  getStatusIcon,
  getStatusColor,
  getHealthScoreColor
} from '../lib/M0ApiService';
import { useM0Stream, IStreamEventData } from '../hooks/useM0Stream';
import { ToastContainer } from '../components/ToastNotification';

export default function M0RealDashboard() {
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState<IM0DashboardData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [liveUpdateCount, setLiveUpdateCount] = useState(0);

  // ============================================================================
  // SSE EVENT HANDLERS
  // ============================================================================

  const handleComponentStatusChange = useCallback((data: IStreamEventData) => {
    console.log('🔄 Live component status change:', data);
    setLiveUpdateCount(prev => prev + 1);
    // Refresh dashboard data to reflect the change
    refreshDashboardData();
  }, []);

  const handleHealthScoreChange = useCallback((data: IStreamEventData) => {
    console.log('💚 Live health score change:', data);
    setLiveUpdateCount(prev => prev + 1);
    // Refresh dashboard data to reflect the change
    refreshDashboardData();
  }, []);

  const handleErrorDetected = useCallback((data: IStreamEventData) => {
    console.error('❌ Live error detected:', data);
    setLiveUpdateCount(prev => prev + 1);
    // Refresh dashboard data to reflect the error
    refreshDashboardData();
  }, []);

  const handleWarningDetected = useCallback((data: IStreamEventData) => {
    console.warn('⚠️ Live warning detected:', data);
    setLiveUpdateCount(prev => prev + 1);
    // Refresh dashboard data to reflect the warning
    refreshDashboardData();
  }, []);

  const handleSystemMetricUpdate = useCallback((data: IStreamEventData) => {
    console.log('📈 Live system metrics update:', data);
    // Update system metrics in real-time if available
    if (data.systemMetrics && dashboardData) {
      setDashboardData(prev => prev ? {
        ...prev,
        overallHealthScore: data.systemMetrics!.overallHealthScore,
        totalComponents: data.systemMetrics!.totalComponents,
        healthyComponents: data.systemMetrics!.healthyComponents,
        errorComponents: data.systemMetrics!.errorComponents,
        systemMetrics: {
          ...prev.systemMetrics,
          averageResponseTime: data.systemMetrics!.averageResponseTime
        }
      } : null);
      setLastUpdate(new Date());
    }
  }, [dashboardData]);

  // ============================================================================
  // SSE INTEGRATION
  // ============================================================================

  const {
    isConnected: sseConnected,
    eventCounts,
    systemMetrics: sseSystemMetrics,
    lastHeartbeat,
    notifications,
    removeNotification
  } = useM0Stream({
    autoConnect: true,
    onComponentStatusChange: handleComponentStatusChange,
    onHealthScoreChange: handleHealthScoreChange,
    onErrorDetected: handleErrorDetected,
    onWarningDetected: handleWarningDetected,
    onSystemMetricUpdate: handleSystemMetricUpdate,
    enableNotifications: true,
    notificationDuration: 5000
  });

  // ============================================================================
  // INITIALIZATION & DATA FETCHING
  // ============================================================================

  useEffect(() => {
    initializeM0Dashboard();

    // Set up periodic refresh (less frequent since SSE provides real-time updates)
    const updateInterval = setInterval(() => {
      refreshDashboardData();
    }, 30000); // Update every 30 seconds (reduced from 5 seconds)

    return () => {
      clearInterval(updateInterval);
    };
  }, []);

  const initializeM0Dashboard = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🚀 Initializing M0 Real Dashboard with server-side API integration...');

      // Fetch initial dashboard data from API
      const initialData = await m0ApiService.getDashboardData(false); // Don't use cache on init
      setDashboardData(initialData);
      setLastUpdate(new Date());

      setLoading(false);
      console.log('✅ M0 Real Dashboard initialized successfully with expanded components');

    } catch (err) {
      console.error('❌ Failed to initialize M0 Real Dashboard:', err);
      setError(err instanceof Error ? err.message : 'Unknown initialization error');
      setLoading(false);
    }
  };

  const refreshDashboardData = async () => {
    try {
      const updatedData = await m0ApiService.getDashboardData(true); // Use cache for regular updates
      setDashboardData(updatedData);
      setLastUpdate(new Date());
    } catch (err) {
      console.warn('Failed to refresh dashboard data:', err);
      // Don't show error for background updates, just log it
    }
  };

  const handleManualRefresh = async () => {
    try {
      setIsRefreshing(true);
      const refreshedData = await m0ApiService.refreshComponents();
      setDashboardData(refreshedData);
      setLastUpdate(new Date());
    } catch (err) {
      console.error('Failed to manually refresh components:', err);
      setError(err instanceof Error ? err.message : 'Refresh failed');
    } finally {
      setIsRefreshing(false);
    }
  };

  // Utility functions are now imported from M0ApiService

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-cyan-400 mx-auto mb-4"></div>
          <h2 className="text-2xl font-bold text-white mb-2">
            🏗️ Open Architecture Framework
          </h2>
          <p className="text-xl text-blue-300 mb-3">
            Initializing OA Framework Dashboard...
          </p>
          <p className="text-cyan-400 mt-2">
            Loading enterprise component system
          </p>
          <p className="text-sm text-slate-400 mt-1">
            Discovering governance, tracking, memory safety, and integration components
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-red-900 to-slate-900 flex items-center justify-center">
        <div className="bg-red-900/30 border border-red-500 rounded-lg p-8 max-w-md">
          <h2 className="text-red-400 text-2xl font-bold mb-4">❌ OA Framework Dashboard Initialization Failed</h2>
          <p className="text-white mb-4">{error}</p>
          <button
            onClick={initializeM0Dashboard}
            className="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-6 rounded-lg transition-colors"
          >
            Retry Initialization
          </button>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-yellow-400 text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold text-white">
            No Dashboard Data Available
          </h2>
          <p className="text-blue-300 mt-2">
            OA Framework initialized but no data received
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      {/* Toast Notifications */}
      <ToastContainer notifications={notifications} onDismiss={removeNotification} />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-2">
                Open Architecture Framework
              </h1>
              <p className="text-blue-300 text-xl font-semibold mb-3">
                OA Framework Dashboard
              </p>
              <p className="text-cyan-400 text-lg mb-2">
                Total Components Created: <span className="font-bold text-white">{dashboardData.totalComponents}</span> (M0 - M0.2 Complete)
              </p>
              <p className="text-sm text-slate-400">
                Last updated: {lastUpdate.toLocaleTimeString()} • Real-Time Monitoring Active
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={handleManualRefresh}
                disabled={isRefreshing}
                className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2 shadow-lg"
              >
                <span className={isRefreshing ? 'animate-spin' : ''}>
                  {isRefreshing ? '🔄' : '🔄'}
                </span>
                <span>{isRefreshing ? 'Refreshing...' : 'Refresh'}</span>
              </button>
            </div>
          </div>
          <div className="mt-3 flex items-center space-x-4 flex-wrap gap-2">
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${sseConnected ? 'bg-green-400 animate-pulse' : 'bg-slate-600'}`}></div>
              <span className="text-sm text-slate-300">
                {sseConnected ? 'SSE Connected' : 'SSE Disconnected'}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
              <span className="text-sm text-slate-300">Real M0 Integration</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-purple-400 rounded-full"></div>
              <span className="text-sm text-slate-300">Server-Side API</span>
            </div>
            {liveUpdateCount > 0 && (
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-orange-400 rounded-full animate-pulse"></div>
                <span className="text-sm text-slate-300">
                  {liveUpdateCount} live update{liveUpdateCount !== 1 ? 's' : ''}
                </span>
              </div>
            )}
            {lastHeartbeat && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-slate-400">
                  💓 Last heartbeat: {lastHeartbeat.toLocaleTimeString()}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Overall Health Score - Featured Card */}
        <div className="bg-gradient-to-r from-green-900/40 to-emerald-900/40 border border-green-500/50 rounded-xl p-6 md:p-8 mb-8 shadow-2xl">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="mb-4 md:mb-0">
              <h2 className="text-2xl md:text-3xl font-bold text-white mb-2">
                Overall Health Score
              </h2>
              <p className="text-green-300 text-lg">
                {dashboardData.healthyComponents} / {dashboardData.totalComponents} components healthy
              </p>
            </div>
            <div className="relative">
              <div className="text-6xl md:text-8xl font-bold text-green-400">
                {dashboardData.overallHealthScore}%
              </div>
              {dashboardData.overallHealthScore === 100 && (
                <div className="absolute -top-2 -right-2 text-4xl animate-bounce">✅</div>
              )}
            </div>
          </div>

          {/* Health Score Progress Bar */}
          <div className="mt-6 bg-slate-800/50 rounded-full h-4 overflow-hidden">
            <div
              className="bg-gradient-to-r from-green-500 to-emerald-400 h-full transition-all duration-500 ease-out"
              style={{ width: `${dashboardData.overallHealthScore}%` }}
            ></div>
          </div>
        </div>

        {/* Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <div className="bg-gradient-to-br from-green-900/40 to-emerald-900/40 border border-green-500/50 rounded-xl p-6 shadow-xl hover:shadow-2xl transition-shadow">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-green-500/20 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-3xl font-bold text-green-400">{dashboardData.healthyComponents}</p>
                <p className="text-green-300">Healthy Components</p>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-yellow-900/40 to-orange-900/40 border border-yellow-500/50 rounded-xl p-6 shadow-xl hover:shadow-2xl transition-shadow">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-yellow-500/20 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-3xl font-bold text-yellow-400">{dashboardData.warningComponents}</p>
                <p className="text-yellow-300">Warning Components</p>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-red-900/40 to-pink-900/40 border border-red-500/50 rounded-xl p-6 shadow-xl hover:shadow-2xl transition-shadow">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-red-500/20 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-3xl font-bold text-red-400">{dashboardData.errorComponents}</p>
                <p className="text-red-300">Error Components</p>
              </div>
            </div>
          </div>
        </div>

        {/* M0.1 Enhanced Dashboards */}
        <div className="mb-8">
          <div className="mb-6">
            <h2 className="text-3xl font-bold text-white mb-2">🚀 M0.1 Enhanced Dashboards</h2>
            <p className="text-blue-300 text-lg">
              Explore M0.1 enhancements with enterprise features, performance analytics, and component showcases
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* M0.1 Overview Dashboard Link */}
            <Link href="/m01-overview" className="group">
              <div className="bg-gradient-to-br from-emerald-900/40 to-teal-900/40 border border-emerald-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">📊</span>
                  <div className="text-emerald-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">M0.1 Overview Dashboard</h3>
                <p className="text-sm text-emerald-300 mb-3">
                  Comprehensive overview of M0.1 milestone with statistics, tasks, and component gallery
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-emerald-400 font-semibold">
                    50 tasks • 6 components
                  </span>
                  <span className="text-emerald-300">View Overview →</span>
                </div>
              </div>
            </Link>

            {/* M0.1 Enterprise Features Link */}
            <Link href="/m01-features" className="group">
              <div className="bg-gradient-to-br from-purple-900/40 to-pink-900/40 border border-purple-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">🚀</span>
                  <div className="text-purple-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Enterprise Features</h3>
                <p className="text-sm text-purple-300 mb-3">
                  Security, scalability, integrations, audit trails, RBAC, and data governance
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-purple-400 font-semibold">
                    6 feature showcases
                  </span>
                  <span className="text-purple-300">View Features →</span>
                </div>
              </div>
            </Link>

            {/* M0.1 Performance Analytics Link */}
            <Link href="/m01-performance" className="group">
              <div className="bg-gradient-to-br from-blue-900/40 to-indigo-900/40 border border-blue-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">📈</span>
                  <div className="text-blue-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Performance Analytics</h3>
                <p className="text-sm text-blue-300 mb-3">
                  Real-time metrics, resource utilization, and performance optimization insights
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-blue-400 font-semibold">
                    136 components monitored
                  </span>
                  <span className="text-blue-300">View Analytics →</span>
                </div>
              </div>
            </Link>

            {/* M0.1 Components Gallery Link */}
            <Link href="/m01-components" className="group">
              <div className="bg-gradient-to-br from-purple-900/40 to-pink-900/40 border border-purple-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">🎨</span>
                  <div className="text-purple-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Components Gallery</h3>
                <p className="text-sm text-purple-300 mb-3">
                  Interactive demos for 6 enhanced M0.1 components with live simulations
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-purple-400 font-semibold">
                    6 interactive demos
                  </span>
                  <span className="text-purple-300">Explore Components →</span>
                </div>
              </div>
            </Link>

            {/* M0 vs M0.1 Comparison Link */}
            <Link href="/m01-comparison" className="group">
              <div className="bg-gradient-to-br from-orange-900/40 to-amber-900/40 border border-orange-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">⚖️</span>
                  <div className="text-orange-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">M0 vs M0.1 Comparison</h3>
                <p className="text-sm text-orange-300 mb-3">
                  Side-by-side analysis of capabilities and performance improvements
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-orange-400 font-semibold">
                    +67% enhancement
                  </span>
                  <span className="text-orange-300">View Comparison →</span>
                </div>
              </div>
            </Link>

            {/* Animation & Transitions Link */}
            <Link href="/m01-animations" className="group">
              <div className="bg-gradient-to-br from-pink-900/40 to-rose-900/40 border border-pink-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">✨</span>
                  <div className="text-pink-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Animation & Transitions</h3>
                <p className="text-sm text-pink-300 mb-3">
                  Smooth page transitions, component animations, and micro-interactions
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-pink-400 font-semibold">
                    9 animation components
                  </span>
                  <span className="text-pink-300">View Animations →</span>
                </div>
              </div>
            </Link>

            {/* User Customization Link */}
            <Link href="/m01-customization" className="group">
              <div className="bg-gradient-to-br from-cyan-900/40 to-teal-900/40 border border-cyan-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">⚙️</span>
                  <div className="text-cyan-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">User Customization</h3>
                <p className="text-sm text-cyan-300 mb-3">
                  Personalize theme, layout, and widget preferences with export/import
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-cyan-400 font-semibold">
                    Theme • Layout • Widgets
                  </span>
                  <span className="text-cyan-300">Customize →</span>
                </div>
              </div>
            </Link>

            {/* Advanced Analytics Link */}
            <Link href="/m01-analytics" className="group">
              <div className="bg-gradient-to-br from-violet-900/40 to-purple-900/40 border border-violet-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">📊</span>
                  <div className="text-violet-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Advanced Analytics</h3>
                <p className="text-sm text-violet-300 mb-3">
                  Statistical analysis, trend prediction, anomaly detection, and correlation
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-violet-400 font-semibold">
                    Stats • Trends • Anomalies
                  </span>
                  <span className="text-violet-300">View Analytics →</span>
                </div>
              </div>
            </Link>

            {/* Documentation & Help Link */}
            <Link href="/m01-help" className="group">
              <div className="bg-gradient-to-br from-amber-900/40 to-yellow-900/40 border border-amber-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">📚</span>
                  <div className="text-amber-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Documentation & Help</h3>
                <p className="text-sm text-amber-300 mb-3">
                  Interactive tutorials, feature docs, and FAQ for comprehensive support
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-amber-400 font-semibold">
                    Docs • Tutorials • FAQ
                  </span>
                  <span className="text-amber-300">Get Help →</span>
                </div>
              </div>
            </Link>
          </div>
        </div>

        {/* M0.2 Database Performance & Services */}
        <div className="mb-8">
          <div className="mb-6">
            <h2 className="text-3xl font-bold text-white mb-2">🗄️ M0.2 Database Performance & Services</h2>
            <p className="text-blue-300 text-lg">
              Real PostgreSQL integration with query optimization, connection pooling, and enterprise services
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Query Optimization Dashboard Link */}
            <Link href="/m02-query-optimization" className="group" data-tour="query-optimization">
              <div className="bg-gradient-to-br from-blue-900/40 to-cyan-900/40 border border-blue-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">🔍</span>
                  <div className="text-blue-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Query Optimization</h3>
                <p className="text-sm text-blue-300 mb-3">
                  Real PostgreSQL 17.5 query analysis with execution plans, performance metrics, and optimization recommendations
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-blue-400 font-semibold">
                    PostgreSQL 17.5 • Real DB
                  </span>
                  <span className="text-blue-300">Analyze Queries →</span>
                </div>
              </div>
            </Link>

            {/* Connection Pool Monitor Link */}
            <Link href="/m02-connection-pool" className="group" data-tour="connection-pool">
              <div className="bg-gradient-to-br from-purple-900/40 to-indigo-900/40 border border-purple-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">🔗</span>
                  <div className="text-purple-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Connection Pool Monitor</h3>
                <p className="text-sm text-purple-300 mb-3">
                  Real-time connection pool monitoring with health metrics, lifecycle tracking, and performance analytics
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-purple-400 font-semibold">
                    3 Pools • Real-time
                  </span>
                  <span className="text-purple-300">Monitor Pools →</span>
                </div>
              </div>
            </Link>

            {/* Notification Services Link */}
            <Link href="/m02-notifications" className="group" data-tour="notification-control">
              <div className="bg-gradient-to-br from-amber-900/40 to-yellow-900/40 border border-amber-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">🔔</span>
                  <div className="text-amber-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Notification Services</h3>
                <p className="text-sm text-amber-300 mb-3">
                  Multi-channel notifications with email, SMS, push, and webhook integrations
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-amber-400 font-semibold">
                    4 Channels • Rich Editor
                  </span>
                  <span className="text-amber-300">Manage Notifications →</span>
                </div>
              </div>
            </Link>

            {/* Alert Management Link */}
            <Link href="/m02-alerts" className="group" data-tour="alert-management">
              <div className="bg-gradient-to-br from-red-900/40 to-orange-900/40 border border-red-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">🚨</span>
                  <div className="text-red-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Alert Management</h3>
                <p className="text-sm text-red-300 mb-3">
                  Enterprise alert management with rule builder, escalation workflows, and real-time monitoring
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-red-400 font-semibold">
                    Rules • Workflows • Analytics
                  </span>
                  <span className="text-red-300">Manage Alerts →</span>
                </div>
              </div>
            </Link>

            {/* Performance Analytics Suite Link */}
            <Link href="/m02-performance-analytics" className="group" data-tour="performance-analytics">
              <div className="bg-gradient-to-br from-purple-900/40 to-indigo-900/40 border border-purple-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">📊</span>
                  <div className="text-purple-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Performance Analytics Suite</h3>
                <p className="text-sm text-purple-300 mb-3">
                  Advanced performance analytics with trend forecasting, bottleneck detection, and optimization recommendations
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-purple-400 font-semibold">
                    Trends • Bottlenecks • Insights
                  </span>
                  <span className="text-purple-300">View Analytics →</span>
                </div>
              </div>
            </Link>

            {/* Notification Analytics Link */}
            <Link href="/m02-notification-analytics" className="group" data-tour="notification-analytics">
              <div className="bg-gradient-to-br from-green-900/40 to-emerald-900/40 border border-green-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">📈</span>
                  <div className="text-green-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Notification Analytics</h3>
                <p className="text-sm text-green-300 mb-3">
                  Comprehensive notification analytics with delivery metrics, engagement tracking, and failure analysis
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-green-400 font-semibold">
                    Delivery • Engagement • Failures
                  </span>
                  <span className="text-green-300">View Analytics →</span>
                </div>
              </div>
            </Link>

            {/* Notification Scenario Simulator Link */}
            <Link href="/m02-notification-simulator" className="group" data-tour="notification-simulator">
              <div className="bg-gradient-to-br from-indigo-900/40 to-purple-900/40 border border-indigo-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">🎬</span>
                  <div className="text-indigo-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Notification Simulator</h3>
                <p className="text-sm text-indigo-300 mb-3">
                  Interactive notification scenario simulator with escalation workflows and multi-channel delivery
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-indigo-400 font-semibold">
                    4 Scenarios • Escalation • Failover
                  </span>
                  <span className="text-indigo-300">Run Simulation →</span>
                </div>
              </div>
            </Link>

            {/* Database Performance Simulator Link */}
            <Link href="/m02-db-performance-simulator" className="group" data-tour="db-simulator">
              <div className="bg-gradient-to-br from-blue-900/40 to-cyan-900/40 border border-blue-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">⚡</span>
                  <div className="text-blue-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">DB Performance Simulator</h3>
                <p className="text-sm text-blue-300 mb-3">
                  Test database optimization strategies with PostgreSQL and Redis performance scenarios
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-blue-400 font-semibold">
                    5 Scenarios • PostgreSQL • Redis
                  </span>
                  <span className="text-blue-300">Run Tests →</span>
                </div>
              </div>
            </Link>

            {/* M0.1 Integration Dashboard Link */}
            <Link href="/m02-integration" className="group" data-tour="integration-dashboard">
              <div className="bg-gradient-to-br from-purple-900/40 to-pink-900/40 border border-purple-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">🔗</span>
                  <div className="text-purple-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">M0.1 + M0.2 Integration</h3>
                <p className="text-sm text-purple-300 mb-3">
                  Unified dashboard demonstrating cross-system integration with correlation analysis and architecture visualization
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-purple-400 font-semibold">
                    5 Systems • Correlations • Architecture Map
                  </span>
                  <span className="text-purple-300">View Integration →</span>
                </div>
              </div>
            </Link>

            {/* Demo Tour Link */}
            <Link href="/m02-tour" className="group">
              <div className="bg-gradient-to-br from-amber-900/40 to-orange-900/40 border border-amber-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">🎯</span>
                  <div className="text-amber-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Comprehensive Demo Tour</h3>
                <p className="text-sm text-amber-300 mb-3">
                  Interactive guided tour showcasing all 12 M0.2 features with step-by-step demonstrations
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-amber-400 font-semibold">
                    12 Features • 15 Minutes • Interactive
                  </span>
                  <span className="text-amber-300">Start Tour →</span>
                </div>
              </div>
            </Link>

            {/* Channel Manager Link */}
            <Link href="/m02-channels" className="group" data-tour="channel-manager">
              <div className="bg-gradient-to-br from-cyan-900/40 to-teal-900/40 border border-cyan-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">📡</span>
                  <div className="text-cyan-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Channel Manager</h3>
                <p className="text-sm text-cyan-300 mb-3">
                  Multi-provider communication channel management with health monitoring, failover, and rate limiting
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-cyan-400 font-semibold">
                    10+ Providers • Failover • Analytics
                  </span>
                  <span className="text-cyan-300">Manage Channels →</span>
                </div>
              </div>
            </Link>

            {/* Template Editor Link */}
            <Link href="/m02-templates" className="group" data-tour="template-editor">
              <div className="bg-gradient-to-br from-emerald-900/40 to-green-900/40 border border-emerald-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">📝</span>
                  <div className="text-emerald-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Template Editor</h3>
                <p className="text-sm text-emerald-300 mb-3">
                  Create and edit templates with live preview, variable substitution, and version control
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-emerald-400 font-semibold">
                    4 Engines • Live Preview • Versioning
                  </span>
                  <span className="text-emerald-300">Edit Templates →</span>
                </div>
              </div>
            </Link>
          </div>
        </div>

        {/* M0.3 Configuration & Compliance */}
        <div className="mb-8">
          <div className="mb-6">
            <h2 className="text-3xl font-bold text-white mb-2">🔧 M0.3 Configuration & Compliance</h2>
            <p className="text-blue-300 text-lg">
              Manage audit logging configuration, compliance profiles, and real-time monitoring
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Configuration Management Dashboard */}
            <Link href="/m03-configuration" className="group">
              <div className="bg-gradient-to-br from-indigo-900/40 to-blue-900/40 border border-indigo-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">⚙️</span>
                  <div className="text-indigo-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Configuration Management</h3>
                <p className="text-sm text-indigo-300 mb-3">
                  Manage global, category, and component-level logging configurations with real-time updates
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-indigo-400 font-semibold">
                    3 Levels • Real-time • Validation
                  </span>
                  <span className="text-indigo-300">Configure →</span>
                </div>
              </div>
            </Link>

            {/* Real-Time Logging Control Panel */}
            <Link href="/m03-logging" className="group">
              <div className="bg-gradient-to-br from-cyan-900/40 to-teal-900/40 border border-cyan-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">🎛️</span>
                  <div className="text-cyan-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Logging Control Panel</h3>
                <p className="text-sm text-cyan-300 mb-3">
                  Real-time logging control with hot-reload visualization and live log streaming
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-cyan-400 font-semibold">
                    Live Logs • &lt;1s Reload • Traffic Gen
                  </span>
                  <span className="text-cyan-300">Control →</span>
                </div>
              </div>
            </Link>

            {/* Compliance Profile Manager */}
            <Link href="/m03-compliance" className="group">
              <div className="bg-gradient-to-br from-green-900/40 to-emerald-900/40 border border-green-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">🔐</span>
                  <div className="text-green-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Compliance Profiles</h3>
                <p className="text-sm text-green-300 mb-3">
                  Manage SOX, GDPR, HIPAA, and PCI-DSS compliance profiles with enforcement modes
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-green-400 font-semibold">
                    4 Profiles • Enforcement • Requirements
                  </span>
                  <span className="text-green-300">Manage →</span>
                </div>
              </div>
            </Link>

            {/* Audit Trail Viewer */}
            <Link href="/m03-audit" className="group">
              <div className="bg-gradient-to-br from-orange-900/40 to-red-900/40 border border-orange-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">📋</span>
                  <div className="text-orange-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Audit Trail Viewer</h3>
                <p className="text-sm text-orange-300 mb-3">
                  View configuration change history with diff viewer and export capabilities
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-orange-400 font-semibold">
                    History • Diff Viewer • Export
                  </span>
                  <span className="text-orange-300">View Audit →</span>
                </div>
              </div>
            </Link>

            {/* Configuration Hierarchy Explorer - Feature 2.1 */}
            <Link href="/m03-hierarchy" className="group">
              <div className="bg-gradient-to-br from-purple-900/40 to-violet-900/40 border border-purple-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">🌳</span>
                  <div className="text-purple-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Hierarchy Explorer</h3>
                <p className="text-sm text-purple-300 mb-3">
                  Visualize configuration hierarchy with effective value calculator and conflict analysis
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-purple-400 font-semibold">
                    Tree • Effective Config • Conflicts
                  </span>
                  <span className="text-purple-300">Explore →</span>
                </div>
              </div>
            </Link>

            {/* Log Volume & Performance Analytics - Feature 2.2 */}
            <Link href="/m03-analytics" className="group">
              <div className="bg-gradient-to-br from-blue-900/40 to-cyan-900/40 border border-blue-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">📊</span>
                  <div className="text-blue-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Analytics Dashboard</h3>
                <p className="text-sm text-blue-300 mb-3">
                  Log volume trends, performance metrics, and storage cost estimator
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-blue-400 font-semibold">
                    Trends • Metrics • Cost Analysis
                  </span>
                  <span className="text-blue-300">View Analytics →</span>
                </div>
              </div>
            </Link>

            {/* Multi-Environment Simulator - Feature 2.3 */}
            <Link href="/m03-environment" className="group">
              <div className="bg-gradient-to-br from-teal-900/40 to-cyan-900/40 border border-teal-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">🌍</span>
                  <div className="text-teal-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Environment Simulator</h3>
                <p className="text-sm text-teal-300 mb-3">
                  Multi-environment configuration simulator with file defaults, database overrides, and merged configs
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-teal-400 font-semibold">
                    Dev • Staging • Production
                  </span>
                  <span className="text-teal-300">Simulate →</span>
                </div>
              </div>
            </Link>

            {/* WebSocket Real-Time Monitor - Feature 2.4 */}
            <Link href="/m03-websocket" className="group">
              <div className="bg-gradient-to-br from-rose-900/40 to-pink-900/40 border border-rose-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">🔌</span>
                  <div className="text-rose-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">WebSocket Real-Time Monitor</h3>
                <p className="text-sm text-rose-300 mb-3">
                  Dedicated monitor for WebSocket infrastructure with connection status, event payloads, and latency metrics
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-rose-400 font-semibold">
                    Live Events • Latency • Clients
                  </span>
                  <span className="text-rose-300">Monitor →</span>
                </div>
              </div>
            </Link>

            {/* Custom Profile Builder - Feature 3.1 */}
            <Link href="/m03-custom-profile" className="group">
              <div className="bg-gradient-to-br from-amber-900/40 to-yellow-900/40 border border-amber-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">🔧</span>
                  <div className="text-amber-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Custom Profile Builder</h3>
                <p className="text-sm text-amber-300 mb-3">
                  Create and manage custom compliance profiles with requirements, JSON export/import, and validation
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-amber-400 font-semibold">
                    Profiles • Requirements • Export/Import
                  </span>
                  <span className="text-amber-300">Build Profiles →</span>
                </div>
              </div>
            </Link>

            {/* API Testing Interface - Feature 3.2 */}
            <Link href="/m03-api-testing" className="group">
              <div className="bg-gradient-to-br from-rose-900/40 to-pink-900/40 border border-rose-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">🧪</span>
                  <div className="text-rose-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">API Testing Interface</h3>
                <p className="text-sm text-rose-300 mb-3">
                  Swagger-like interface for testing M0.3 RESTful Configuration API endpoints with request/response viewer
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-rose-400 font-semibold">
                    8 Endpoints • JSON Editor • History
                  </span>
                  <span className="text-rose-300">Test APIs →</span>
                </div>
              </div>
            </Link>

            {/* Guided Tour - Feature 3.4 */}
            <Link href="/m03-tour" className="group">
              <div className="bg-gradient-to-br from-violet-900/40 to-purple-900/40 border border-violet-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">🎯</span>
                  <div className="text-violet-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Guided Tour</h3>
                <p className="text-sm text-violet-300 mb-3">
                  Interactive step-by-step tour showcasing all 12 M0.3 features with contextual explanations
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-violet-400 font-semibold">
                    12 Features • 18 Minutes • Interactive
                  </span>
                  <span className="text-violet-300">Start Tour →</span>
                </div>
              </div>
            </Link>
          </div>
        </div>



        {/* M0 Core Dashboards */}
        <div className="mb-8">
          <div className="mb-6">
            <h2 className="text-3xl font-bold text-white mb-2">📊 M0 Core Dashboards</h2>
            <p className="text-blue-300 text-lg">
              Access specialized monitoring dashboards for M0 core components
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Security Dashboard Link */}
            <Link href="/security-dashboard" className="group">
              <div className="bg-gradient-to-br from-red-900/40 to-orange-900/40 border border-red-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">🔒</span>
                  <div className="text-red-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Security Dashboard</h3>
                <p className="text-sm text-red-300 mb-3">
                  Threat detection, vulnerability scanning, security metrics, and real-time alerts
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-red-400 font-semibold">
                    {dashboardData.categories.memorySafety?.length || 0} components
                  </span>
                  <span className="text-red-300">View Dashboard →</span>
                </div>
              </div>
            </Link>

            {/* Governance Dashboard Link */}
            <Link href="/governance-dashboard" className="group">
              <div className="bg-gradient-to-br from-purple-900/40 to-indigo-900/40 border border-purple-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">⚖️</span>
                  <div className="text-purple-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Governance Dashboard</h3>
                <p className="text-sm text-purple-300 mb-3">
                  Policy enforcement, compliance monitoring, rule validation, and audit trails
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-purple-400 font-semibold">
                    {dashboardData.categories.governance?.length || 0} components
                  </span>
                  <span className="text-purple-300">View Dashboard →</span>
                </div>
              </div>
            </Link>

            {/* Tracking Dashboard Link */}
            <Link href="/tracking-dashboard" className="group">
              <div className="bg-gradient-to-br from-blue-900/40 to-cyan-900/40 border border-blue-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">📊</span>
                  <div className="text-blue-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Tracking Dashboard</h3>
                <p className="text-sm text-blue-300 mb-3">
                  Session analytics, event timelines, component health checks, and performance metrics
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-blue-400 font-semibold">
                    {dashboardData.categories.tracking?.length || 0} components
                  </span>
                  <span className="text-blue-300">View Dashboard →</span>
                </div>
              </div>
            </Link>

            {/* Integration Console Link */}
            <Link href="/integration-console" className="group">
              <div className="bg-gradient-to-br from-green-900/40 to-emerald-900/40 border border-green-500/50 rounded-xl shadow-xl p-6 hover:shadow-2xl transition-all duration-200 cursor-pointer">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-4xl">🔗</span>
                  <div className="text-green-400 group-hover:translate-x-1 transition-transform">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Integration Console</h3>
                <p className="text-sm text-green-300 mb-3">
                  Cross-component testing, integration monitoring, and performance analysis
                </p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-green-400 font-semibold">
                    {dashboardData.categories.integration?.length || 0} components
                  </span>
                  <span className="text-green-300">View Dashboard →</span>
                </div>
              </div>
            </Link>
          </div>
        </div>

        {/* Component Categories */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {Object.entries(dashboardData.categories).map(([categoryKey, components]) => {
            const categoryName = categoryKey === 'memorySafety' ? 'Memory Safety' :
                               categoryKey.charAt(0).toUpperCase() + categoryKey.slice(1);
            const categoryIcon = categoryKey === 'governance' ? '⚖️' :
                               categoryKey === 'tracking' ? '📊' :
                               categoryKey === 'memorySafety' ? '🛡️' : '🔗';

            const categoryColors = categoryKey === 'governance' ? 'from-purple-900/40 to-pink-900/40 border-purple-500/50' :
                                  categoryKey === 'tracking' ? 'from-blue-900/40 to-cyan-900/40 border-blue-500/50' :
                                  categoryKey === 'memorySafety' ? 'from-green-900/40 to-emerald-900/40 border-green-500/50' :
                                  'from-orange-900/40 to-red-900/40 border-orange-500/50';

            return (
              <div key={categoryKey} className={`bg-gradient-to-br ${categoryColors} border rounded-xl shadow-xl`}>
                <div className="px-6 py-4 border-b border-slate-700/50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-2xl">{categoryIcon}</span>
                      <h3 className="text-xl font-bold text-white">
                        {categoryName} Components ({components.length})
                      </h3>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="text-sm text-slate-300">
                        Health: {components.length > 0 ?
                          Math.round(components.reduce((sum, c) => sum + c.healthScore, 0) / components.length) : 0}%
                      </div>
                    </div>
                  </div>
                </div>
                <div className="px-6 py-4">
                  {components.length === 0 ? (
                    <p className="text-slate-400 text-center py-8">
                      No components in this category
                    </p>
                  ) : (
                    <div className="space-y-3">
                      {components.map((component) => (
                        <div
                          key={component.id}
                          className="flex items-center justify-between p-3 bg-slate-800/50 rounded-lg border border-slate-700/50 hover:bg-slate-800/70 transition-colors"
                        >
                          <div className="flex items-center space-x-3">
                            <span className="text-lg">{getStatusIcon(component.status)}</span>
                            <div>
                              <p className="font-medium text-white">{component.name}</p>
                              <p className="text-sm text-slate-400">
                                Last updated: {component.lastUpdate.toLocaleTimeString()}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-3">
                            <div className="text-right">
                              <p className={`text-sm font-medium ${getHealthScoreColor(component.healthScore)}`}>
                                {component.healthScore}%
                              </p>
                              <p className="text-xs text-slate-400">
                                {component.metrics.responseTime}ms
                              </p>
                            </div>
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(component.status)}`}>
                              {component.status.toUpperCase()}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* System Metrics */}
        <div className="mt-8 grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 bg-gradient-to-br from-indigo-900/40 to-purple-900/40 border border-indigo-500/50 rounded-xl shadow-xl">
            <div className="px-6 py-4 border-b border-slate-700/50">
              <h3 className="text-xl font-bold text-white">System Metrics</h3>
            </div>
            <div className="px-6 py-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="text-center">
                  <p className="text-2xl font-semibold text-indigo-400">
                    {Math.round(dashboardData.systemMetrics.averageResponseTime)}ms
                  </p>
                  <p className="text-sm text-slate-300">Average Response Time</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-semibold text-indigo-400">
                    {dashboardData.systemMetrics.totalOperations.toLocaleString()}
                  </p>
                  <p className="text-sm text-slate-300">Total Operations</p>
                </div>
                <div className="text-center">
                  <p className={`text-2xl font-semibold ${getHealthScoreColor(100 - dashboardData.systemMetrics.errorRate)}`}>
                    {dashboardData.systemMetrics.errorRate.toFixed(1)}%
                  </p>
                  <p className="text-sm text-slate-300">Error Rate</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-semibold text-indigo-400">
                    {Math.round(dashboardData.systemMetrics.totalMemoryUsage)}MB
                  </p>
                  <p className="text-sm text-slate-300">Memory Usage</p>
                </div>
              </div>
            </div>
          </div>

          {/* SSE Statistics */}
          <div className="bg-gradient-to-br from-cyan-900/40 to-blue-900/40 border border-cyan-500/50 rounded-xl shadow-xl">
            <div className="px-6 py-4 border-b border-slate-700/50">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-bold text-white">Real-Time SSE</h3>
                <div className={`w-2 h-2 rounded-full ${sseConnected ? 'bg-green-400 animate-pulse' : 'bg-red-400'}`}></div>
              </div>
            </div>
            <div className="px-6 py-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-300">Connection Status</span>
                  <span className={`text-sm font-medium ${sseConnected ? 'text-green-400' : 'text-red-400'}`}>
                    {sseConnected ? 'Connected' : 'Disconnected'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-300">Live Updates</span>
                  <span className="text-sm font-medium text-cyan-400">{liveUpdateCount}</span>
                </div>
                {Object.entries(eventCounts).length > 0 && (
                  <>
                    <div className="border-t border-slate-700/50 pt-3 mt-3">
                      <p className="text-xs text-slate-400 mb-2">Event Counts</p>
                      {Object.entries(eventCounts).map(([type, count]) => (
                        <div key={type} className="flex items-center justify-between mb-1">
                          <span className="text-xs text-slate-300">{type}</span>
                          <span className="text-xs font-medium text-cyan-400">{count}</span>
                        </div>
                      ))}
                    </div>
                  </>
                )}
                {lastHeartbeat && (
                  <div className="border-t border-slate-700/50 pt-3 mt-3">
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-slate-300">Last Heartbeat</span>
                      <span className="text-xs font-medium text-cyan-400">
                        {lastHeartbeat.toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <footer className="mt-12 text-center text-slate-400 text-sm">
          <p className="text-white font-semibold mb-2">
            🏗️ Open Architecture Framework (OA Framework)
          </p>
          <p className="text-blue-300 text-lg mb-2">
            Object Oriented Architecture - Enterprise-Grade Component System
          </p>
          <p className="text-cyan-400">
            {dashboardData.totalComponents} Components Created • M0 - M0.2 Complete
          </p>
          <p className="mt-2">
            ✅ Real-Time Integration • 🛡️ Memory-Safe Patterns • ⚡ SSE Updates • 🔔 Live Notifications
          </p>
          {sseConnected && Object.keys(eventCounts).length > 0 && (
            <p className="mt-2 text-xs text-slate-500">
              SSE Events: {Object.entries(eventCounts).map(([type, count]) => `${type}: ${count}`).join(' • ')}
            </p>
          )}
          <p className="mt-3 text-slate-500">Authority: President & CEO, E.Z. Consultancy</p>
        </footer>
      </div>
    </div>
  );
}
