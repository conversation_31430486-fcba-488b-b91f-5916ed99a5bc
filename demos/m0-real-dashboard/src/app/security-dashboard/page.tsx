'use client';

/**
 * ============================================================================
 * OA FRAMEWORK - M0 REAL DASHBOARD
 * Security Dashboard Page
 * ============================================================================
 * 
 * @fileoverview Main security dashboard page component
 * @module app/security-dashboard/page
 * @version 1.0.0
 * @since 2025-10-21
 * 
 * @description
 * Real-time security monitoring dashboard for memory safety, buffer
 * utilization, threat analysis, and security operations.
 * 
 * Features:
 * - Real-time security metrics monitoring
 * - Memory usage and buffer utilization visualization
 * - Threat level indicators and alerts
 * - Security component status grid
 * - Security operations (memory-scan, buffer-analysis, security-audit)
 * - Alert management system
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2025 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

import React, { useState } from 'react';
import { useSecurityData } from '@/hooks/useSecurityData';
import { useSecurityOperations } from '@/hooks/useSecurityOperations';
import { useSecurityAlerts } from '@/hooks/useSecurityAlerts';
import { DEFAULT_DASHBOARD_CONFIG } from '@/types/security-types';
import type { ISecurityDashboardConfig, ISecurityComponent } from '@/types/security-types';

// Import visualization components
import { SecurityOverviewPanel } from '@/components/security/SecurityOverviewPanel';
import { MemoryUsageMonitor } from '@/components/security/MemoryUsageMonitor';
import { BufferUtilizationChart } from '@/components/security/BufferUtilizationChart';
import { ThreatLevelIndicator } from '@/components/security/ThreatLevelIndicator';
import { ComponentStatusGrid } from '@/components/security/ComponentStatusGrid';

// Import operations and alert components
import { SecurityOperationsPanel } from '@/components/security/SecurityOperationsPanel';
import { OperationResultsDisplay } from '@/components/security/OperationResultsDisplay';
import { AlertNotification } from '@/components/security/AlertNotification';
import { AlertHistoryPanel } from '@/components/security/AlertHistoryPanel';

/**
 * Security Dashboard Page Component
 * 
 * @returns Security dashboard page
 */
export default function SecurityDashboardPage() {
  // Dashboard configuration state
  const [config, setConfig] = useState<ISecurityDashboardConfig>(DEFAULT_DASHBOARD_CONFIG);

  // Fetch security data with auto-refresh
  const {
    data: securityData,
    loading,
    error,
    lastUpdate,
    refresh,
    isRefreshing
  } = useSecurityData(config.refreshInterval, config.autoRefresh);

  // Security operations
  const {
    operationStatus,
    operationResult,
    operationError,
    operationHistory,
    runMemoryScan,
    runBufferAnalysis,
    runSecurityAudit,
    isOperationRunning
  } = useSecurityOperations();

  // Security alerts
  const {
    alerts,
    unacknowledgedCount,
    criticalCount,
    warningCount,
    acknowledgeAlert,
    acknowledgeAll,
    clearAlert,
    clearAll
  } = useSecurityAlerts(securityData);

  /**
   * Handle refresh interval change
   */
  const handleRefreshIntervalChange = (interval: number) => {
    setConfig(prev => ({ ...prev, refreshInterval: interval }));
  };

  /**
   * Handle auto-refresh toggle
   */
  const handleAutoRefreshToggle = () => {
    setConfig(prev => ({ ...prev, autoRefresh: !prev.autoRefresh }));
  };

  /**
   * Handle manual refresh
   */
  const handleManualRefresh = async () => {
    await refresh();
  };

  /**
   * Handle component click
   */
  const handleComponentClick = (component: ISecurityComponent) => {
    // TODO: Implement component detail view or modal
    console.log('Component clicked:', component);
  };

  /**
   * Handle operation complete
   */
  const handleOperationComplete = (result: unknown) => {
    console.log('Operation completed:', result);
    // Refresh security data after operation
    refresh();
  };

  /**
   * Handle alert dismiss
   */
  const handleAlertDismiss = (alertId: string) => {
    clearAlert(alertId);
  };

  /**
   * Render loading state
   */
  if (loading && !securityData) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Loading Security Dashboard
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Fetching security data from M0 components...
          </p>
        </div>
      </div>
    );
  }

  /**
   * Render error state
   */
  if (error && !securityData) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="flex items-center mb-4">
            <div className="flex-shrink-0">
              <svg className="h-12 w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Error Loading Dashboard
              </h3>
            </div>
          </div>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {error}
          </p>
          <button
            onClick={handleManualRefresh}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  /**
   * Render dashboard
   */
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                🔒 Security Dashboard
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Real-time memory safety and security monitoring
              </p>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Alert Badge */}
              {unacknowledgedCount > 0 && (
                <div className="relative">
                  <button className="relative p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                    </svg>
                    <span className="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full">
                      {unacknowledgedCount}
                    </span>
                  </button>
                </div>
              )}

              {/* Last Update */}
              {lastUpdate && (
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Last update: {lastUpdate.toLocaleTimeString()}
                </div>
              )}

              {/* Refresh Button */}
              <button
                onClick={handleManualRefresh}
                disabled={isRefreshing}
                className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <svg 
                  className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                {isRefreshing ? 'Refreshing...' : 'Refresh'}
              </button>

              {/* Auto-refresh Toggle */}
              <button
                onClick={handleAutoRefreshToggle}
                className={`inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  config.autoRefresh
                    ? 'bg-green-600 text-white hover:bg-green-700'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Auto-refresh {config.autoRefresh ? 'ON' : 'OFF'}
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {securityData ? (
          <div className="space-y-6">
            {/* Overview Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Total Components */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Total Components
                    </p>
                    <p className="text-3xl font-bold text-gray-900 dark:text-white mt-2">
                      {securityData.totalSecurityComponents}
                    </p>
                  </div>
                  <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <svg className="h-8 w-8 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Healthy Components */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Healthy Components
                    </p>
                    <p className="text-3xl font-bold text-green-600 dark:text-green-400 mt-2">
                      {securityData.healthyComponents}
                    </p>
                  </div>
                  <div className="p-3 bg-green-100 dark:bg-green-900 rounded-lg">
                    <svg className="h-8 w-8 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Memory Usage */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Memory Usage
                    </p>
                    <p className="text-3xl font-bold text-gray-900 dark:text-white mt-2">
                      {securityData.metrics.memoryUsage.toFixed(1)}%
                    </p>
                  </div>
                  <div className={`p-3 rounded-lg ${
                    securityData.metrics.memoryUsage >= 90 ? 'bg-red-100 dark:bg-red-900' :
                    securityData.metrics.memoryUsage >= 80 ? 'bg-yellow-100 dark:bg-yellow-900' :
                    'bg-green-100 dark:bg-green-900'
                  }`}>
                    <svg className={`h-8 w-8 ${
                      securityData.metrics.memoryUsage >= 90 ? 'text-red-600 dark:text-red-400' :
                      securityData.metrics.memoryUsage >= 80 ? 'text-yellow-600 dark:text-yellow-400' :
                      'text-green-600 dark:text-green-400'
                    }`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Threat Level */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Threat Level
                    </p>
                    <p className={`text-3xl font-bold mt-2 uppercase ${
                      securityData.metrics.threatLevel === 'high' ? 'text-red-600 dark:text-red-400' :
                      securityData.metrics.threatLevel === 'medium' ? 'text-yellow-600 dark:text-yellow-400' :
                      'text-green-600 dark:text-green-400'
                    }`}>
                      {securityData.metrics.threatLevel}
                    </p>
                  </div>
                  <div className={`p-3 rounded-lg ${
                    securityData.metrics.threatLevel === 'high' ? 'bg-red-100 dark:bg-red-900' :
                    securityData.metrics.threatLevel === 'medium' ? 'bg-yellow-100 dark:bg-yellow-900' :
                    'bg-green-100 dark:bg-green-900'
                  }`}>
                    <svg className={`h-8 w-8 ${
                      securityData.metrics.threatLevel === 'high' ? 'text-red-600 dark:text-red-400' :
                      securityData.metrics.threatLevel === 'medium' ? 'text-yellow-600 dark:text-yellow-400' :
                      'text-green-600 dark:text-green-400'
                    }`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            {/* Security Overview Panel */}
            <SecurityOverviewPanel data={securityData} />

            {/* Visualization Grid - Memory, Buffer, and Threat Level */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Memory Usage Monitor */}
              <MemoryUsageMonitor
                metrics={securityData.metrics}
                showTrend={true}
                maxDataPoints={30}
              />

              {/* Buffer Utilization Chart */}
              <BufferUtilizationChart
                metrics={securityData.metrics}
                showTrend={true}
                maxDataPoints={30}
              />

              {/* Threat Level Indicator */}
              <ThreatLevelIndicator
                metrics={securityData.metrics}
                showHistory={true}
                maxHistoryEntries={10}
              />
            </div>

            {/* Component Status Grid */}
            <ComponentStatusGrid
              components={securityData.components}
              onComponentClick={handleComponentClick}
            />

            {/* Security Operations and Results */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Security Operations Panel */}
              <SecurityOperationsPanel
                components={securityData.components}
                onOperationComplete={handleOperationComplete}
              />

              {/* Operation Results Display */}
              <OperationResultsDisplay
                results={operationHistory}
                onClearHistory={clearAll}
                maxResults={10}
              />
            </div>

            {/* Alert History Panel */}
            <AlertHistoryPanel
              alerts={alerts}
              onAcknowledge={acknowledgeAlert}
              onClearAll={clearAll}
              pageSize={20}
            />
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-600 dark:text-gray-400">No security data available</p>
          </div>
        )}
      </main>

      {/* Alert Notifications (Toast) */}
      <AlertNotification
        alerts={alerts}
        onAcknowledge={acknowledgeAlert}
        onDismiss={handleAlertDismiss}
        autoDissmissTimeout={5000}
        position="top-right"
      />
    </div>
  );
}

