'use client';

/**
 * ============================================================================
 * M0 TRACKING DASHBOARD - Specialized Category View
 * ============================================================================
 *
 * Priority 3: Specialized Dashboards
 *
 * Enterprise-grade tracking dashboard displaying real-time status of 21+
 * tracking components with session analysis, data processing status, and
 * orchestration metrics.
 *
 * Features:
 * - Real-time SSE updates for tracking events
 * - Advanced filtering (status, health score, search, sorting)
 * - Tracking-specific metrics (active sessions, events, processing rate)
 * - Specialized operations (session analysis, data processing, orchestration)
 * - Toast notifications for critical tracking events
 * - Responsive design (mobile/tablet/desktop)
 *
 * Authority: President & CEO, <PERSON><PERSON><PERSON>. Consultancy
 * Created: 2025-10-20
 */

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { useM0Stream, IStreamEventData } from '../../hooks/useM0Stream';
import { ToastContainer } from '../../components/ToastNotification';
import { getStatusIcon, getStatusColor, getHealthScoreColor } from '../../lib/M0ApiService';

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

interface ITrackingMetrics {
  activeSessions: number;
  totalEvents: number;
  averageResponseTime: number;
  dataProcessingRate: number;
  lastActivity: string;
}

interface ITrackingComponent {
  id: string;
  name: string;
  status: 'healthy' | 'warning' | 'error' | 'offline';
  category: string;
  healthScore: number;
  lastUpdate: string;
  trackingType: 'session' | 'analytics' | 'orchestration' | 'progress' | 'data-management';
}

interface ITrackingData {
  totalTrackingComponents: number;
  healthyComponents: number;
  errorComponents: number;
  filteredCount: number;
  page: number;
  limit: number;
  totalPages: number;
  metrics: ITrackingMetrics;
  components: ITrackingComponent[];
  query: Record<string, unknown>;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export default function TrackingDashboard() {
  const [loading, setLoading] = useState(true);
  const [trackingData, setTrackingData] = useState<ITrackingData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [liveUpdateCount, setLiveUpdateCount] = useState(0);

  // Filter states
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [sortBy, setSortBy] = useState<string>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage] = useState<number>(20);

  // ============================================================================
  // SSE EVENT HANDLERS
  // ============================================================================

  const handleComponentStatusChange = useCallback((data: IStreamEventData) => {
    // Only refresh if it's a tracking component
    if (data.componentId?.includes('tracking') || data.category === 'tracking') {
      console.log('📊 Tracking component status change:', data);
      setLiveUpdateCount(prev => prev + 1);
      fetchTrackingData();
    }
  }, []);

  const handleHealthScoreChange = useCallback((data: IStreamEventData) => {
    if (data.componentId?.includes('tracking') || data.category === 'tracking') {
      console.log('💚 Tracking health score change:', data);
      setLiveUpdateCount(prev => prev + 1);
      fetchTrackingData();
    }
  }, []);

  const handleErrorDetected = useCallback((data: IStreamEventData) => {
    if (data.componentId?.includes('tracking') || data.category === 'tracking') {
      console.error('❌ Tracking error detected:', data);
      setLiveUpdateCount(prev => prev + 1);
      fetchTrackingData();
    }
  }, []);

  const handleWarningDetected = useCallback((data: IStreamEventData) => {
    if (data.componentId?.includes('tracking') || data.category === 'tracking') {
      console.warn('⚠️ Tracking warning detected:', data);
      setLiveUpdateCount(prev => prev + 1);
      fetchTrackingData();
    }
  }, []);

  // ============================================================================
  // SSE INTEGRATION
  // ============================================================================

  const {
    isConnected: sseConnected,
    eventCounts,
    lastHeartbeat,
    notifications,
    removeNotification
  } = useM0Stream({
    autoConnect: true,
    onComponentStatusChange: handleComponentStatusChange,
    onHealthScoreChange: handleHealthScoreChange,
    onErrorDetected: handleErrorDetected,
    onWarningDetected: handleWarningDetected,
    enableNotifications: true,
    notificationDuration: 5000
  });

  // ============================================================================
  // DATA FETCHING
  // ============================================================================

  const fetchTrackingData = useCallback(async () => {
    try {
      // Build query parameters
      const params = new URLSearchParams();
      if (statusFilter !== 'all') params.append('status', statusFilter);
      if (searchQuery) params.append('search', searchQuery);
      if (sortBy) params.append('sortBy', sortBy);
      if (sortOrder) params.append('order', sortOrder);
      params.append('page', currentPage.toString());
      params.append('limit', itemsPerPage.toString());

      const queryString = params.toString();
      const url = `/api/m0-tracking${queryString ? `?${queryString}` : ''}`;

      console.log('📊 Fetching tracking data:', url);

      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch tracking data: ${response.statusText}`);
      }

      const result = await response.json();
      if (result.success && result.data) {
        setTrackingData(result.data);
        setLastUpdate(new Date());
        setError(null);
      } else {
        throw new Error(result.error || 'Failed to fetch tracking data');
      }
    } catch (err) {
      console.error('Error fetching tracking data:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [statusFilter, searchQuery, sortBy, sortOrder, currentPage, itemsPerPage]);

  // Initial load and filter changes
  useEffect(() => {
    fetchTrackingData();
  }, [fetchTrackingData]);

  // ============================================================================
  // RENDER HELPERS
  // ============================================================================

  const renderTrackingTypeBadge = (type: ITrackingComponent['trackingType']) => {
    const badges = {
      'session': { label: 'Session', color: 'bg-blue-100 text-blue-800' },
      'analytics': { label: 'Analytics', color: 'bg-green-100 text-green-800' },
      'orchestration': { label: 'Orchestration', color: 'bg-purple-100 text-purple-800' },
      'progress': { label: 'Progress', color: 'bg-orange-100 text-orange-800' },
      'data-management': { label: 'Data Mgmt', color: 'bg-pink-100 text-pink-800' }
    };

    const badge = badges[type] || { label: type, color: 'bg-gray-100 text-gray-800' };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${badge.color}`}>
        {badge.label}
      </span>
    );
  };

  // ============================================================================
  // LOADING & ERROR STATES
  // ============================================================================

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600 text-lg">Loading Tracking Dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-xl p-8 max-w-md">
          <div className="text-red-600 text-6xl mb-4">❌</div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Error Loading Dashboard</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => {
              setLoading(true);
              setError(null);
              fetchTrackingData();
            }}
            className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!trackingData) {
    return null;
  }

  // ============================================================================
  // MAIN RENDER
  // ============================================================================

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50">
      {/* Toast Notifications */}
      <ToastContainer notifications={notifications} onDismiss={removeNotification} />

      {/* Header */}
      <header className="bg-white shadow-md border-b-4 border-green-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center gap-3">
                <Link href="/" className="text-green-600 hover:text-green-800 transition-colors">
                  ← Back to Main Dashboard
                </Link>
              </div>
              <h1 className="text-4xl font-bold text-gray-900 mt-2">
                📊 Tracking Dashboard
              </h1>
              <p className="text-gray-600 mt-1">
                Real-time monitoring of {trackingData.totalTrackingComponents} tracking components
              </p>
            </div>
            <div className="text-right">
              <div className="flex items-center gap-2 mb-2">
                <span className={`inline-block w-3 h-3 rounded-full ${sseConnected ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`}></span>
                <span className="text-sm font-medium text-gray-700">
                  {sseConnected ? 'SSE Connected' : 'SSE Disconnected'}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span className="inline-block w-3 h-3 rounded-full bg-orange-500 animate-pulse"></span>
                <span className="text-sm font-medium text-gray-700">
                  Live Updates: {liveUpdateCount}
                </span>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Last update: {lastUpdate.toLocaleTimeString()}
              </p>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tracking Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Active Sessions */}
          <div className="bg-white rounded-lg shadow-lg p-6 border-l-4 border-blue-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Sessions</p>
                <p className="text-3xl font-bold text-blue-600 mt-2">
                  {trackingData.metrics.activeSessions}
                </p>
              </div>
              <div className="text-4xl">👥</div>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              Session trackers and logs
            </p>
          </div>

          {/* Total Events */}
          <div className="bg-white rounded-lg shadow-lg p-6 border-l-4 border-green-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Events</p>
                <p className="text-3xl font-bold text-green-600 mt-2">
                  {trackingData.metrics.totalEvents.toLocaleString()}
                </p>
              </div>
              <div className="text-4xl">📈</div>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              Tracked operations
            </p>
          </div>

          {/* Avg Response Time */}
          <div className="bg-white rounded-lg shadow-lg p-6 border-l-4 border-orange-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Response</p>
                <p className="text-3xl font-bold text-orange-600 mt-2">
                  {trackingData.metrics.averageResponseTime.toFixed(1)}ms
                </p>
              </div>
              <div className="text-4xl">⚡</div>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              Average response time
            </p>
          </div>

          {/* Processing Rate */}
          <div className="bg-white rounded-lg shadow-lg p-6 border-l-4 border-purple-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Processing Rate</p>
                <p className="text-3xl font-bold text-purple-600 mt-2">
                  {(trackingData.metrics.dataProcessingRate / 1000).toFixed(1)}k/s
                </p>
              </div>
              <div className="text-4xl">🔄</div>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              Data processing rate
            </p>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-bold text-gray-900 mb-4">🔍 Filters & Search</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Status Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                value={statusFilter}
                onChange={(e) => {
                  setStatusFilter(e.target.value);
                  setCurrentPage(1);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="all">All Statuses</option>
                <option value="healthy">Healthy</option>
                <option value="warning">Warning</option>
                <option value="error">Error</option>
                <option value="offline">Offline</option>
              </select>
            </div>

            {/* Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Search
              </label>
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                  setCurrentPage(1);
                }}
                placeholder="Search components..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            {/* Sort By */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Sort By
              </label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="name">Name</option>
                <option value="healthScore">Health Score</option>
                <option value="lastUpdate">Last Update</option>
              </select>
            </div>

            {/* Sort Order */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Order
              </label>
              <select
                value={sortOrder}
                onChange={(e) => setSortOrder(e.target.value as 'asc' | 'desc')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="asc">Ascending</option>
                <option value="desc">Descending</option>
              </select>
            </div>
          </div>

          {/* Results Summary */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <p className="text-sm text-gray-600">
              Showing <span className="font-semibold">{trackingData.components.length}</span> of{' '}
              <span className="font-semibold">{trackingData.filteredCount}</span> filtered components
              {trackingData.filteredCount !== trackingData.totalTrackingComponents && (
                <span> (Total: {trackingData.totalTrackingComponents})</span>
              )}
            </p>
          </div>
        </div>

        {/* Components Table */}
        <div className="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 className="text-xl font-bold text-gray-900">
              📊 Tracking Components
            </h2>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Component
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Health Score
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Update
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {trackingData.components.map((component) => (
                  <tr key={component.id} className="hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{component.name}</div>
                      <div className="text-xs text-gray-500">{component.id}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {renderTrackingTypeBadge(component.trackingType)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(component.status)}`}>
                        {getStatusIcon(component.status)} {component.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`text-sm font-bold ${getHealthScoreColor(component.healthScore)}`}>
                        {component.healthScore}%
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(component.lastUpdate).toLocaleString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {trackingData.totalPages > 1 && (
            <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Page {trackingData.page} of {trackingData.totalPages}
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(trackingData.totalPages, prev + 1))}
                  disabled={currentPage === trackingData.totalPages}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>

        {/* SSE Statistics */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-4">📡 Real-Time SSE Statistics</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-sm text-gray-600">Connection</p>
              <p className={`text-lg font-bold ${sseConnected ? 'text-green-600' : 'text-gray-400'}`}>
                {sseConnected ? 'Connected' : 'Disconnected'}
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-600">Live Updates</p>
              <p className="text-lg font-bold text-orange-600">{liveUpdateCount}</p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-600">Total Events</p>
              <p className="text-lg font-bold text-blue-600">
                {Object.values(eventCounts).reduce((a, b) => a + b, 0)}
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-600">Last Heartbeat</p>
              <p className="text-lg font-bold text-purple-600">
                {lastHeartbeat ? lastHeartbeat.toLocaleTimeString() : 'N/A'}
              </p>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center text-sm text-gray-600">
            <p className="font-semibold">M0 Tracking Dashboard - Priority 3: Specialized Dashboards</p>
            <p className="mt-1">
              Real-time monitoring • {trackingData.totalTrackingComponents} components •
              SSE {sseConnected ? 'Connected' : 'Disconnected'} •
              {liveUpdateCount} live updates
            </p>
            <p className="mt-2 text-xs text-gray-500">
              Authority: President & CEO, E.Z. Consultancy • Created: 2025-10-20
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}

