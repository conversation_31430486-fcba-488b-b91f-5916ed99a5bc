/**
 * ============================================================================
 * M0.1 Overview Dashboard Page
 * ============================================================================
 * 
 * Purpose: Comprehensive overview of M0.1 milestone enhancements
 * Features: Milestone statistics, task completion, performance metrics, component gallery
 * 
 * This page showcases the 50 completed M0.1 tasks and 6 enhanced components
 * with real-time data from the M0.1 API routes.
 * 
 * Author: AI Assistant (Phase 2 Day 3 Implementation)
 * Created: 2025-12-31
 * ============================================================================
 */

'use client';

import { Container, Box, useTheme } from '@mui/material';
import M01OverviewDashboard from '../../components/dashboards/M01OverviewDashboard';

export default function M01OverviewPage() {
  const theme = useTheme();

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: theme.palette.mode === 'dark'
          ? 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)'
          : 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',
        position: 'relative',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: theme.palette.mode === 'dark'
            ? 'radial-gradient(circle at 20% 50%, rgba(102, 126, 234, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.1) 0%, transparent 50%)'
            : 'radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.2) 0%, transparent 50%), radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.15) 0%, transparent 50%)',
          pointerEvents: 'none',
        }
      }}
    >
      <Container maxWidth="xl">
        <Box sx={{ py: 3, position: 'relative', zIndex: 1 }}>
          <M01OverviewDashboard />
        </Box>
      </Container>
    </Box>
  );
}

