/**
 * ============================================================================
 * OA FRAMEWORK - M0 REAL DASHBOARD
 * Governance Dashboard Page
 * ============================================================================
 * 
 * @fileoverview Main governance dashboard page integrating all governance components
 * @module app/governance-dashboard/page
 * @version 1.0.0
 * @since 2025-10-22
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2025 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useGovernanceData } from '@/hooks/useGovernanceData';
import { useGovernanceOperations } from '@/hooks/useGovernanceOperations';
import { useGovernanceAlerts } from '@/hooks/useGovernanceAlerts';
import { GovernanceOverviewPanel } from '@/components/governance/GovernanceOverviewPanel';
import { ComplianceScoreGauge } from '@/components/governance/ComplianceScoreGauge';
import { RuleEngineStatus } from '@/components/governance/RuleEngineStatus';
import { FrameworkStatusGrid } from '@/components/governance/FrameworkStatusGrid';
import { ViolationsList } from '@/components/governance/ViolationsList';
import { ComplianceOperationsPanel } from '@/components/governance/ComplianceOperationsPanel';
import { OperationResultsDisplay } from '@/components/governance/OperationResultsDisplay';
import { AlertNotification } from '@/components/governance/AlertNotification';
import { AlertHistoryPanel } from '@/components/governance/AlertHistoryPanel';
import { ArrowLeft, RefreshCw, Bell, Shield } from 'lucide-react';

// ============================================================================
// PAGE COMPONENT
// ============================================================================

/**
 * Governance Dashboard Page
 * 
 * Main dashboard for governance monitoring featuring:
 * - Real-time governance metrics
 * - Compliance score tracking
 * - Rule engine monitoring
 * - Framework status grid
 * - Violations tracking
 * - Governance operations
 * - Alert management
 * 
 * @returns Governance dashboard page
 */
export default function GovernanceDashboardPage(): JSX.Element {
  const [showAlerts, setShowAlerts] = useState<boolean>(true);

  // Fetch governance data with auto-refresh (30 seconds)
  const { data, loading, error, lastUpdate, refresh, isRefreshing } = useGovernanceData(30000, true);

  // Governance operations
  const {
    operationStatus,
    operationResult,
    runComplianceCheck,
    runRuleValidation,
    runFrameworkAudit,
  } = useGovernanceOperations();

  // Alert management
  const {
    alerts,
    unacknowledgedCount,
    acknowledgeAlert,
    clearAlert,
    clearAll,
  } = useGovernanceAlerts(data);

  // Loading state
  if (loading && !data) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="w-12 h-12 text-purple-600 dark:text-purple-400 animate-spin mx-auto mb-4" />
          <p className="text-lg font-medium text-gray-900 dark:text-white">
            Loading Governance Dashboard...
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
            Fetching governance data
          </p>
        </div>
      </div>
    );
  }

  // Error state
  if (error && !data) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center max-w-md">
          <Shield className="w-12 h-12 text-red-600 dark:text-red-400 mx-auto mb-4" />
          <p className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Failed to Load Governance Data
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            {error}
          </p>
          <button
            onClick={refresh}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm font-medium transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!data) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link
                href="/"
                className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
                  <Shield className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                  Governance Dashboard
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Real-time governance monitoring and compliance tracking
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              {/* Alert Toggle */}
              <button
                onClick={() => setShowAlerts(!showAlerts)}
                className="relative px-3 py-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white transition-colors"
              >
                <Bell className="w-5 h-5" />
                {unacknowledgedCount > 0 && (
                  <span className="absolute -top-1 -right-1 px-1.5 py-0.5 text-xs font-bold text-white bg-red-600 rounded-full">
                    {unacknowledgedCount}
                  </span>
                )}
              </button>

              {/* Refresh Button */}
              <button
                onClick={refresh}
                disabled={isRefreshing}
                className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white rounded-lg text-sm font-medium transition-colors flex items-center gap-2"
              >
                <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                Refresh
              </button>
            </div>
          </div>

          {/* Last Update */}
          {lastUpdate && (
            <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
              Last updated: {lastUpdate.toLocaleString()}
            </div>
          )}
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Active Alerts */}
        {showAlerts && alerts.filter(a => !a.acknowledged).length > 0 && (
          <div className="mb-6 space-y-3">
            {alerts
              .filter(a => !a.acknowledged)
              .slice(0, 3)
              .map(alert => (
                <AlertNotification
                  key={alert.id}
                  alert={alert}
                  onAcknowledge={acknowledgeAlert}
                  onDismiss={clearAlert}
                />
              ))}
          </div>
        )}

        {/* Dashboard Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Overview & Metrics */}
          <div className="lg:col-span-2 space-y-6">
            {/* Overview Panel */}
            <GovernanceOverviewPanel data={data} />

            {/* Metrics Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <ComplianceScoreGauge score={data.metrics.complianceScore} showTrend={true} />
              <RuleEngineStatus data={data} />
            </div>

            {/* Framework Status Grid */}
            <FrameworkStatusGrid data={data} />

            {/* Violations List */}
            <ViolationsList data={data} />
          </div>

          {/* Right Column - Operations & Alerts */}
          <div className="space-y-6">
            {/* Operations Panel */}
            <ComplianceOperationsPanel
              operationStatus={operationStatus}
              onComplianceCheck={() => runComplianceCheck()}
              onRuleValidation={() => runRuleValidation()}
              onFrameworkAudit={() => runFrameworkAudit()}
            />

            {/* Operation Results */}
            <OperationResultsDisplay result={operationResult} />

            {/* Alert History */}
            <AlertHistoryPanel alerts={alerts} onClearAll={clearAll} />
          </div>
        </div>
      </main>
    </div>
  );
}

