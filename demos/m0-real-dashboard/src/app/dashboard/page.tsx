'use client';

/**
 * M0 Real Dashboard - Overview Page
 * Task B: UI Development Phase 1
 * 
 * Displays comprehensive overview of all 136 integrated M0 components with:
 * - Overall health score visualization
 * - Total component count display
 * - Category breakdown cards (Governance, Tracking, Memory Safety, Integration)
 * - Real-time status updates
 * - Responsive design (mobile/tablet/desktop)
 */

import { useEffect, useState } from 'react';
import { IM0DashboardData } from '../../lib/M0ComponentManager';

export default function DashboardPage() {
  const [dashboardData, setDashboardData] = useState<IM0DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // Fetch dashboard data from API
  const fetchDashboardData = async () => {
    try {
      const response = await fetch('/api/m0-components');
      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }
      const result = await response.json();

      // API wraps data in { success: true, data: dashboardData } structure
      if (result.success && result.data) {
        setDashboardData(result.data);
        setLastUpdate(new Date());
        setError(null);
      } else {
        throw new Error(result.error || 'Invalid API response structure');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch dashboard data');
      console.error('Dashboard data fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch and real-time updates (every 5 seconds)
  useEffect(() => {
    fetchDashboardData();
    const interval = setInterval(fetchDashboardData, 5000);
    return () => clearInterval(interval);
  }, []);

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-400 mx-auto mb-4"></div>
          <p className="text-white text-xl font-semibold">Loading M0 Dashboard...</p>
          <p className="text-blue-300 text-sm mt-2">Initializing 136 components</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-red-900 to-slate-900 flex items-center justify-center">
        <div className="bg-red-900/30 border border-red-500 rounded-lg p-8 max-w-md">
          <h2 className="text-red-400 text-2xl font-bold mb-4">❌ Dashboard Error</h2>
          <p className="text-white mb-4">{error}</p>
          <button
            onClick={() => {
              setLoading(true);
              setError(null);
              fetchDashboardData();
            }}
            className="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-6 rounded-lg transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // No data state
  if (!dashboardData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 flex items-center justify-center">
        <p className="text-white text-xl">No dashboard data available</p>
      </div>
    );
  }

  // Validate categories exist
  if (!dashboardData.categories) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-red-900 to-slate-900 flex items-center justify-center">
        <div className="bg-red-900/30 border border-red-500 rounded-lg p-8 max-w-md">
          <h2 className="text-red-400 text-2xl font-bold mb-4">❌ Invalid Data Structure</h2>
          <p className="text-white mb-4">Dashboard data is missing category information</p>
          <button
            onClick={() => {
              setLoading(true);
              setError(null);
              fetchDashboardData();
            }}
            className="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-6 rounded-lg transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Calculate category completion percentages with safe defaults
  const categoryStats = {
    governance: {
      count: dashboardData.categories.governance?.length || 0,
      target: 61,
      percentage: Math.round(((dashboardData.categories.governance?.length || 0) / 61) * 100)
    },
    tracking: {
      count: dashboardData.categories.tracking?.length || 0,
      target: 33,
      percentage: Math.round(((dashboardData.categories.tracking?.length || 0) / 33) * 100)
    },
    memorySafety: {
      count: dashboardData.categories.memorySafety?.length || 0,
      target: 14,
      percentage: Math.round(((dashboardData.categories.memorySafety?.length || 0) / 14) * 100)
    },
    integration: {
      count: dashboardData.categories.integration?.length || 0,
      target: 15,
      percentage: Math.round(((dashboardData.categories.integration?.length || 0) / 15) * 100)
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 p-4 md:p-8">
      {/* Header */}
      <header className="mb-8">
        <h1 className="text-4xl md:text-5xl font-bold text-white mb-2">
          M0 Real Dashboard
        </h1>
        <p className="text-blue-300 text-lg">
          Enterprise Component Integration & Monitoring
        </p>
        <p className="text-slate-400 text-sm mt-2">
          Last updated: {lastUpdate.toLocaleTimeString()}
        </p>
      </header>

      {/* Overall Health Score */}
      <div className="bg-gradient-to-r from-green-900/40 to-emerald-900/40 border border-green-500/50 rounded-xl p-6 md:p-8 mb-8 shadow-2xl">
        <div className="flex flex-col md:flex-row items-center justify-between">
          <div className="mb-4 md:mb-0">
            <h2 className="text-2xl md:text-3xl font-bold text-white mb-2">
              Overall Health Score
            </h2>
            <p className="text-green-300 text-lg">
              {dashboardData.healthyComponents} / {dashboardData.totalComponents} components healthy
            </p>
          </div>
          <div className="relative">
            <div className="text-6xl md:text-8xl font-bold text-green-400">
              {dashboardData.overallHealthScore}%
            </div>
            {dashboardData.overallHealthScore === 100 && (
              <div className="absolute -top-2 -right-2 text-4xl animate-bounce">✅</div>
            )}
          </div>
        </div>

        {/* Health Score Progress Bar */}
        <div className="mt-6 bg-slate-800/50 rounded-full h-4 overflow-hidden">
          <div
            className="bg-gradient-to-r from-green-500 to-emerald-400 h-full transition-all duration-500 ease-out"
            style={{ width: `${dashboardData.overallHealthScore}%` }}
          ></div>
        </div>
      </div>

      {/* Total Components Card */}
      <div className="bg-gradient-to-r from-blue-900/40 to-indigo-900/40 border border-blue-500/50 rounded-xl p-6 mb-8 shadow-2xl">
        <h2 className="text-2xl font-bold text-white mb-4">Total Components</h2>
        <div className="flex items-baseline gap-4">
          <div className="text-5xl md:text-6xl font-bold text-blue-400">
            {dashboardData.totalComponents}
          </div>
          <div className="text-blue-300 text-lg">
            components integrated
          </div>
        </div>
        <p className="text-slate-400 mt-2">
          Target: 123+ components (111% complete) 🎉
        </p>
      </div>

      {/* Category Breakdown Cards */}
      <h2 className="text-3xl font-bold text-white mb-6">Category Breakdown</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Governance Category */}
        <div className="bg-gradient-to-br from-purple-900/40 to-pink-900/40 border border-purple-500/50 rounded-xl p-6 shadow-xl hover:shadow-2xl transition-shadow">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-bold text-white">Governance</h3>
            <span className="text-3xl">🏛️</span>
          </div>
          <div className="text-4xl font-bold text-purple-400 mb-2">
            {categoryStats.governance.count}
          </div>
          <p className="text-purple-300 text-sm mb-4">
            Target: {categoryStats.governance.target} components
          </p>
          <div className="bg-slate-800/50 rounded-full h-3 overflow-hidden mb-2">
            <div
              className="bg-gradient-to-r from-purple-500 to-pink-400 h-full transition-all duration-500"
              style={{ width: `${Math.min(categoryStats.governance.percentage, 100)}%` }}
            ></div>
          </div>
          <p className="text-purple-300 text-sm font-semibold">
            {categoryStats.governance.percentage}% complete
          </p>
        </div>

        {/* Tracking Category */}
        <div className="bg-gradient-to-br from-blue-900/40 to-cyan-900/40 border border-blue-500/50 rounded-xl p-6 shadow-xl hover:shadow-2xl transition-shadow">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-bold text-white">Tracking</h3>
            <span className="text-3xl">📊</span>
          </div>
          <div className="text-4xl font-bold text-blue-400 mb-2">
            {categoryStats.tracking.count}
          </div>
          <p className="text-blue-300 text-sm mb-4">
            Target: {categoryStats.tracking.target} components
          </p>
          <div className="bg-slate-800/50 rounded-full h-3 overflow-hidden mb-2">
            <div
              className="bg-gradient-to-r from-blue-500 to-cyan-400 h-full transition-all duration-500"
              style={{ width: `${Math.min(categoryStats.tracking.percentage, 100)}%` }}
            ></div>
          </div>
          <p className="text-blue-300 text-sm font-semibold">
            {categoryStats.tracking.percentage}% complete {categoryStats.tracking.percentage === 100 && '🎉'}
          </p>
        </div>

        {/* Memory Safety Category */}
        <div className="bg-gradient-to-br from-green-900/40 to-emerald-900/40 border border-green-500/50 rounded-xl p-6 shadow-xl hover:shadow-2xl transition-shadow">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-bold text-white">Memory Safety</h3>
            <span className="text-3xl">🛡️</span>
          </div>
          <div className="text-4xl font-bold text-green-400 mb-2">
            {categoryStats.memorySafety.count}
          </div>
          <p className="text-green-300 text-sm mb-4">
            Target: {categoryStats.memorySafety.target} components
          </p>
          <div className="bg-slate-800/50 rounded-full h-3 overflow-hidden mb-2">
            <div
              className="bg-gradient-to-r from-green-500 to-emerald-400 h-full transition-all duration-500"
              style={{ width: `${Math.min(categoryStats.memorySafety.percentage, 100)}%` }}
            ></div>
          </div>
          <p className="text-green-300 text-sm font-semibold">
            {categoryStats.memorySafety.percentage}% complete
          </p>
        </div>

        {/* Integration Category */}
        <div className="bg-gradient-to-br from-orange-900/40 to-red-900/40 border border-orange-500/50 rounded-xl p-6 shadow-xl hover:shadow-2xl transition-shadow">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-bold text-white">Integration</h3>
            <span className="text-3xl">🔗</span>
          </div>
          <div className="text-4xl font-bold text-orange-400 mb-2">
            {categoryStats.integration.count}
          </div>
          <p className="text-orange-300 text-sm mb-4">
            Target: {categoryStats.integration.target} components
          </p>
          <div className="bg-slate-800/50 rounded-full h-3 overflow-hidden mb-2">
            <div
              className="bg-gradient-to-r from-orange-500 to-red-400 h-full transition-all duration-500"
              style={{ width: `${Math.min(categoryStats.integration.percentage, 100)}%` }}
            ></div>
          </div>
          <p className="text-orange-300 text-sm font-semibold">
            {categoryStats.integration.percentage}% complete {categoryStats.integration.percentage === 100 && '🎉'}
          </p>
        </div>
      </div>

      {/* Footer */}
      <footer className="mt-12 text-center text-slate-400 text-sm">
        <p>M0 Real Dashboard - Enterprise Component Integration</p>
        <p className="mt-1">Authority: President & CEO, E.Z. Consultancy</p>
        <p className="mt-1">Auto-refresh: Every 5 seconds</p>
      </footer>
    </div>
  );
}

