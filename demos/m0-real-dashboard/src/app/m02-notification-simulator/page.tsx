/**
 * ============================================================================
 * NOTIFICATION SCENARIO SIMULATOR PAGE - M0.2 FEATURE 3.2
 * ============================================================================
 * 
 * Interactive notification scenario simulator page
 * 
 * Authority: President & CEO, E<PERSON>Z. Consultancy
 * Created: 2026-01-14
 * Milestone: M0.2 Demo - Feature 3.2
 * ============================================================================
 */

import React from 'react';
import { NotificationScenarioSimulator } from '@/components/m02/NotificationScenarioSimulator';

/**
 * Notification Scenario Simulator Page
 */
export default function NotificationSimulatorPage(): JSX.Element {
  return <NotificationScenarioSimulator />;
}

