'use client';

import React from 'react';
import { Container, Box, Typography, Paper } from '@mui/material';
import LogVolumeAnalytics from '@/components/m03/LogVolumeAnalytics';

export default function M03AnalyticsPage() {
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Paper sx={{ p: 3, mb: 4, background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
          📊 Log Volume & Performance Analytics
        </Typography>
        <Typography variant="body1">
          Analyze log volume trends, performance overhead, and storage cost impact of configuration optimization
        </Typography>
      </Paper>

      <Box sx={{ mt: 4 }}>
        <LogVolumeAnalytics />
      </Box>
    </Container>
  );
}
