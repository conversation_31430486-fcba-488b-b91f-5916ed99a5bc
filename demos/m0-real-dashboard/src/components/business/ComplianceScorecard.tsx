/**
 * ============================================================================
 * OA FRAMEWORK - BUSINESS COMPLIANCE SCORECARD COMPONENT
 * Compliance Scorecard Dashboard Component
 * ============================================================================
 * 
 * @fileoverview Compliance scorecard component with regulatory tracking and gap analysis
 * @module components/business/ComplianceScorecard
 * @version 1.0.0
 * @since 2026-02-01
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2026 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

'use client';

import React, { useState } from 'react';
import { Shield, FileText, AlertTriangle, CheckCircle, Clock, TrendingUp } from 'lucide-react';
import { ExecutiveDashboardData } from '../../types/business-types';

// ============================================================================
// REGULATION CARD COMPONENT
// ============================================================================

interface RegulationCardProps {
  regulation: {
    id: string;
    name: string;
    acronym: string;
    description: string;
    requirements: any[];
    score: number;
    lastAudit: Date;
    nextAudit: Date;
    status: string;
  };
}

/**
 * Regulation Card Component
 */
const RegulationCard: React.FC<RegulationCardProps> = ({ regulation }) => {
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'compliant': return 'text-green-400';
      case 'partial': return 'text-yellow-400';
      case 'non_compliant': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'compliant': return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'partial': return <Clock className="w-5 h-5 text-yellow-400" />;
      case 'non_compliant': return <AlertTriangle className="w-5 h-5 text-red-400" />;
      default: return <Shield className="w-5 h-5 text-gray-400" />;
    }
  };

  const getScoreColor = (score: number): string => {
    if (score >= 90) return 'from-green-500 to-emerald-400';
    if (score >= 70) return 'from-yellow-500 to-orange-400';
    return 'from-red-500 to-pink-400';
  };

  const getMetRequirements = () => {
    return regulation.requirements.filter(r => r.status === 'met').length;
  };

  const getTotalRequirements = () => {
    return regulation.requirements.length;
  };

  return (
    <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-xl p-6 hover:shadow-xl transition-all duration-300 hover:border-slate-600/50">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className={`w-10 h-10 bg-gradient-to-br ${getScoreColor(regulation.score)} rounded-lg flex items-center justify-center`}>
            {getStatusIcon(regulation.status)}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white">{regulation.name}</h3>
            <p className="text-sm text-slate-400">{regulation.acronym} - {regulation.description}</p>
          </div>
        </div>
        <div className={`text-sm font-semibold ${getStatusColor(regulation.status)}`}>
          {regulation.status.replace('_', ' ').toUpperCase()}
        </div>
      </div>

      <div className="space-y-3 mb-4">
        <div>
          <div className="flex justify-between text-sm text-slate-400 mb-1">
            <span>Compliance Score</span>
            <span>{regulation.score}%</span>
          </div>
          <div className="w-full bg-slate-700 rounded-full h-2">
            <div
              className={`h-2 rounded-full bg-gradient-to-r ${getScoreColor(regulation.score)}`}
              style={{ width: `${regulation.score}%` }}
            ></div>
          </div>
        </div>

        <div>
          <div className="flex justify-between text-sm text-slate-400 mb-1">
            <span>Requirements</span>
            <span>{getMetRequirements()} / {getTotalRequirements()}</span>
          </div>
          <div className="w-full bg-slate-700 rounded-full h-2">
            <div
              className="h-2 rounded-full bg-gradient-to-r from-blue-500 to-cyan-400"
              style={{ width: `${(getMetRequirements() / getTotalRequirements()) * 100}%` }}
            ></div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <div className="text-sm text-slate-400 mb-1">Last Audit</div>
          <div className="text-sm text-white">{regulation.lastAudit.toLocaleDateString()}</div>
        </div>
        <div>
          <div className="text-sm text-slate-400 mb-1">Next Audit</div>
          <div className="text-sm font-semibold text-white">{regulation.nextAudit.toLocaleDateString()}</div>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="text-xs text-slate-400">
          {getMetRequirements()} met requirements
        </div>
        <div className="text-xs text-slate-400">
          {regulation.requirements.filter(r => r.status === 'not_met').length} gaps identified
        </div>
      </div>
    </div>
  );
};

// ============================================================================
// COMPLIANCE GAP ANALYSIS COMPONENT
// ============================================================================

interface GapAnalysisProps {
  gaps: ExecutiveDashboardData['complianceGaps'];
}

/**
 * Gap Analysis Component
 */
const GapAnalysis: React.FC<GapAnalysisProps> = ({ gaps }) => {
  const getGapSeverityColor = (severity: string): string => {
    switch (severity) {
      case 'high': return 'text-red-400';
      case 'medium': return 'text-yellow-400';
      case 'low': return 'text-green-400';
      default: return 'text-gray-400';
    }
  };

  const getGapSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'high': return <AlertTriangle className="w-4 h-4 text-red-400" />;
      case 'medium': return <Clock className="w-4 h-4 text-yellow-400" />;
      case 'low': return <CheckCircle className="w-4 h-4 text-green-400" />;
      default: return <FileText className="w-4 h-4 text-gray-400" />;
    }
  };

  return (
    <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-xl p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-bold text-white">Compliance Gap Analysis</h3>
          <p className="text-sm text-slate-400">Identified gaps and remediation efforts</p>
        </div>
        <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-pink-400 rounded-lg flex items-center justify-center">
          <AlertTriangle className="w-6 h-6 text-white" />
        </div>
      </div>

      <div className="space-y-4">
        {gaps.map((gap, index) => (
          <div key={index} className="bg-slate-700/50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-3">
                {getGapSeverityIcon(gap.gapSeverity)}
                <div>
                  <div className="font-semibold text-white">{gap.regulation} - {gap.requirement}</div>
                  <div className="text-sm text-slate-400">{gap.currentState}</div>
                </div>
              </div>
              <div className={`text-sm font-semibold ${getGapSeverityColor(gap.gapSeverity)}`}>
                {gap.gapSeverity.toUpperCase()}
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-slate-400">Required State:</span>
                <span className="ml-2 text-white">{gap.requiredState}</span>
              </div>
              <div>
                <span className="text-slate-400">Remediation Effort:</span>
                <span className="ml-2 text-white capitalize">{gap.remediationEffort}</span>
              </div>
              <div>
                <span className="text-slate-400">Estimated Cost:</span>
                <span className="ml-2 text-white">${gap.estimatedCost.toLocaleString()}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// ============================================================================
// COMPLIANCE SCORECARD MAIN COMPONENT
// ============================================================================

interface ComplianceScorecardProps {
  data: ExecutiveDashboardData;
}

/**
 * Compliance Scorecard Main Component
 */
const ComplianceScorecard: React.FC<ComplianceScorecardProps> = ({ data }) => {
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('score');

  const filteredRegulations = data.compliance.filter(regulation => {
    return filterStatus === 'all' || regulation.status === filterStatus;
  });

  const sortedRegulations = [...filteredRegulations].sort((a, b) => {
    switch (sortBy) {
      case 'score': return b.score - a.score;
      case 'name': return a.name.localeCompare(b.name);
      case 'status': return a.status.localeCompare(b.status);
      default: return 0;
    }
  });

  const getComplianceSummary = () => {
    const compliantRegs = data.compliance.filter(r => r.status === 'compliant').length;
    const partialRegs = data.compliance.filter(r => r.status === 'partial').length;
    const nonCompliantRegs = data.compliance.filter(r => r.status === 'non_compliant').length;
    const avgScore = Math.round(data.compliance.reduce((sum, r) => sum + r.score, 0) / data.compliance.length);
    const totalGaps = data.complianceGaps.length;
    
    return { compliantRegs, partialRegs, nonCompliantRegs, avgScore, totalGaps };
  };

  const summary = getComplianceSummary();

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-900/40 to-cyan-900/40 border border-blue-500/50 rounded-xl p-6 shadow-2xl">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold text-white mb-2">Compliance Scorecard</h2>
            <p className="text-blue-300 text-lg">
              Regulatory compliance tracking and audit readiness
            </p>
          </div>
          <div className="text-right">
            <div className="text-sm text-slate-300 mb-1">Overall Compliance</div>
            <div className="text-lg font-semibold text-white">
              {summary.avgScore}%
            </div>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <div className="bg-gradient-to-br from-green-800/50 to-emerald-800/50 border border-green-500/50 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-green-400">{summary.compliantRegs}</div>
              <div className="text-sm text-slate-400">Fully Compliant</div>
            </div>
            <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-400" />
            </div>
          </div>
        </div>
        <div className="bg-gradient-to-br from-yellow-800/50 to-orange-800/50 border border-yellow-500/50 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-yellow-400">{summary.partialRegs}</div>
              <div className="text-sm text-slate-400">Partial Compliance</div>
            </div>
            <div className="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
              <Clock className="w-6 h-6 text-yellow-400" />
            </div>
          </div>
        </div>
        <div className="bg-gradient-to-br from-red-800/50 to-pink-800/50 border border-red-500/50 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-red-400">{summary.nonCompliantRegs}</div>
              <div className="text-sm text-slate-400">Non-Compliant</div>
            </div>
            <div className="w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center">
              <AlertTriangle className="w-6 h-6 text-red-400" />
            </div>
          </div>
        </div>
        <div className="bg-gradient-to-br from-blue-800/50 to-cyan-800/50 border border-blue-500/50 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-blue-400">{summary.avgScore}%</div>
              <div className="text-sm text-slate-400">Average Score</div>
            </div>
            <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-blue-400" />
            </div>
          </div>
        </div>
        <div className="bg-gradient-to-br from-purple-800/50 to-pink-800/50 border border-purple-500/50 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-purple-400">{summary.totalGaps}</div>
              <div className="text-sm text-slate-400">Compliance Gaps</div>
            </div>
            <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
              <FileText className="w-6 h-6 text-purple-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Sort */}
      <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-xl p-6">
        <div className="flex flex-wrap gap-4 items-center">
          <div>
            <label className="text-sm text-slate-400 mb-2 block">Filter by Status</label>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="bg-slate-700 text-white px-4 py-2 rounded-lg border border-slate-600"
            >
              <option value="all">All Statuses</option>
              <option value="compliant">Compliant</option>
              <option value="partial">Partial</option>
              <option value="non_compliant">Non-Compliant</option>
            </select>
          </div>
          <div>
            <label className="text-sm text-slate-400 mb-2 block">Sort by</label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="bg-slate-700 text-white px-4 py-2 rounded-lg border border-slate-600"
            >
              <option value="score">Score</option>
              <option value="name">Name</option>
              <option value="status">Status</option>
            </select>
          </div>
          <div className="ml-auto text-sm text-slate-400">
            Showing {filteredRegulations.length} of {data.compliance.length} regulations
          </div>
        </div>
      </div>

      {/* Gap Analysis */}
      <GapAnalysis gaps={data.complianceGaps} />

      {/* Regulations Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {sortedRegulations.map((regulation) => (
          <RegulationCard key={regulation.id} regulation={regulation} />
        ))}
      </div>

      {/* Summary */}
      <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-xl p-6">
        <h3 className="text-xl font-bold text-white mb-4">Compliance Management Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="bg-slate-700/50 rounded-lg p-4">
              <div className="text-sm text-slate-400 mb-1">Total Regulations</div>
              <div className="text-lg font-semibold text-white">{data.compliance.length}</div>
            </div>
            <div className="bg-slate-700/50 rounded-lg p-4">
              <div className="text-sm text-slate-400 mb-1">Audit Readiness</div>
              <div className="text-lg font-semibold text-green-400">
                {Math.round((data.compliance.filter(r => r.status === 'compliant').length / data.compliance.length) * 100)}%
              </div>
            </div>
          </div>
          <div className="space-y-4">
            <div className="bg-slate-700/50 rounded-lg p-4">
              <div className="text-sm text-slate-400 mb-1">High Priority Gaps</div>
              <div className="text-lg font-semibold text-red-400">
                {data.complianceGaps.filter(g => g.gapSeverity === 'high').length}
              </div>
            </div>
            <div className="bg-slate-700/50 rounded-lg p-4">
              <div className="text-sm text-slate-400 mb-1">Remediation Cost</div>
              <div className="text-lg font-semibold text-yellow-400">
                ${data.complianceGaps.reduce((sum, g) => sum + g.estimatedCost, 0).toLocaleString()}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComplianceScorecard;