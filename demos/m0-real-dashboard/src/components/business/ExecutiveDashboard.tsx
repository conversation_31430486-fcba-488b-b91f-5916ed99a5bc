/**
 * ============================================================================
 * OA FRAMEWORK - BUSINESS EXECUTIVE DASHBOARD COMPONENT
 * Executive Dashboard Main Component
 * ============================================================================
 * 
 * @fileoverview Main executive dashboard component displaying business KPIs,
 * overview metrics, and key performance indicators
 * @module components/business/ExecutiveDashboard
 * @version 1.0.0
 * @since 2026-02-01
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2026 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

'use client';

import React from 'react';
import { RefreshCw, TrendingUp, TrendingDown, Minus, DollarSign, Percent, Users, Shield } from 'lucide-react';
import { ExecutiveDashboardData } from '../../types/business-types';

// ============================================================================
// KPI CARD COMPONENT
// ============================================================================

interface KpiCardProps {
  kpi: {
    id: string;
    name: string;
    value: number;
    target: number;
    unit: string;
    trend: 'up' | 'down' | 'stable';
    trendValue: number;
    category: string;
    description: string;
  };
}

/**
 * KPI Card Component
 */
const KpiCard: React.FC<KpiCardProps> = ({ kpi }) => {
  const getTrendIcon = () => {
    switch (kpi.trend) {
      case 'up': return <TrendingUp className="w-4 h-4 text-green-400" />;
      case 'down': return <TrendingDown className="w-4 h-4 text-red-400" />;
      case 'stable': return <Minus className="w-4 h-4 text-yellow-400" />;
      default: return null;
    }
  };

  const getTrendColor = () => {
    switch (kpi.trend) {
      case 'up': return 'text-green-400';
      case 'down': return 'text-red-400';
      case 'stable': return 'text-yellow-400';
      default: return 'text-gray-400';
    }
  };

  const getProgressColor = () => {
    const percentage = (kpi.value / kpi.target) * 100;
    if (percentage >= 100) return 'from-green-500 to-emerald-400';
    if (percentage >= 80) return 'from-yellow-500 to-orange-400';
    return 'from-red-500 to-pink-400';
  };

  const formatValue = (value: number, unit: string): string => {
    if (unit === 'USD') {
      return `$${value.toLocaleString()}`;
    } else if (unit === '%') {
      return `${value}%`;
    } else if (unit === 'count') {
      return value.toLocaleString();
    } else if (unit === 'ms') {
      return `${value}ms`;
    }
    return value.toString();
  };

  return (
    <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-xl p-6 hover:shadow-xl transition-all duration-300 hover:border-slate-600/50">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-cyan-400 rounded-lg flex items-center justify-center">
            {kpi.category === 'revenue' && <DollarSign className="w-6 h-6 text-white" />}
            {kpi.category === 'cost' && <Percent className="w-6 h-6 text-white" />}
            {kpi.category === 'efficiency' && <Users className="w-6 h-6 text-white" />}
            {kpi.category === 'compliance' && <Shield className="w-6 h-6 text-white" />}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white">{kpi.name}</h3>
            <p className="text-sm text-slate-400">{kpi.description}</p>
          </div>
        </div>
        <div className={`flex items-center space-x-1 ${getTrendColor()}`}>
          {getTrendIcon()}
          <span className="text-sm font-medium">
            {kpi.trendValue > 0 ? '+' : ''}{kpi.trendValue}{kpi.unit === '%' ? '%' : ''}
          </span>
        </div>
      </div>
      
      <div className="flex items-end justify-between">
        <div>
          <div className="text-3xl font-bold text-white mb-1">
            {formatValue(kpi.value, kpi.unit)}
          </div>
          <div className="text-sm text-slate-400">
            Target: {formatValue(kpi.target, kpi.unit)}
          </div>
        </div>
        
        <div className="text-right">
          <div className="text-sm text-slate-400 mb-1">Progress</div>
          <div className="w-20 h-2 bg-slate-700 rounded-full overflow-hidden">
            <div
              className={`h-full bg-gradient-to-r ${getProgressColor()}`}
              style={{ width: `${Math.min((kpi.value / kpi.target) * 100, 100)}%` }}
            ></div>
          </div>
          <div className="text-xs text-slate-400 mt-1">
            {Math.round((kpi.value / kpi.target) * 100)}%
          </div>
        </div>
      </div>
    </div>
  );
};

// ============================================================================
// OVERVIEW METRICS COMPONENT
// ============================================================================

interface OverviewMetricsProps {
  overview: ExecutiveDashboardData['overview'];
}

/**
 * Overview Metrics Component
 */
const OverviewMetrics: React.FC<OverviewMetricsProps> = ({ overview }) => {
  const metrics = [
    {
      label: 'Total Revenue',
      value: `$${overview.totalRevenue.toLocaleString()}`,
      icon: DollarSign,
      color: 'from-green-500 to-emerald-400'
    },
    {
      label: 'Net Profit',
      value: `$${overview.netProfit.toLocaleString()}`,
      icon: Percent,
      color: 'from-blue-500 to-cyan-400'
    },
    {
      label: 'Efficiency Score',
      value: `${overview.efficiencyScore}%`,
      icon: Users,
      color: 'from-purple-500 to-pink-400'
    },
    {
      label: 'Compliance Score',
      value: `${overview.complianceScore}%`,
      icon: Shield,
      color: 'from-orange-500 to-red-400'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {metrics.map((metric, index) => (
        <div
          key={index}
          className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-xl p-6 hover:shadow-xl transition-all duration-300 hover:border-slate-600/50"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-slate-400 mb-1">{metric.label}</p>
              <p className="text-2xl font-bold text-white">{metric.value}</p>
            </div>
            <div className={`w-12 h-12 bg-gradient-to-br ${metric.color} rounded-lg flex items-center justify-center`}>
              <metric.icon className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

// ============================================================================
// CATEGORY SECTION COMPONENT
// ============================================================================

interface CategorySectionProps {
  category: {
    id: string;
    name: string;
    icon: string;
    color: string;
    kpis: any[];
    average: number;
    target: number;
  };
}

/**
 * Category Section Component
 */
const CategorySection: React.FC<CategorySectionProps> = ({ category }) => {
  const getAverageColor = () => {
    if (category.average >= 90) return 'text-green-400';
    if (category.average >= 70) return 'text-yellow-400';
    return 'text-red-400';
  };

  return (
    <div className="mb-8">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <div className={`text-4xl ${category.icon}`}></div>
          <div>
            <h2 className="text-2xl font-bold text-white">{category.name}</h2>
            <p className="text-slate-400">Average: <span className={getAverageColor()}>{category.average}%</span></p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-sm text-slate-400 mb-1">Category Target</div>
          <div className="text-lg font-semibold text-white">{category.target}%</div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {category.kpis?.map((kpi) => (
          <KpiCard key={kpi.id} kpi={kpi} />
        )) || (
          <div className="col-span-full text-center text-slate-400 py-8">
            No KPIs available for this category
          </div>
        )}
      </div>
    </div>
  );
};

// ============================================================================
// EXECUTIVE DASHBOARD MAIN COMPONENT
// ============================================================================

interface ExecutiveDashboardProps {
  data: ExecutiveDashboardData;
  onRefresh: () => void;
  isRefreshing: boolean;
}

/**
 * Executive Dashboard Main Component
 */
const ExecutiveDashboard: React.FC<ExecutiveDashboardProps> = ({ data, onRefresh, isRefreshing }) => {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-900/40 to-cyan-900/40 border border-blue-500/50 rounded-xl p-6 shadow-2xl">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold text-white mb-2">Executive Overview</h2>
            <p className="text-blue-300 text-lg">
              Comprehensive business performance and strategic insights
            </p>
          </div>
          <button
            onClick={onRefresh}
            disabled={isRefreshing}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-500 text-white font-semibold px-4 py-2 rounded-lg transition-colors flex items-center space-x-2"
          >
            <RefreshCw className={isRefreshing ? 'animate-spin' : ''} />
            <span>{isRefreshing ? 'Refreshing...' : 'Refresh Data'}</span>
          </button>
        </div>
      </div>

      {/* Overview Metrics */}
      <OverviewMetrics overview={data.overview} />

      {/* Business KPIs by Category */}
      <div className="space-y-8">
        {data.businessKPIs.map((category) => (
          <CategorySection key={category.id} category={category} />
        ))}
      </div>

      {/* Summary Statistics */}
      <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-xl p-6">
        <h3 className="text-xl font-bold text-white mb-4">Summary Statistics</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-green-400">
              {data.businessKPIs.reduce((acc, cat) => acc + (cat.kpis?.filter(k => k.trend === 'up').length || 0), 0)}
            </div>
            <div className="text-sm text-slate-400">Improving KPIs</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-yellow-400">
              {data.businessKPIs.reduce((acc, cat) => acc + (cat.kpis?.filter(k => k.trend === 'stable').length || 0), 0)}
            </div>
            <div className="text-sm text-slate-400">Stable KPIs</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-red-400">
              {data.businessKPIs.reduce((acc, cat) => acc + (cat.kpis?.filter(k => k.trend === 'down').length || 0), 0)}
            </div>
            <div className="text-sm text-slate-400">Declining KPIs</div>
          </div>
        </div>
      </div>

      {/* Last Updated */}
      <div className="text-center text-slate-400 text-sm">
        Last updated: {data.overview.lastUpdated.toLocaleString()}
      </div>
    </div>
  );
};

export default ExecutiveDashboard;