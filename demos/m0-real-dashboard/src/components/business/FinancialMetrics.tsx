/**
 * ============================================================================
 * OA FRAMEWORK - BUSINESS FINANCIAL METRICS COMPONENT
 * Financial Metrics Dashboard Component
 * ============================================================================
 * 
 * @fileoverview Financial metrics component with ROI calculator and cost analysis
 * @module components/business/FinancialMetrics
 * @version 1.0.0
 * @since 2026-02-01
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2026 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

'use client';

import React, { useState } from 'react';
import { DollarSign, TrendingUp, TrendingDown, BarChart3, Download, Calculator } from 'lucide-react';
import { ExecutiveDashboardData } from '../../types/business-types';

// ============================================================================
// FINANCIAL METRIC CARD COMPONENT
// ============================================================================

interface FinancialMetricCardProps {
  metric: {
    id: string;
    name: string;
    value: number;
    currency: string;
    period: string;
    change: number;
    changeType: 'increase' | 'decrease';
    description: string;
    category: string;
  };
}

/**
 * Financial Metric Card Component
 */
const FinancialMetricCard: React.FC<FinancialMetricCardProps> = ({ metric }) => {
  const getChangeColor = () => {
    return metric.changeType === 'increase' ? 'text-green-400' : 'text-red-400';
  };

  const getChangeIcon = () => {
    return metric.changeType === 'increase' ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />;
  };

  const formatCurrency = (value: number, currency: string): string => {
    return `${currency}${value.toLocaleString()}`;
  };

  return (
    <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-xl p-6 hover:shadow-xl transition-all duration-300 hover:border-slate-600/50">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-400 rounded-lg flex items-center justify-center">
            <DollarSign className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white">{metric.name}</h3>
            <p className="text-sm text-slate-400">{metric.description}</p>
          </div>
        </div>
        <div className={`flex items-center space-x-1 ${getChangeColor()}`}>
          {getChangeIcon()}
          <span className="text-sm font-medium">
            {metric.changeType === 'increase' ? '+' : ''}{metric.change}%
          </span>
        </div>
      </div>
      
      <div className="flex items-end justify-between">
        <div>
          <div className="text-3xl font-bold text-white mb-1">
            {formatCurrency(metric.value, metric.currency)}
          </div>
          <div className="text-sm text-slate-400">
            Period: {metric.period}
          </div>
        </div>
        
        <div className="text-right">
          <div className="text-sm text-slate-400 mb-1">Category</div>
          <div className="text-xs font-semibold text-white capitalize">
            {metric.category}
          </div>
        </div>
      </div>
    </div>
  );
};

// ============================================================================
// ROI CALCULATOR COMPONENT
// ============================================================================

interface ROICalculatorProps {
  roiData: ExecutiveDashboardData['roiAnalysis'];
}

/**
 * ROI Calculator Component
 */
const ROICalculator: React.FC<ROICalculatorProps> = ({ roiData }) => {
  const [showDetails, setShowDetails] = useState(false);

  const formatCurrency = (value: number): string => {
    return `$${value.toLocaleString()}`;
  };

  const formatPercentage = (value: number): string => {
    return `${value}%`;
  };

  return (
    <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-xl p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-cyan-400 rounded-lg flex items-center justify-center">
            <Calculator className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-white">ROI Analysis</h3>
            <p className="text-sm text-slate-400">Return on Investment Calculator</p>
          </div>
        </div>
        <button
          onClick={() => setShowDetails(!showDetails)}
          className="text-slate-400 hover:text-white transition-colors"
        >
          {showDetails ? 'Hide Details' : 'Show Details'}
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
        <div className="bg-slate-700/50 rounded-lg p-4">
          <div className="text-sm text-slate-400 mb-1">Total Investment</div>
          <div className="text-2xl font-bold text-white">{formatCurrency(roiData.totalInvestment)}</div>
        </div>
        <div className="bg-slate-700/50 rounded-lg p-4">
          <div className="text-sm text-slate-400 mb-1">Annual Savings</div>
          <div className="text-2xl font-bold text-green-400">{formatCurrency(roiData.annualSavings)}</div>
        </div>
        <div className="bg-slate-700/50 rounded-lg p-4">
          <div className="text-sm text-slate-400 mb-1">ROI Percentage</div>
          <div className="text-2xl font-bold text-blue-400">{formatPercentage(roiData.roiPercentage)}</div>
        </div>
      </div>

      {showDetails && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="bg-slate-700/50 rounded-lg p-4">
              <div className="text-sm text-slate-400 mb-1">Payback Period</div>
              <div className="text-lg font-semibold text-white">{roiData.paybackPeriod} months</div>
            </div>
            <div className="bg-slate-700/50 rounded-lg p-4">
              <div className="text-sm text-slate-400 mb-1">Net Present Value</div>
              <div className="text-lg font-semibold text-white">{formatCurrency(roiData.npv)}</div>
            </div>
            <div className="bg-slate-700/50 rounded-lg p-4">
              <div className="text-sm text-slate-400 mb-1">Internal Rate of Return</div>
              <div className="text-lg font-semibold text-white">{formatPercentage(roiData.irr)}</div>
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="bg-slate-700/50 rounded-lg p-4">
              <div className="text-sm text-slate-400 mb-1">Break-even Point</div>
              <div className="text-lg font-semibold text-white">{roiData.breakEvenPoint} months</div>
            </div>
            <div className="bg-slate-700/50 rounded-lg p-4">
              <div className="text-sm text-slate-400 mb-1">Sensitivity Analysis</div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-slate-300">Best Case:</span>
                  <span className="text-sm font-semibold text-green-400">{formatPercentage(roiData.sensitivityAnalysis.bestCase)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-slate-300">Base Case:</span>
                  <span className="text-sm font-semibold text-blue-400">{formatPercentage(roiData.sensitivityAnalysis.baseCase)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-slate-300">Worst Case:</span>
                  <span className="text-sm font-semibold text-red-400">{formatPercentage(roiData.sensitivityAnalysis.worstCase)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// ============================================================================
// COST BREAKDOWN COMPONENT
// ============================================================================

interface CostBreakdownProps {
  costBreakdown: ExecutiveDashboardData['costBreakdown'];
}

/**
 * Cost Breakdown Component
 */
const CostBreakdown: React.FC<CostBreakdownProps> = ({ costBreakdown }) => {
  const getTotalCost = () => {
    return costBreakdown.reduce((total, item) => total + item.amount, 0);
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'increasing': return <TrendingUp className="w-4 h-4 text-red-400" />;
      case 'decreasing': return <TrendingDown className="w-4 h-4 text-green-400" />;
      case 'stable': return <Minus className="w-4 h-4 text-yellow-400" />;
      default: return null;
    }
  };

  return (
    <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-xl p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-bold text-white">Cost Breakdown</h3>
          <p className="text-sm text-slate-400">Total: ${getTotalCost().toLocaleString()}</p>
        </div>
        <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-orange-400 rounded-lg flex items-center justify-center">
          <BarChart3 className="w-6 h-6 text-white" />
        </div>
      </div>

      <div className="space-y-4">
        {costBreakdown.map((item, index) => (
          <div key={index} className="bg-slate-700/50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-gradient-to-br from-blue-500 to-cyan-400 rounded-full"></div>
                <span className="font-medium text-white">{item.category}</span>
              </div>
              <div className="flex items-center space-x-2">
                {getTrendIcon(item.trend)}
                <span className="text-sm text-slate-300">{item.trend}</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-lg font-semibold text-white">${item.amount.toLocaleString()}</div>
                <div className="text-sm text-slate-400">{item.description}</div>
              </div>
              <div className="text-right">
                <div className="text-sm text-slate-300">Percentage</div>
                <div className="text-lg font-bold text-blue-400">{item.percentage}%</div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// ============================================================================
// FINANCIAL METRICS MAIN COMPONENT
// ============================================================================

interface FinancialMetricsProps {
  data: ExecutiveDashboardData;
  onExportPDF: () => void;
}

/**
 * Financial Metrics Main Component
 */
const FinancialMetrics: React.FC<FinancialMetricsProps> = ({ data, onExportPDF }) => {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-900/40 to-emerald-900/40 border border-green-500/50 rounded-xl p-6 shadow-2xl">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold text-white mb-2">Financial Metrics</h2>
            <p className="text-green-300 text-lg">
              Comprehensive financial analysis and ROI calculations
            </p>
          </div>
          <button
            onClick={onExportPDF}
            className="bg-green-600 hover:bg-green-700 text-white font-semibold px-4 py-2 rounded-lg transition-colors flex items-center space-x-2"
          >
            <Download className="w-5 h-5" />
            <span>Export PDF</span>
          </button>
        </div>
      </div>

      {/* Financial Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {data.financialMetrics.map((metric) => (
          <FinancialMetricCard key={metric.id} metric={metric} />
        ))}
      </div>

      {/* ROI Analysis */}
      <ROICalculator roiData={data.roiAnalysis} />

      {/* Cost Breakdown */}
      <CostBreakdown costBreakdown={data.costBreakdown} />

      {/* Summary */}
      <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-xl p-6">
        <h3 className="text-xl font-bold text-white mb-4">Financial Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-400">
              ${data.financialMetrics.find(m => m.category === 'savings')?.value.toLocaleString() || '0'}
            </div>
            <div className="text-sm text-slate-400">Total Savings</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400">
              {data.roiAnalysis.roiPercentage}%
            </div>
            <div className="text-sm text-slate-400">ROI Percentage</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-400">
              {data.roiAnalysis.paybackPeriod} months
            </div>
            <div className="text-sm text-slate-400">Payback Period</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-400">
              ${getTotalInfrastructureCost(data.costBreakdown).toLocaleString()}
            </div>
            <div className="text-sm text-slate-400">Infrastructure Cost</div>
          </div>
        </div>
      </div>
    </div>
  );
};

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Get total infrastructure cost
 */
function getTotalInfrastructureCost(costBreakdown: ExecutiveDashboardData['costBreakdown']): number {
  const infrastructureItem = costBreakdown.find(item => item.category === 'Infrastructure');
  return infrastructureItem ? infrastructureItem.amount : 0;
}

export default FinancialMetrics;