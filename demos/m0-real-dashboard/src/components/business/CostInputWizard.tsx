/**
 * ============================================================================
 * OA FRAMEWORK - COST INPUT WIZARD
 * Cost Input Wizard Component
 * ============================================================================
 * 
 * @fileoverview Interactive cost input wizard for ROI calculator
 * @module components/business/CostInputWizard
 * @version 1.0.0
 * @since 2026-02-01
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2026 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

'use client';

import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Stepper, 
  Step, 
  StepLabel, 
  Button, 
  Grid, 
  Card, 
  CardContent, 
  Typography, 
  TextField, 
  Select, 
  MenuItem, 
  FormControl, 
  InputLabel, 
  Chip, 
  Alert, 
  LinearProgress,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Calculator,
  TrendingUp,
  Users,
  Shield,
  School,
  HelpCircle,
  Save,
  RefreshCw
} from 'lucide-react';

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface CostInput {
  infrastructure: number;
  personnel: number;
  software: number;
  compliance: number;
  training: number;
}

interface WizardStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
}

// ============================================================================
// COST INPUT WIZARD COMPONENT
// ============================================================================

interface CostInputWizardProps {
  onCalculate: (costs: CostInput) => void;
  onReset: () => void;
  isLoading?: boolean;
  defaultValues?: Partial<CostInput>;
}

export default function CostInputWizard({ 
  onCalculate, 
  onReset, 
  isLoading = false,
  defaultValues 
}: CostInputWizardProps): JSX.Element {
  // State management
  const [activeStep, setActiveStep] = useState(0);
  const [costs, setCosts] = useState<CostInput>({
    infrastructure: defaultValues?.infrastructure || 500000,
    personnel: defaultValues?.personnel || 2000000,
    software: defaultValues?.software || 300000,
    compliance: defaultValues?.compliance || 150000,
    training: defaultValues?.training || 50000
  });
  const [isValid, setIsValid] = useState(true);
  const [showHelp, setShowHelp] = useState(false);

  // Wizard steps configuration
  const steps: WizardStep[] = [
    {
      id: 'infrastructure',
      title: 'Infrastructure Costs',
      description: 'Cloud hosting, servers, and infrastructure expenses',
      icon: <Calculate className="w-6 h-6" />
    },
    {
      id: 'personnel',
      title: 'Personnel Costs',
      description: 'Development, operations, and support team salaries',
      icon: <People className="w-6 h-6" />
    },
    {
      id: 'software',
      title: 'Software Costs',
      description: 'Licenses, subscriptions, and third-party services',
      icon: <TrendingUp className="w-6 h-6" />
    },
    {
      id: 'compliance',
      title: 'Compliance Costs',
      description: 'Audit, security, and regulatory compliance expenses',
      icon: <Security className="w-6 h-6" />
    },
    {
      id: 'training',
      title: 'Training Costs',
      description: 'Training, documentation, and change management',
      icon: <School className="w-6 h-6" />
    }
  ];

  // Calculate total costs
  const totalCosts = Object.values(costs).reduce((sum, cost) => sum + cost, 0);

  // Handle cost input change
  const handleCostChange = (field: keyof CostInput, value: string) => {
    const numValue = parseFloat(value) || 0;
    setCosts(prev => ({
      ...prev,
      [field]: Math.max(0, numValue) // Ensure non-negative values
    }));
  };

  // Handle step navigation
  const handleNext = () => {
    if (activeStep < steps.length - 1) {
      setActiveStep(prev => prev + 1);
    }
  };

  const handleBack = () => {
    if (activeStep > 0) {
      setActiveStep(prev => prev - 1);
    }
  };

  // Handle calculation
  const handleCalculate = () => {
    if (totalCosts > 0) {
      onCalculate(costs);
    } else {
      setIsValid(false);
    }
  };

  // Handle reset
  const handleReset = () => {
    setCosts({
      infrastructure: 500000,
      personnel: 2000000,
      software: 300000,
      compliance: 150000,
      training: 50000
    });
    setActiveStep(0);
    setIsValid(true);
    onReset();
  };

  // Get current step data
  const currentStep = steps[activeStep];

  // Render cost input field
  const renderCostField = (field: keyof CostInput, label: string, description: string, defaultValue: number) => (
    <Card className="mb-4 border border-slate-200 hover:shadow-lg transition-shadow">
      <CardContent>
        <div className="flex items-center justify-between mb-2">
          <Typography variant="h6" className="font-semibold text-slate-800">
            {label}
          </Typography>
          <Tooltip title={description} arrow>
            <IconButton size="small">
              <HelpCircle className="w-4 h-4 text-slate-500" />
            </IconButton>
          </Tooltip>
        </div>
        <Typography variant="body2" className="text-slate-600 mb-3">
          {description}
        </Typography>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={8}>
            <TextField
              fullWidth
              type="number"
              value={costs[field]}
              onChange={(e) => handleCostChange(field, e.target.value)}
              label="Annual Cost"
              InputProps={{
                startAdornment: <span className="mr-2 text-slate-500">$</span>,
                inputProps: { min: 0, step: 1000 }
              }}
              error={costs[field] < 0}
              helperText={costs[field] < 0 ? "Cost cannot be negative" : ""}
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <Chip
              label={`Current: $${costs[field].toLocaleString()}`}
              color="primary"
              variant="outlined"
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );

  return (
    <Box className="w-full">
      {/* Header */}
      <Box className="mb-6 text-center">
        <div className="flex items-center justify-center mb-2">
          <div className="bg-blue-100 p-3 rounded-full">
            <Calculate className="w-8 h-8 text-blue-600" />
          </div>
        </div>
        <Typography variant="h4" className="font-bold text-slate-900 mb-2">
          Cost Input Wizard
        </Typography>
        <Typography variant="body1" className="text-slate-600">
          Configure your current operational costs to calculate OA Framework ROI
        </Typography>
        <div className="mt-4 flex justify-center space-x-4">
          <Chip 
            label={`Total Annual Costs: $${totalCosts.toLocaleString()}`} 
            color="primary" 
            variant="filled"
          />
          <Chip 
            label={`Step ${activeStep + 1} of ${steps.length}`} 
            color="secondary" 
            variant="outlined"
          />
        </div>
      </Box>

      {/* Progress Bar */}
      <Box className="mb-6">
        <LinearProgress 
          variant="determinate" 
          value={(activeStep / (steps.length - 1)) * 100}
          className="h-2"
        />
        <Typography variant="caption" className="text-slate-500 mt-1 block text-center">
          Progress: {Math.round((activeStep / (steps.length - 1)) * 100)}%
        </Typography>
      </Box>

      {/* Stepper */}
      <Stepper 
        activeStep={activeStep} 
        alternativeLabel 
        className="mb-8"
      >
        {steps.map((step, index) => (
          <Step key={step.id}>
            <StepLabel>
              <div className="flex flex-col items-center">
                <div className={`p-2 rounded-full ${
                  index <= activeStep ? 'bg-blue-100 text-blue-600' : 'bg-slate-100 text-slate-400'
                }`}>
                  {step.icon}
                </div>
                <span className="text-xs mt-1 text-center">{step.title}</span>
              </div>
            </StepLabel>
          </Step>
        ))}
      </Stepper>

      {/* Alert for validation */}
      {!isValid && (
        <Alert 
          severity="warning" 
          className="mb-4"
          onClose={() => setIsValid(true)}
        >
          Please enter valid cost values before calculating ROI.
        </Alert>
      )}

      {/* Current Step Content */}
      <Box className="mb-6">
        <Card className="border border-slate-200">
          <CardContent>
            <div className="flex items-center justify-between mb-4">
              <div>
                <Typography variant="h5" className="font-semibold text-slate-800">
                  {currentStep.title}
                </Typography>
                <Typography variant="body2" className="text-slate-600">
                  {currentStep.description}
                </Typography>
              </div>
              <div className="bg-slate-100 p-3 rounded-lg">
                {currentStep.icon}
              </div>
            </div>

            {/* Step-specific content */}
            {activeStep === 0 && renderCostField(
              'infrastructure',
              'Infrastructure Costs',
              'Cloud hosting, servers, data centers, and infrastructure management',
              500000
            )}
            
            {activeStep === 1 && renderCostField(
              'personnel',
              'Personnel Costs',
              'Development team, operations staff, and technical support salaries',
              2000000
            )}
            
            {activeStep === 2 && renderCostField(
              'software',
              'Software Costs',
              'Software licenses, subscriptions, and third-party service fees',
              300000
            )}
            
            {activeStep === 3 && renderCostField(
              'compliance',
              'Compliance Costs',
              'Security audits, regulatory compliance, and certification expenses',
              150000
            )}
            
            {activeStep === 4 && renderCostField(
              'training',
              'Training Costs',
              'Employee training, documentation, and change management',
              50000
            )}
          </CardContent>
        </Card>
      </Box>

      {/* Summary Card */}
      <Card className="mb-6 border border-slate-200">
        <CardContent>
          <Typography variant="h6" className="font-semibold text-slate-800 mb-3">
            Cost Summary
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={4}>
              <div className="bg-blue-50 p-3 rounded-lg">
                <Typography variant="body2" className="text-slate-600">Infrastructure</Typography>
                <Typography variant="h6" className="font-bold text-blue-600">
                  ${costs.infrastructure.toLocaleString()}
                </Typography>
              </div>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <div className="bg-green-50 p-3 rounded-lg">
                <Typography variant="body2" className="text-slate-600">Personnel</Typography>
                <Typography variant="h6" className="font-bold text-green-600">
                  ${costs.personnel.toLocaleString()}
                </Typography>
              </div>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <div className="bg-purple-50 p-3 rounded-lg">
                <Typography variant="body2" className="text-slate-600">Software</Typography>
                <Typography variant="h6" className="font-bold text-purple-600">
                  ${costs.software.toLocaleString()}
                </Typography>
              </div>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <div className="bg-orange-50 p-3 rounded-lg">
                <Typography variant="body2" className="text-slate-600">Compliance</Typography>
                <Typography variant="h6" className="font-bold text-orange-600">
                  ${costs.compliance.toLocaleString()}
                </Typography>
              </div>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <div className="bg-red-50 p-3 rounded-lg">
                <Typography variant="body2" className="text-slate-600">Training</Typography>
                <Typography variant="h6" className="font-bold text-red-600">
                  ${costs.training.toLocaleString()}
                </Typography>
              </div>
            </Grid>
            <Grid item xs={12} sm={12} md={4}>
              <div className="bg-slate-100 p-3 rounded-lg">
                <Typography variant="body2" className="text-slate-600">Total Annual Costs</Typography>
                <Typography variant="h5" className="font-bold text-slate-800">
                  ${totalCosts.toLocaleString()}
                </Typography>
              </div>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Actions */}
      <Box className="flex justify-between items-center">
        <div className="flex space-x-2">
          <Button
            variant="outlined"
            onClick={handleReset}
            startIcon={<RefreshCw className="w-4 h-4" />}
            disabled={isLoading}
          >
            Reset
          </Button>
          <Button
            variant="outlined"
            onClick={() => setShowHelp(!showHelp)}
            startIcon={<HelpCircle className="w-4 h-4" />}
          >
            {showHelp ? 'Hide Help' : 'Show Help'}
          </Button>
        </div>

        <div className="flex space-x-2">
          <Button
            disabled={activeStep === 0 || isLoading}
            onClick={handleBack}
            variant="outlined"
          >
            Back
          </Button>
          {activeStep < steps.length - 1 ? (
            <Button
              variant="contained"
              onClick={handleNext}
              disabled={isLoading}
            >
              Next
            </Button>
          ) : (
            <Button
              variant="contained"
              color="primary"
              onClick={handleCalculate}
              disabled={isLoading || totalCosts <= 0}
              startIcon={<TrendingUp className="w-4 h-4" />}
            >
              {isLoading ? 'Calculating...' : 'Calculate ROI'}
            </Button>
          )}
        </div>
      </Box>

      {/* Help Section */}
      {showHelp && (
        <Card className="mt-6 border border-slate-200">
          <CardContent>
            <Typography variant="h6" className="font-semibold text-slate-800 mb-3">
              Cost Input Guidelines
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Typography variant="body2" className="text-slate-600 mb-2">
                  <strong>Infrastructure Costs:</strong> Include cloud hosting fees, server costs, data center expenses, and infrastructure management.
                </Typography>
                <Typography variant="body2" className="text-slate-600 mb-2">
                  <strong>Personnel Costs:</strong> Include salaries, benefits, and overhead for development, operations, and support teams.
                </Typography>
                <Typography variant="body2" className="text-slate-600">
                  <strong>Software Costs:</strong> Include license fees, subscription costs, and third-party service charges.
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="body2" className="text-slate-600 mb-2">
                  <strong>Compliance Costs:</strong> Include audit fees, security assessments, regulatory compliance, and certification expenses.
                </Typography>
                <Typography variant="body2" className="text-slate-600">
                  <strong>Training Costs:</strong> Include employee training programs, documentation, and change management expenses.
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}
    </Box>
  );
}