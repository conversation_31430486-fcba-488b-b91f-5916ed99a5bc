/**
 * ============================================================================
 * OA FRAMEWORK - BUSINESS RISK ASSESSMENT COMPONENT
 * Risk Assessment Dashboard Component
 * ============================================================================
 * 
 * @fileoverview Risk assessment component with security, compliance, and operational risk tracking
 * @module components/business/RiskAssessment
 * @version 1.0.0
 * @since 2026-02-01
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2026 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

'use client';

import React, { useState } from 'react';
import { AlertTriangle, Shield, Activity, TrendingUp, TrendingDown, BarChart3 } from 'lucide-react';
import { ExecutiveDashboardData, RiskAssessmentType } from '../../types/business-types';

// ============================================================================
// RISK CARD COMPONENT
// ============================================================================

interface RiskCardProps {
  risk: {
    id: string;
    name: string;
    category: string;
    likelihood: 'low' | 'medium' | 'high';
    impact: 'low' | 'medium' | 'high';
    riskScore: number;
    mitigationStatus: string;
    owner: string;
    dueDate: Date;
    description: string;
    controls: any[];
  };
}

/**
 * Risk Card Component
 */
const RiskCard: React.FC<RiskCardProps> = ({ risk }) => {
  const getRiskColor = (score: number): string => {
    if (score >= 70) return 'from-red-500 to-pink-400';
    if (score >= 40) return 'from-yellow-500 to-orange-400';
    return 'from-green-500 to-emerald-400';
  };

  const getRiskIcon = (category: string) => {
    switch (category) {
      case 'security': return <Shield className="w-5 h-5" />;
      case 'compliance': return <Activity className="w-5 h-5" />;
      case 'operational': return <AlertTriangle className="w-5 h-5" />;
      case 'financial': return <BarChart3 className="w-5 h-5" />;
      default: return <AlertTriangle className="w-5 h-5" />;
    }
  };

  const getLikelihoodColor = (likelihood: string): string => {
    switch (likelihood) {
      case 'high': return 'text-red-400';
      case 'medium': return 'text-yellow-400';
      case 'low': return 'text-green-400';
      default: return 'text-gray-400';
    }
  };

  const getImpactColor = (impact: string): string => {
    switch (impact) {
      case 'high': return 'text-red-400';
      case 'medium': return 'text-yellow-400';
      case 'low': return 'text-green-400';
      default: return 'text-gray-400';
    }
  };

  const getMitigationStatusColor = (status: string): string => {
    switch (status) {
      case 'completed': return 'text-green-400';
      case 'in_progress': return 'text-yellow-400';
      case 'not_started': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-xl p-6 hover:shadow-xl transition-all duration-300 hover:border-slate-600/50">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className={`w-10 h-10 bg-gradient-to-br ${getRiskColor(risk.riskScore)} rounded-lg flex items-center justify-center`}>
            {getRiskIcon(risk.category)}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white">{risk.name}</h3>
            <p className="text-sm text-slate-400 capitalize">{risk.category} risk</p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-white">{risk.riskScore}</div>
          <div className="text-xs text-slate-400">Risk Score</div>
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <div className="text-sm text-slate-400 mb-1">Likelihood</div>
          <div className={`font-semibold ${getLikelihoodColor(risk.likelihood)}`}>
            {risk.likelihood.toUpperCase()}
          </div>
        </div>
        <div>
          <div className="text-sm text-slate-400 mb-1">Impact</div>
          <div className={`font-semibold ${getImpactColor(risk.impact)}`}>
            {risk.impact.toUpperCase()}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <div className="text-sm text-slate-400 mb-1">Mitigation Status</div>
          <div className={`font-semibold ${getMitigationStatusColor(risk.mitigationStatus)}`}>
            {risk.mitigationStatus.replace('_', ' ').toUpperCase()}
          </div>
        </div>
        <div>
          <div className="text-sm text-slate-400 mb-1">Owner</div>
          <div className="font-semibold text-white">{risk.owner}</div>
        </div>
      </div>

      <div className="text-sm text-slate-400 mb-4">{risk.description}</div>

      <div className="flex items-center justify-between">
        <div className="text-xs text-slate-400">
          Due: {risk.dueDate.toLocaleDateString()}
        </div>
        <div className="text-xs text-slate-400">
          Controls: {risk.controls.length}
        </div>
      </div>
    </div>
  );
};

// ============================================================================
// RISK TREND CHART COMPONENT
// ============================================================================

interface RiskTrendChartProps {
  trends: ExecutiveDashboardData['riskTrends'];
}

/**
 * Risk Trend Chart Component
 */
const RiskTrendChart: React.FC<RiskTrendChartProps> = ({ trends }) => {
  const latestTrend = trends[trends.length - 1];
  
  return (
    <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-xl p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-bold text-white">Risk Trends</h3>
          <p className="text-sm text-slate-400">Last 30 days risk assessment</p>
        </div>
        <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-pink-400 rounded-lg flex items-center justify-center">
          <TrendingUp className="w-6 h-6 text-white" />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-slate-700/50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-white">{latestTrend.totalRiskScore}</div>
          <div className="text-sm text-slate-400">Total Risk Score</div>
        </div>
        <div className="bg-slate-700/50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-red-400">{latestTrend.highRiskCount}</div>
          <div className="text-sm text-slate-400">High Risk</div>
        </div>
        <div className="bg-slate-700/50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-yellow-400">{latestTrend.mediumRiskCount}</div>
          <div className="text-sm text-slate-400">Medium Risk</div>
        </div>
        <div className="bg-slate-700/50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-green-400">{latestTrend.lowRiskCount}</div>
          <div className="text-sm text-slate-400">Low Risk</div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-slate-700/50 rounded-lg p-4">
          <div className="text-sm text-slate-400 mb-2">Mitigated Risks</div>
          <div className="text-lg font-semibold text-green-400">{latestTrend.mitigatedCount}</div>
        </div>
        <div className="bg-slate-700/50 rounded-lg p-4">
          <div className="text-sm text-slate-400 mb-2">Trend Direction</div>
          <div className="flex items-center space-x-2">
            <TrendingDown className="w-5 h-5 text-green-400" />
            <span className="text-lg font-semibold text-green-400">Improving</span>
          </div>
        </div>
      </div>
    </div>
  );
};

// ============================================================================
// RISK ASSESSMENT MAIN COMPONENT
// ============================================================================

interface RiskAssessmentProps {
  data: ExecutiveDashboardData;
}

/**
 * Risk Assessment Main Component
 */
const RiskAssessment: React.FC<RiskAssessmentProps> = ({ data }) => {
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  const filteredRisks = data.riskAssessment.filter(risk => {
    const categoryMatch = filterCategory === 'all' || risk.category === filterCategory;
    const statusMatch = filterStatus === 'all' || risk.mitigationStatus === filterStatus;
    return categoryMatch && statusMatch;
  });

  const getRiskSummary = () => {
    const highRisks = data.riskAssessment.filter(r => r.riskScore >= 70).length;
    const mediumRisks = data.riskAssessment.filter(r => r.riskScore >= 40 && r.riskScore < 70).length;
    const lowRisks = data.riskAssessment.filter(r => r.riskScore < 40).length;
    
    return { highRisks, mediumRisks, lowRisks };
  };

  const summary = getRiskSummary();

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-red-900/40 to-pink-900/40 border border-red-500/50 rounded-xl p-6 shadow-2xl">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold text-white mb-2">Risk Assessment</h2>
            <p className="text-red-300 text-lg">
              Comprehensive risk monitoring and mitigation tracking
            </p>
          </div>
          <div className="text-right">
            <div className="text-sm text-slate-300 mb-1">Risk Categories</div>
            <div className="text-lg font-semibold text-white">
              {data.riskAssessment.length} total risks
            </div>
          </div>
        </div>
      </div>

      {/* Risk Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gradient-to-br from-red-800/50 to-pink-800/50 border border-red-500/50 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-red-400">{summary.highRisks}</div>
              <div className="text-sm text-slate-400">High Risk</div>
            </div>
            <div className="w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center">
              <AlertTriangle className="w-6 h-6 text-red-400" />
            </div>
          </div>
        </div>
        <div className="bg-gradient-to-br from-yellow-800/50 to-orange-800/50 border border-yellow-500/50 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-yellow-400">{summary.mediumRisks}</div>
              <div className="text-sm text-slate-400">Medium Risk</div>
            </div>
            <div className="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
              <Activity className="w-6 h-6 text-yellow-400" />
            </div>
          </div>
        </div>
        <div className="bg-gradient-to-br from-green-800/50 to-emerald-800/50 border border-green-500/50 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-green-400">{summary.lowRisks}</div>
              <div className="text-sm text-slate-400">Low Risk</div>
            </div>
            <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
              <Shield className="w-6 h-6 text-green-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-xl p-6">
        <div className="flex flex-wrap gap-4">
          <div>
            <label className="text-sm text-slate-400 mb-2 block">Filter by Category</label>
            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
              className="bg-slate-700 text-white px-4 py-2 rounded-lg border border-slate-600"
            >
              <option value="all">All Categories</option>
              <option value="security">Security</option>
              <option value="compliance">Compliance</option>
              <option value="operational">Operational</option>
              <option value="financial">Financial</option>
            </select>
          </div>
          <div>
            <label className="text-sm text-slate-400 mb-2 block">Filter by Status</label>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="bg-slate-700 text-white px-4 py-2 rounded-lg border border-slate-600"
            >
              <option value="all">All Statuses</option>
              <option value="completed">Completed</option>
              <option value="in_progress">In Progress</option>
              <option value="not_started">Not Started</option>
            </select>
          </div>
        </div>
      </div>

      {/* Risk Trends */}
      <RiskTrendChart trends={data.riskTrends} />

      {/* Risk Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredRisks.map((risk) => (
          <RiskCard key={risk.id} risk={risk} />
        ))}
      </div>

      {/* Summary */}
      <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-xl p-6">
        <h3 className="text-xl font-bold text-white mb-4">Risk Management Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="bg-slate-700/50 rounded-lg p-4">
              <div className="text-sm text-slate-400 mb-1">Total Risks Identified</div>
              <div className="text-lg font-semibold text-white">{data.riskAssessment.length}</div>
            </div>
            <div className="bg-slate-700/50 rounded-lg p-4">
              <div className="text-sm text-slate-400 mb-1">Average Risk Score</div>
              <div className="text-lg font-semibold text-white">
                {Math.round(data.riskAssessment.reduce((sum, r) => sum + r.riskScore, 0) / data.riskAssessment.length)}
              </div>
            </div>
          </div>
          <div className="space-y-4">
            <div className="bg-slate-700/50 rounded-lg p-4">
              <div className="text-sm text-slate-400 mb-1">Mitigation Progress</div>
              <div className="text-lg font-semibold text-green-400">
                {Math.round((data.riskAssessment.filter(r => r.mitigationStatus === 'completed').length / data.riskAssessment.length) * 100)}%
              </div>
            </div>
            <div className="bg-slate-700/50 rounded-lg p-4">
              <div className="text-sm text-slate-400 mb-1">Critical Risks</div>
              <div className="text-lg font-semibold text-red-400">{summary.highRisks}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RiskAssessment;