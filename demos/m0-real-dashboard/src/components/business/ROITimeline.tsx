/**
 * ============================================================================
 * OA FRAMEWORK - ROI TIMELINE
 * ROI Timeline Component
 * ============================================================================
 * 
 * @fileoverview ROI timeline visualization component for ROI calculator
 * @module components/business/ROITimeline
 * @version 1.0.0
 * @since 2026-02-01
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2026 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

'use client';

import React from 'react';
import { 
  Box, 
  Card, 
  CardContent, 
  Typography, 
  Grid, 
  Chip, 
  LinearProgress, 
  Button, 
  Select, 
  MenuItem, 
  FormControl, 
  InputLabel, 
  Alert, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow, 
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  TrendingUp,
  Calendar,
  DollarSign,
  BarChart3,
  Target,
  RefreshCw,
  ExpandMore,
  Download
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, ReferenceLine } from 'recharts';

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface ROITimelineData {
  month: number;
  monthLabel: string;
  cumulativeCashFlow: number;
  monthlySavings: number;
  monthlyInvestment: number;
  netCashFlow: number;
}

interface ROITimelineProps {
  roiCalculation: any; // ROICalculation type
  onExport?: () => void;
  isLoading?: boolean;
}

// ============================================================================
// ROI TIMELINE COMPONENT
// ============================================================================

export default function ROITimeline({ 
  roiCalculation, 
  onExport, 
  isLoading = false 
}: ROITimelineProps): JSX.Element {
  // Generate timeline data
  const timelineData = generateTimelineData(roiCalculation);
  
  // Get key metrics
  const breakEvenMonth = roiCalculation?.breakEvenPoint || 0;
  const totalInvestment = roiCalculation?.totalInvestment || 0;
  const annualSavings = roiCalculation?.annualSavings || 0;
  const roiPercentage = roiCalculation?.roiPercentage || 0;
  const npv = roiCalculation?.npv || 0;
  const irr = roiCalculation?.irr || 0;

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(value);
  };

  // Get month label
  const getMonthLabel = (month: number): string => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return months[(month - 1) % 12];
  };

  // Get investment phase
  const getInvestmentPhase = (month: number, totalMonths: number): string => {
    const phase1End = Math.floor(totalMonths * 0.15);
    const phase2End = Math.floor(totalMonths * 0.70);
    const phase3End = Math.floor(totalMonths * 0.90);

    if (month <= phase1End) return 'Planning';
    if (month <= phase2End) return 'Implementation';
    if (month <= phase3End) return 'Integration';
    return 'Deployment';
  };

  return (
    <Box className="w-full">
      {/* Header */}
      <Box className="mb-6 text-center">
        <div className="flex items-center justify-center mb-2">
          <div className="bg-blue-100 p-3 rounded-full">
            <Calendar className="w-8 h-8 text-blue-600" />
          </div>
        </div>
        <Typography variant="h4" className="font-bold text-slate-900 mb-2">
          ROI Timeline Analysis
        </Typography>
        <Typography variant="body1" className="text-slate-600">
          Implementation timeline and financial projections over time
        </Typography>
        <div className="mt-4 flex justify-center space-x-4">
          <Chip 
            label={`Break-even Point: Month ${breakEvenMonth}`} 
            color="success" 
            variant="filled"
          />
          <Chip 
            label={`Total Investment: ${formatCurrency(totalInvestment)}`} 
            color="primary" 
            variant="outlined"
          />
          <Chip 
            label={`ROI: ${roiPercentage.toFixed(1)}%`} 
            color="secondary" 
            variant="outlined"
          />
        </div>
      </Box>

      {/* Key Metrics Overview */}
      <Grid container spacing={3} className="mb-6">
        <Grid item xs={12} sm={6} md={3}>
          <Card className="border border-slate-200">
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <Typography variant="body2" className="text-slate-600">Total Investment</Typography>
                  <Typography variant="h5" className="font-bold text-slate-800">
                    {formatCurrency(totalInvestment)}
                  </Typography>
                </div>
                <div className="bg-blue-100 p-2 rounded-lg">
                  <DollarSign className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card className="border border-slate-200">
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <Typography variant="body2" className="text-slate-600">Annual Savings</Typography>
                  <Typography variant="h5" className="font-bold text-green-600">
                    {formatCurrency(annualSavings)}
                  </Typography>
                </div>
                <div className="bg-green-100 p-2 rounded-lg">
                  <TrendingUp className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card className="border border-slate-200">
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <Typography variant="body2" className="text-slate-600">Net Present Value</Typography>
                  <Typography variant="h5" className="font-bold text-purple-600">
                    {formatCurrency(npv)}
                  </Typography>
                </div>
                <div className="bg-purple-100 p-2 rounded-lg">
                  <BarChart3 className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card className="border border-slate-200">
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <Typography variant="body2" className="text-slate-600">Internal Rate of Return</Typography>
                  <Typography variant="h5" className="font-bold text-orange-600">
                    {irr.toFixed(1)}%
                  </Typography>
                </div>
                <div className="bg-orange-100 p-2 rounded-lg">
                  <Target className="w-6 h-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* ROI Chart */}
      <Card className="mb-6 border border-slate-200">
        <CardContent>
          <Typography variant="h6" className="font-semibold text-slate-800 mb-3">
            Cumulative Cash Flow Timeline
          </Typography>
          <div style={{ width: '100%', height: 400 }}>
            <ResponsiveContainer>
              <LineChart data={timelineData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="monthLabel" 
                  tick={{ fontSize: 12 }}
                  label={{ value: "Month", position: "insideBottom", offset: -5 }}
                />
                <YAxis 
                  tickFormatter={(value) => `$${(value / 1000000).toFixed(0)}M`}
                  label={{ value: "Cumulative Cash Flow", angle: -90, position: "insideLeft" }}
                />
                <Tooltip 
                  formatter={(value) => [formatCurrency(value as number), 'Value']}
                  labelFormatter={(label) => `Month ${timelineData.find(d => d.monthLabel === label)?.month}`}
                />
                <Legend />
                <ReferenceLine 
                  y={0} 
                  stroke="#64748b" 
                  strokeDasharray="5 5" 
                  label="Break-even Line" 
                />
                <ReferenceLine 
                  x={breakEvenMonth} 
                  stroke="#10b981" 
                  strokeDasharray="5 5" 
                  label={`Break-even: Month ${breakEvenMonth}`} 
                />
                <Line 
                  type="monotone" 
                  dataKey="cumulativeCashFlow" 
                  stroke="#3b82f6" 
                  strokeWidth={3}
                  dot={false}
                  name="Cumulative Cash Flow"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Monthly Breakdown Table */}
      <Card className="mb-6 border border-slate-200">
        <CardContent>
          <Typography variant="h6" className="font-semibold text-slate-800 mb-3">
            Monthly Cash Flow Breakdown
          </Typography>
          <TableContainer component={Paper} className="border border-slate-200">
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Month</TableCell>
                  <TableCell align="right">Investment</TableCell>
                  <TableCell align="right">Savings</TableCell>
                  <TableCell align="right">Net Flow</TableCell>
                  <TableCell align="right">Cumulative</TableCell>
                  <TableCell>Phase</TableCell>
                  <TableCell>Status</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {timelineData.map((row, index) => (
                  <TableRow key={index}>
                    <TableCell component="th" scope="row">
                      {row.monthLabel} (M{row.month})
                    </TableCell>
                    <TableCell align="right" className="text-red-600">
                      -{formatCurrency(row.monthlyInvestment)}
                    </TableCell>
                    <TableCell align="right" className="text-green-600">
                      +{formatCurrency(row.monthlySavings)}
                    </TableCell>
                    <TableCell align="right" className={row.netCashFlow >= 0 ? 'text-green-600 font-semibold' : 'text-red-600 font-semibold'}>
                      {row.netCashFlow >= 0 ? '+' : ''}{formatCurrency(row.netCashFlow)}
                    </TableCell>
                    <TableCell align="right" className="font-semibold">
                      {formatCurrency(row.cumulativeCashFlow)}
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={getInvestmentPhase(row.month, roiCalculation?.implementationPeriod || 12)}
                        size="small"
                        color={row.month <= breakEvenMonth ? "primary" : "success"}
                      />
                    </TableCell>
                    <TableCell>
                      {row.cumulativeCashFlow >= 0 ? (
                        <Chip label="Profitable" color="success" size="small" />
                      ) : (
                        <Chip label="Investment" color="warning" size="small" />
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Sensitivity Analysis */}
      <Accordion className="mb-6 border border-slate-200">
        <AccordionSummary
          expandIcon={<ExpandMore className="text-slate-600" />}
          aria-controls="sensitivity-content"
          id="sensitivity-header"
        >
          <Typography variant="h6" className="font-semibold text-slate-800">
            <BarChart3 className="w-5 h-5 inline mr-2" />
            Sensitivity Analysis
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Card className="border border-slate-200">
                <CardContent>
                  <Typography variant="body2" className="text-slate-600 mb-2 font-semibold">Best Case Scenario</Typography>
                  <Typography variant="h6" className="text-green-600 font-bold">
                    {roiCalculation?.sensitivityAnalysis?.bestCase?.toFixed(1) || 'N/A'}%
                  </Typography>
                  <Typography variant="body2" className="text-slate-500">
                    20% lower investment, 25% higher savings
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={4}>
              <Card className="border border-slate-200">
                <CardContent>
                  <Typography variant="body2" className="text-slate-600 mb-2 font-semibold">Base Case Scenario</Typography>
                  <Typography variant="h6" className="text-blue-600 font-bold">
                    {roiCalculation?.sensitivityAnalysis?.baseCase?.toFixed(1) || 'N/A'}%
                  </Typography>
                  <Typography variant="body2" className="text-slate-500">
                    Expected investment and savings
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={4}>
              <Card className="border border-slate-200">
                <CardContent>
                  <Typography variant="body2" className="text-slate-600 mb-2 font-semibold">Worst Case Scenario</Typography>
                  <Typography variant="h6" className="text-red-600 font-bold">
                    {roiCalculation?.sensitivityAnalysis?.worstCase?.toFixed(1) || 'N/A'}%
                  </Typography>
                  <Typography variant="body2" className="text-slate-500">
                    20% higher investment, 25% lower savings
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>

      {/* Implementation Phases */}
      <Card className="mb-6 border border-slate-200">
        <CardContent>
          <Typography variant="h6" className="font-semibold text-slate-800 mb-3">
            Implementation Timeline
          </Typography>
          <Grid container spacing={2}>
            {roiCalculation?.implementationTimeline?.map((phase: any, index: number) => (
              <Grid item xs={12} md={6} lg={3} key={index}>
                <Card className="border border-slate-200 h-full">
                  <CardContent>
                    <Typography variant="body2" className="text-slate-600 mb-1 font-semibold">
                      {phase.phase}
                    </Typography>
                    <Typography variant="body2" className="text-slate-500 mb-2">
                      Duration: {phase.duration} months
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={(phase.duration / roiCalculation.implementationPeriod) * 100}
                      className="mb-2"
                    />
                    <Typography variant="body2" className="text-slate-600">
                      Investment: {formatCurrency(phase.investment)}
                    </Typography>
                    <div className="mt-2">
                      {phase.milestones.map((milestone: string, idx: number) => (
                        <Typography key={idx} variant="caption" className="text-slate-500 block">
                          • {milestone}
                        </Typography>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* Actions */}
      <Box className="flex justify-end space-x-2">
        <Button
          variant="outlined"
          onClick={onExport}
          startIcon={<Download className="w-4 h-4" />}
          disabled={isLoading}
        >
          Export Report
        </Button>
        <Button
          variant="contained"
          color="primary"
          startIcon={<RefreshCw className="w-4 h-4" />}
          disabled={isLoading}
        >
          {isLoading ? 'Updating...' : 'Update Timeline'}
        </Button>
      </Box>
    </Box>
  );
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Generate timeline data for the ROI chart
 */
function generateTimelineData(roiCalculation: any): ROITimelineData[] {
  const monthlyCashFlow = roiCalculation?.monthlyCashFlow || [];
  const implementationPeriod = roiCalculation?.implementationPeriod || 12;
  const monthlySavings = (roiCalculation?.annualSavings || 0) / 12;
  const monthlyInvestment = (roiCalculation?.totalInvestment || 0) / implementationPeriod;

  const data: ROITimelineData[] = [];

  for (let month = 1; month <= implementationPeriod; month++) {
    const cumulativeCashFlow = monthlyCashFlow[month - 1] || 0;
    
    data.push({
      month: month,
      monthLabel: getMonthLabel(month),
      cumulativeCashFlow: cumulativeCashFlow,
      monthlySavings: monthlySavings,
      monthlyInvestment: monthlyInvestment,
      netCashFlow: monthlySavings - monthlyInvestment
    });
  }

  return data;
}

/**
 * Get month label for chart
 */
function getMonthLabel(month: number): string {
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  return months[(month - 1) % 12];
}