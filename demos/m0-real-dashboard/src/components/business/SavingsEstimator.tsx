/**
 * ============================================================================
 * OA FRAMEWORK - SAVINGS ESTIMATOR
 * Savings Estimator Component
 * ============================================================================
 * 
 * @fileoverview Savings estimation component for ROI calculator
 * @module components/business/SavingsEstimator
 * @version 1.0.0
 * @since 2026-02-01
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2026 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

'use client';

import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Card, 
  CardContent, 
  Typography, 
  Grid, 
  Chip, 
  LinearProgress, 
  Button, 
  Slider, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  Alert, 
  Accordion, 
  AccordionSummary, 
  AccordionDetails,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from '@mui/material';
import {
  TrendingUp,
  BarChart3,
  Users,
  Shield,
  Settings,
  DollarSign,
  Calculator,
  ExpandMore,
  RefreshCw
} from 'lucide-react';

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface SavingsCategory {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  baseSavings: number;
  multiplier: number;
  minSavings: number;
  maxSavings: number;
  confidence: 'high' | 'medium' | 'low';
}

interface SavingsEstimation {
  category: string;
  estimatedSavings: number;
  confidence: string;
  description: string;
}

interface SavingsEstimatorProps {
  currentCosts: number;
  onSavingsUpdate: (savings: number) => void;
  isLoading?: boolean;
  defaultValues?: Partial<Record<string, number>>;
}

// ============================================================================
// SAVINGS ESTIMATOR COMPONENT
// ============================================================================

export default function SavingsEstimator({ 
  currentCosts, 
  onSavingsUpdate, 
  isLoading = false,
  defaultValues 
}: SavingsEstimatorProps): JSX.Element {
  // State management
  const [savingsMultipliers, setSavingsMultipliers] = useState<Record<string, number>>({
    performance: defaultValues?.performance || 0.15,
    automation: defaultValues?.automation || 0.25,
    riskReduction: defaultValues?.riskReduction || 0.10,
    efficiency: defaultValues?.efficiency || 0.20,
    scalability: defaultValues?.scalability || 0.15
  });

  const [totalSavings, setTotalSavings] = useState(0);
  const [savingsBreakdown, setSavingsBreakdown] = useState<SavingsEstimation[]>([]);

  // Savings categories configuration
  const savingsCategories: SavingsCategory[] = [
    {
      id: 'performance',
      name: 'Performance Optimization',
      description: 'Improved system performance and response times',
      icon: <BarChart3 className="w-6 h-6" />,
      baseSavings: 0.10,
      multiplier: 1.5,
      minSavings: 0.05,
      maxSavings: 0.25,
      confidence: 'high'
    },
    {
      id: 'automation',
      name: 'Process Automation',
      description: 'Automated workflows and reduced manual intervention',
      icon: <Settings className="w-6 h-6" />,
      baseSavings: 0.20,
      multiplier: 1.2,
      minSavings: 0.10,
      maxSavings: 0.40,
      confidence: 'high'
    },
    {
      id: 'riskReduction',
      name: 'Risk Reduction',
      description: 'Reduced security risks and compliance violations',
      icon: <Shield className="w-6 h-6" />,
      baseSavings: 0.05,
      multiplier: 2.0,
      minSavings: 0.02,
      maxSavings: 0.15,
      confidence: 'medium'
    },
    {
      id: 'efficiency',
      name: 'Operational Efficiency',
      description: 'Improved resource utilization and operational efficiency',
      icon: <Users className="w-6 h-6" />,
      baseSavings: 0.15,
      multiplier: 1.3,
      minSavings: 0.08,
      maxSavings: 0.30,
      confidence: 'high'
    },
    {
      id: 'scalability',
      name: 'Scalability Benefits',
      description: 'Better scalability and future growth capabilities',
      icon: <TrendingUp className="w-6 h-6" />,
      baseSavings: 0.08,
      multiplier: 1.4,
      minSavings: 0.03,
      maxSavings: 0.20,
      confidence: 'medium'
    }
  ];

  // Calculate savings when multipliers change
  useEffect(() => {
    const breakdown = savingsCategories.map(category => {
      const savingsRate = Math.min(
        category.maxSavings,
        Math.max(category.minSavings, category.baseSavings * savingsMultipliers[category.id])
      );
      
      const estimatedSavings = currentCosts * savingsRate;
      
      return {
        category: category.name,
        estimatedSavings: Math.round(estimatedSavings),
        confidence: category.confidence,
        description: category.description
      };
    });

    const total = breakdown.reduce((sum, item) => sum + item.estimatedSavings, 0);
    
    setSavingsBreakdown(breakdown);
    setTotalSavings(total);
    onSavingsUpdate(total);
  }, [savingsMultipliers, currentCosts, onSavingsUpdate]);

  // Handle slider change
  const handleSliderChange = (category: string, value: number) => {
    setSavingsMultipliers(prev => ({
      ...prev,
      [category]: value
    }));
  };

  // Get confidence color
  const getConfidenceColor = (confidence: string) => {
    switch (confidence) {
      case 'high': return 'success';
      case 'medium': return 'warning';
      case 'low': return 'error';
      default: return 'default';
    }
  };

  // Get confidence label
  const getConfidenceLabel = (confidence: string) => {
    switch (confidence) {
      case 'high': return 'High Confidence';
      case 'medium': return 'Medium Confidence';
      case 'low': return 'Low Confidence';
      default: return 'Unknown';
    }
  };

  // Reset to default values
  const handleReset = () => {
    setSavingsMultipliers({
      performance: 0.15,
      automation: 0.25,
      riskReduction: 0.10,
      efficiency: 0.20,
      scalability: 0.15
    });
  };

  return (
    <Box className="w-full">
      {/* Header */}
      <Box className="mb-6 text-center">
        <div className="flex items-center justify-center mb-2">
          <div className="bg-green-100 p-3 rounded-full">
            <TrendingUp className="w-8 h-8 text-green-600" />
          </div>
        </div>
        <Typography variant="h4" className="font-bold text-slate-900 mb-2">
          Savings Estimation
        </Typography>
        <Typography variant="body1" className="text-slate-600">
          Configure expected savings from OA Framework implementation
        </Typography>
        <div className="mt-4 flex justify-center space-x-4">
          <Chip 
            label={`Total Estimated Savings: $${totalSavings.toLocaleString()}`} 
            color="success" 
            variant="filled"
          />
          <Chip 
            label={`Savings Rate: ${((totalSavings / currentCosts) * 100).toFixed(1)}%`} 
            color="primary" 
            variant="outlined"
          />
        </div>
      </Box>

      {/* Alert for zero costs */}
      {currentCosts <= 0 && (
        <Alert severity="info" className="mb-4">
          Please configure your current operational costs in the Cost Input Wizard first.
        </Alert>
      )}

      {/* Savings Categories */}
      <Grid container spacing={3}>
        {savingsCategories.map((category, index) => {
          const currentMultiplier = savingsMultipliers[category.id];
          const savingsRate = Math.min(
            category.maxSavings,
            Math.max(category.minSavings, category.baseSavings * currentMultiplier)
          );
          const estimatedSavings = currentCosts * savingsRate;
          const percentage = (estimatedSavings / currentCosts) * 100;

          return (
            <Grid item xs={12} md={6} lg={4} key={category.id}>
              <Card className="h-full border border-slate-200 hover:shadow-lg transition-shadow">
                <CardContent>
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="bg-slate-100 p-2 rounded-lg">
                        {category.icon}
                      </div>
                      <div>
                        <Typography variant="h6" className="font-semibold text-slate-800">
                          {category.name}
                        </Typography>
                        <Typography variant="body2" className="text-slate-600">
                          {category.description}
                        </Typography>
                      </div>
                    </div>
                    <Chip
                      label={getConfidenceLabel(category.confidence)}
                      color={getConfidenceColor(category.confidence) as any}
                      size="small"
                    />
                  </div>

                  {/* Savings Amount */}
                  <div className="mb-3">
                    <Typography variant="body2" className="text-slate-600 mb-1">
                      Estimated Annual Savings
                    </Typography>
                    <Typography variant="h5" className="font-bold text-green-600">
                      ${Math.round(estimatedSavings).toLocaleString()}
                    </Typography>
                    <Typography variant="body2" className="text-slate-500">
                      {percentage.toFixed(1)}% of current costs
                    </Typography>
                  </div>

                  {/* Progress Bar */}
                  <LinearProgress 
                    variant="determinate" 
                    value={(percentage / category.maxSavings) * 100}
                    className="mb-2"
                    color={getConfidenceColor(category.confidence) as any}
                  />

                  {/* Slider Control */}
                  <FormControl fullWidth size="small" className="mt-3">
                    <Typography variant="caption" className="text-slate-600 mb-1">
                      Impact Level: {Math.round(currentMultiplier * 100)}%
                    </Typography>
                    <Slider
                      value={currentMultiplier}
                      min={0}
                      max={2}
                      step={0.01}
                      onChange={(_, value) => handleSliderChange(category.id, value as number)}
                      disabled={isLoading || currentCosts <= 0}
                      valueLabelDisplay="auto"
                      valueLabelFormat={(value) => `${Math.round(value * 100)}%`}
                    />
                    <Typography variant="caption" className="text-slate-500">
                      Range: {category.minSavings * 100}% - {category.maxSavings * 100}%
                    </Typography>
                  </FormControl>
                </CardContent>
              </Card>
            </Grid>
          );
        })}
      </Grid>

      {/* Detailed Breakdown */}
      <Card className="mt-6 border border-slate-200">
        <CardContent>
          <Typography variant="h6" className="font-semibold text-slate-800 mb-3">
            Detailed Savings Breakdown
          </Typography>
          
          <TableContainer component={Paper} className="border border-slate-200">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Category</TableCell>
                  <TableCell align="right">Estimated Savings</TableCell>
                  <TableCell align="right">Percentage</TableCell>
                  <TableCell>Confidence</TableCell>
                  <TableCell>Description</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {savingsBreakdown.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell component="th" scope="row">
                      {item.category}
                    </TableCell>
                    <TableCell align="right" className="font-semibold text-green-600">
                      ${item.estimatedSavings.toLocaleString()}
                    </TableCell>
                    <TableCell align="right">
                      {((item.estimatedSavings / currentCosts) * 100).toFixed(1)}%
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getConfidenceLabel(item.confidence)}
                        color={getConfidenceColor(item.confidence) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" className="text-slate-600">
                        {item.description}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
                <TableRow>
                  <TableCell colSpan={1} className="font-semibold">
                    Total Estimated Savings
                  </TableCell>
                  <TableCell align="right" className="font-bold text-green-600">
                    ${totalSavings.toLocaleString()}
                  </TableCell>
                  <TableCell align="right" className="font-semibold">
                    {((totalSavings / currentCosts) * 100).toFixed(1)}%
                  </TableCell>
                  <TableCell colSpan={2}></TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Assumptions and Methodology */}
      <Accordion className="mt-4 border border-slate-200">
        <AccordionSummary
          expandIcon={<ExpandMore className="text-slate-600" />}
          aria-controls="assumptions-content"
          id="assumptions-header"
        >
          <Typography variant="h6" className="font-semibold text-slate-800">
            <Calculator className="w-5 h-5 inline mr-2" />
            Savings Calculation Methodology
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="body2" className="text-slate-600 mb-2">
                <strong>Performance Optimization:</strong> Based on improved system efficiency, reduced response times, and better resource utilization.
              </Typography>
              <Typography variant="body2" className="text-slate-600 mb-2">
                <strong>Process Automation:</strong> Calculated from reduced manual labor, automated workflows, and elimination of repetitive tasks.
              </Typography>
              <Typography variant="body2" className="text-slate-600">
                <strong>Risk Reduction:</strong> Estimated savings from avoided security incidents, compliance violations, and operational disruptions.
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="body2" className="text-slate-600 mb-2">
                <strong>Operational Efficiency:</strong> Derived from improved processes, better resource allocation, and streamlined operations.
              </Typography>
              <Typography variant="body2" className="text-slate-600">
                <strong>Scalability Benefits:</strong> Future cost avoidance through better scalability and reduced infrastructure growth costs.
              </Typography>
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>

      {/* Actions */}
      <Box className="mt-4 flex justify-end space-x-2">
        <Button
          variant="outlined"
          onClick={handleReset}
          startIcon={<RefreshCw className="w-4 h-4" />}
          disabled={isLoading}
        >
          Reset to Defaults
        </Button>
        <Button
          variant="contained"
          color="success"
          disabled={isLoading || currentCosts <= 0}
          startIcon={<DollarSign className="w-4 h-4" />}
        >
          {isLoading ? 'Calculating...' : 'Apply Savings'}
        </Button>
      </Box>
    </Box>
  );
}