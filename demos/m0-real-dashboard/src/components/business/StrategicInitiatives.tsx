/**
 * ============================================================================
 * OA FRAMEWORK - BUSINESS STRATEGIC INITIATIVES COMPONENT
 * Strategic Initiatives Dashboard Component
 * ============================================================================
 * 
 * @fileoverview Strategic initiatives component with project tracking and milestone management
 * @module components/business/StrategicInitiatives
 * @version 1.0.0
 * @since 2026-02-01
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2026 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

'use client';

import React, { useState } from 'react';
import { Target, Calendar, DollarSign, Users, TrendingUp, Clock, CheckCircle } from 'lucide-react';
import { ExecutiveDashboardData } from '../../types/business-types';

// ============================================================================
// INITIATIVE CARD COMPONENT
// ============================================================================

interface InitiativeCardProps {
  initiative: {
    id: string;
    name: string;
    description: string;
    status: string;
    startDate: Date;
    endDate: Date;
    progress: number;
    budget: number;
    spent: number;
    owner: string;
    milestones: any[];
    benefits: any[];
  };
}

/**
 * Initiative Card Component
 */
const InitiativeCard: React.FC<InitiativeCardProps> = ({ initiative }) => {
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'completed': return 'text-green-400';
      case 'in_progress': return 'text-yellow-400';
      case 'planning': return 'text-blue-400';
      case 'on_hold': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'in_progress': return <Clock className="w-5 h-5 text-yellow-400" />;
      case 'planning': return <Calendar className="w-5 h-5 text-blue-400" />;
      case 'on_hold': return <Clock className="w-5 h-5 text-red-400" />;
      default: return <Target className="w-5 h-5 text-gray-400" />;
    }
  };

  const getBudgetPercentage = () => {
    return Math.round((initiative.spent / initiative.budget) * 100);
  };

  const getProgressColor = () => {
    if (initiative.progress >= 90) return 'from-green-500 to-emerald-400';
    if (initiative.progress >= 60) return 'from-yellow-500 to-orange-400';
    return 'from-red-500 to-pink-400';
  };

  return (
    <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-xl p-6 hover:shadow-xl transition-all duration-300 hover:border-slate-600/50">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-400 rounded-lg flex items-center justify-center">
            {getStatusIcon(initiative.status)}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white">{initiative.name}</h3>
            <p className="text-sm text-slate-400">{initiative.description}</p>
          </div>
        </div>
        <div className={`text-sm font-semibold ${getStatusColor(initiative.status)}`}>
          {initiative.status.replace('_', ' ').toUpperCase()}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <div className="text-sm text-slate-400 mb-1">Timeline</div>
          <div className="text-sm text-white">
            {initiative.startDate.toLocaleDateString()} - {initiative.endDate.toLocaleDateString()}
          </div>
        </div>
        <div>
          <div className="text-sm text-slate-400 mb-1">Owner</div>
          <div className="text-sm font-semibold text-white">{initiative.owner}</div>
        </div>
      </div>

      <div className="space-y-3 mb-4">
        <div>
          <div className="flex justify-between text-sm text-slate-400 mb-1">
            <span>Progress</span>
            <span>{initiative.progress}%</span>
          </div>
          <div className="w-full bg-slate-700 rounded-full h-2">
            <div
              className={`h-2 rounded-full bg-gradient-to-r ${getProgressColor()}`}
              style={{ width: `${initiative.progress}%` }}
            ></div>
          </div>
        </div>

        <div>
          <div className="flex justify-between text-sm text-slate-400 mb-1">
            <span>Budget</span>
            <span>${initiative.spent.toLocaleString()} / ${initiative.budget.toLocaleString()}</span>
          </div>
          <div className="w-full bg-slate-700 rounded-full h-2">
            <div
              className="h-2 rounded-full bg-gradient-to-r from-blue-500 to-cyan-400"
              style={{ width: `${getBudgetPercentage()}%` }}
            ></div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <div className="text-sm text-slate-400 mb-1">Milestones</div>
          <div className="text-sm font-semibold text-white">{initiative.milestones.length} total</div>
        </div>
        <div>
          <div className="text-sm text-slate-400 mb-1">Benefits</div>
          <div className="text-sm font-semibold text-white">{initiative.benefits.length} identified</div>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="text-xs text-slate-400">
          {initiative.milestones.filter(m => m.status === 'completed').length} of {initiative.milestones.length} milestones completed
        </div>
        <div className="text-xs text-slate-400">
          ${initiative.benefits.reduce((sum, b) => sum + b.value, 0).toLocaleString()} total benefits
        </div>
      </div>
    </div>
  );
};

// ============================================================================
// STRATEGIC INITIATIVES MAIN COMPONENT
// ============================================================================

interface StrategicInitiativesProps {
  data: ExecutiveDashboardData;
}

/**
 * Strategic Initiatives Main Component
 */
const StrategicInitiatives: React.FC<StrategicInitiativesProps> = ({ data }) => {
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('progress');

  const filteredInitiatives = data.strategicInitiatives.filter(initiative => {
    return filterStatus === 'all' || initiative.status === filterStatus;
  });

  const sortedInitiatives = [...filteredInitiatives].sort((a, b) => {
    switch (sortBy) {
      case 'progress': return b.progress - a.progress;
      case 'budget': return b.budget - a.budget;
      case 'timeline': return new Date(a.startDate).getTime() - new Date(b.startDate).getTime();
      default: return 0;
    }
  });

  const getInitiativeSummary = () => {
    const totalBudget = data.strategicInitiatives.reduce((sum, i) => sum + i.budget, 0);
    const totalSpent = data.strategicInitiatives.reduce((sum, i) => sum + i.spent, 0);
    const totalBenefits = data.strategicInitiatives.reduce((sum, i) => sum + i.benefits.reduce((bSum, b) => bSum + b.value, 0), 0);
    const completedInitiatives = data.strategicInitiatives.filter(i => i.status === 'completed').length;
    
    return { totalBudget, totalSpent, totalBenefits, completedInitiatives };
  };

  const summary = getInitiativeSummary();

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-900/40 to-pink-900/40 border border-purple-500/50 rounded-xl p-6 shadow-2xl">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold text-white mb-2">Strategic Initiatives</h2>
            <p className="text-purple-300 text-lg">
              Project tracking and milestone management
            </p>
          </div>
          <div className="text-right">
            <div className="text-sm text-slate-300 mb-1">Total Investment</div>
            <div className="text-lg font-semibold text-white">
              ${summary.totalBudget.toLocaleString()}
            </div>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-gradient-to-br from-green-800/50 to-emerald-800/50 border border-green-500/50 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-green-400">{summary.completedInitiatives}</div>
              <div className="text-sm text-slate-400">Completed</div>
            </div>
            <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-400" />
            </div>
          </div>
        </div>
        <div className="bg-gradient-to-br from-blue-800/50 to-cyan-800/50 border border-blue-500/50 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-blue-400">
                {Math.round((summary.totalSpent / summary.totalBudget) * 100)}%
              </div>
              <div className="text-sm text-slate-400">Budget Used</div>
            </div>
            <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-blue-400" />
            </div>
          </div>
        </div>
        <div className="bg-gradient-to-br from-purple-800/50 to-pink-800/50 border border-purple-500/50 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-purple-400">
                ${summary.totalBenefits.toLocaleString()}
              </div>
              <div className="text-sm text-slate-400">Total Benefits</div>
            </div>
            <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-purple-400" />
            </div>
          </div>
        </div>
        <div className="bg-gradient-to-br from-yellow-800/50 to-orange-800/50 border border-yellow-500/50 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-yellow-400">{data.strategicInitiatives.length}</div>
              <div className="text-sm text-slate-400">Active Initiatives</div>
            </div>
            <div className="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
              <Target className="w-6 h-6 text-yellow-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Sort */}
      <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-xl p-6">
        <div className="flex flex-wrap gap-4 items-center">
          <div>
            <label className="text-sm text-slate-400 mb-2 block">Filter by Status</label>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="bg-slate-700 text-white px-4 py-2 rounded-lg border border-slate-600"
            >
              <option value="all">All Statuses</option>
              <option value="completed">Completed</option>
              <option value="in_progress">In Progress</option>
              <option value="planning">Planning</option>
              <option value="on_hold">On Hold</option>
            </select>
          </div>
          <div>
            <label className="text-sm text-slate-400 mb-2 block">Sort by</label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="bg-slate-700 text-white px-4 py-2 rounded-lg border border-slate-600"
            >
              <option value="progress">Progress</option>
              <option value="budget">Budget</option>
              <option value="timeline">Timeline</option>
            </select>
          </div>
          <div className="ml-auto text-sm text-slate-400">
            Showing {filteredInitiatives.length} of {data.strategicInitiatives.length} initiatives
          </div>
        </div>
      </div>

      {/* Initiatives Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {sortedInitiatives.map((initiative) => (
          <InitiativeCard key={initiative.id} initiative={initiative} />
        ))}
      </div>

      {/* Summary */}
      <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-xl p-6">
        <h3 className="text-xl font-bold text-white mb-4">Strategic Initiative Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="bg-slate-700/50 rounded-lg p-4">
              <div className="text-sm text-slate-400 mb-1">Total Budget</div>
              <div className="text-lg font-semibold text-white">${summary.totalBudget.toLocaleString()}</div>
            </div>
            <div className="bg-slate-700/50 rounded-lg p-4">
              <div className="text-sm text-slate-400 mb-1">Total Spent</div>
              <div className="text-lg font-semibold text-white">${summary.totalSpent.toLocaleString()}</div>
            </div>
          </div>
          <div className="space-y-4">
            <div className="bg-slate-700/50 rounded-lg p-4">
              <div className="text-sm text-slate-400 mb-1">Expected Benefits</div>
              <div className="text-lg font-semibold text-green-400">${summary.totalBenefits.toLocaleString()}</div>
            </div>
            <div className="bg-slate-700/50 rounded-lg p-4">
              <div className="text-sm text-slate-400 mb-1">ROI Estimate</div>
              <div className="text-lg font-semibold text-blue-400">
                {Math.round(((summary.totalBenefits - summary.totalSpent) / summary.totalSpent) * 100)}%
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StrategicInitiatives;