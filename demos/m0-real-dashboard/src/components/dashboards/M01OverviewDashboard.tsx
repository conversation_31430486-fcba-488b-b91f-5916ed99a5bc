/**
 * ============================================================================
 * M0.1 Overview Dashboard Component
 * ============================================================================
 * 
 * Purpose: Main dashboard component for M0.1 milestone overview
 * Features: Real-time data fetching, 4 visualization components, responsive layout
 * 
 * Components:
 * - MilestoneStatsCard: M0.1 completion metrics
 * - TaskCompletionChart: 50/50 tasks by category
 * - PerformanceMetricsDisplay: <6ms average performance
 * - ComponentGalleryPreview: 6 enhanced components
 * 
 * Author: AI Assistant (Phase 2 Day 3 Implementation)
 * Created: 2025-12-31
 * ============================================================================
 */

'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Alert,
  Snackbar,
  CircularProgress,
  Paper,
  Divider,
  useTheme
} from '@mui/material';
import { ResponsiveContainer, ResponsiveGrid } from '../common/ResponsiveContainer';
import MilestoneStatsCard from '../widgets/m01/MilestoneStatsCard';
import TaskCompletionChart from '../widgets/m01/TaskCompletionChart';
import PerformanceMetricsDisplay from '../widgets/m01/PerformanceMetricsDisplay';
import ComponentGalleryPreview from '../widgets/m01/ComponentGalleryPreview';
import { glassCard } from '../../styles/glassmorphism';
import { PageTransition, StaggerContainer, StaggerItem, LoadingSpinner } from '../animations';

interface M01OverviewData {
  success: boolean;
  data: {
    milestoneStats: {
      totalTasks: number;
      completedTasks: number;
      completionPercentage: number;
      totalLOC: number;
      totalTestLOC: number;
      averageCoverage: string;
      categories: Record<string, { completed: number; total: number; percentage: number }>;
    };
    performanceMetrics: {
      averagePerformanceGain: string;
      totalLOC: number;
      totalTestLOC: number;
      averageCoverage: string;
    };
    comparisonData: {
      m0Components: number;
      m01EnhancedComponents: number;
      newFeatures: number;
      performanceImprovements: number;
    };
    summary: {
      totalTasks: number;
      completedTasks: number;
      completionPercentage: number;
      totalLOC: number;
      totalTestLOC: number;
      averageCoverage: string;
      enhancedComponents: number;
    };
    categories: Array<{
      name: string;
      completed: number;
      total: number;
      percentage: number;
    }>;
  };
  timestamp: string;
}

export default function M01OverviewDashboard() {
  const theme = useTheme();
  const [data, setData] = useState<M01OverviewData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [alertOpen, setAlertOpen] = useState(false);
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Fetch M0.1 overview data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/m01-enhancements/overview');
        
        if (!response.ok) {
          throw new Error(`Failed to fetch M0.1 data: ${response.statusText}`);
        }

        const result = await response.json();
        
        if (!result.success) {
          throw new Error(result.message || 'Failed to load M0.1 data');
        }

        setData(result);
        setError(null);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
        setError(errorMessage);
        setAlertOpen(true);
        console.error('Error fetching M0.1 overview data:', err);
      } finally {
        setLoading(false);
      }
    };

    if (mounted) {
      fetchData();
    }
  }, [mounted]);

  if (!mounted) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <LoadingSpinner size={60} />
      </Box>
    );
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', minHeight: '400px', gap: 2 }}>
        <LoadingSpinner size={60} />
        <Typography variant="h6" color="text.secondary">
          Loading M0.1 Overview Data...
        </Typography>
      </Box>
    );
  }

  if (error || !data) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error || 'Failed to load M0.1 overview data'}
      </Alert>
    );
  }

  return (
    <PageTransition>
      <ResponsiveContainer>
        <StaggerContainer speed="normal">
          {/* Page Header with Glassmorphism */}
          <StaggerItem>
            <Paper
              elevation={0}
              sx={{
                p: 3,
                mb: 3,
                ...glassCard(theme),
                background: theme.palette.mode === 'dark'
                  ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%)'
                  : 'linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%)',
                backdropFilter: 'blur(20px)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                position: 'relative',
                overflow: 'hidden',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%)',
                  pointerEvents: 'none',
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2, position: 'relative', zIndex: 1 }}>
                <Box>
                  <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold', mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                    ⭐ M0.1 Enhancements Overview
                  </Typography>
                  <Typography variant="body1" sx={{ opacity: 0.85 }}>
                    Comprehensive view of 50 completed tasks and 6 enhanced components
                  </Typography>
                </Box>
                <Box sx={{ textAlign: 'right' }}>
                  <Typography variant="h3" sx={{ fontWeight: 'bold' }}>
                    {data.data.summary.completionPercentage}%
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.8 }}>
                    Completion Rate
                  </Typography>
                </Box>
              </Box>
            </Paper>
          </StaggerItem>

          {/* Milestone Statistics Card */}
          <StaggerItem>
            <Box sx={{ mb: 3 }}>
              <MilestoneStatsCard
                milestoneStats={data.data.milestoneStats}
                summary={data.data.summary}
              />
            </Box>
          </StaggerItem>

          {/* Task Completion Chart and Performance Metrics */}
          <StaggerItem>
            <ResponsiveGrid>
              <Box>
                <TaskCompletionChart
                  categories={data.data.categories}
                  totalTasks={data.data.summary.totalTasks}
                  completedTasks={data.data.summary.completedTasks}
                />
              </Box>
              <Box>
                <PerformanceMetricsDisplay
                  performanceMetrics={data.data.performanceMetrics}
                  comparisonData={data.data.comparisonData}
                />
              </Box>
            </ResponsiveGrid>
          </StaggerItem>

          {/* Component Gallery Preview */}
          <StaggerItem>
            <Box sx={{ mt: 3 }}>
              <ComponentGalleryPreview />
            </Box>
          </StaggerItem>
        </StaggerContainer>

        {/* Error Snackbar */}
        <Snackbar
          open={alertOpen}
          autoHideDuration={6000}
          onClose={() => setAlertOpen(false)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        >
          <Alert onClose={() => setAlertOpen(false)} severity="error" sx={{ width: '100%' }}>
            {error}
          </Alert>
        </Snackbar>
      </ResponsiveContainer>
    </PageTransition>
  );
}

