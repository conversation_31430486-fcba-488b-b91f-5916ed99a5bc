/**
 * ============================================================================
 * SYSTEM HEALTH OVERVIEW COMPONENT
 * ============================================================================
 * 
 * Purpose: Display overall system health status and key metrics
 * Features: Health score, uptime, active alerts, critical issues
 * 
 * Author: AI Assistant (Enhancement 3.1 Implementation)
 * Created: 2026-01-03
 * ============================================================================
 */

'use client';

import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Grid,
  CircularProgress,
  Chip
} from '@mui/material';
import {
  CheckCircle,
  Warning,
  Error as ErrorIcon,
  HelpOutline,
  TrendingUp,
  Notifications,
  BugReport
} from '@mui/icons-material';
import { ISystemHealth, THealthStatus } from '@/lib/monitoring-service';

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface ISystemHealthOverviewProps {
  health: ISystemHealth;
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Get status color
 */
const getStatusColor = (status: THealthStatus): string => {
  switch (status) {
    case 'healthy': return '#4caf50';
    case 'warning': return '#ff9800';
    case 'critical': return '#f44336';
    case 'unknown': return '#9e9e9e';
  }
};

/**
 * Get status icon
 */
const getStatusIcon = (status: THealthStatus) => {
  switch (status) {
    case 'healthy': return <CheckCircle sx={{ fontSize: 48 }} />;
    case 'warning': return <Warning sx={{ fontSize: 48 }} />;
    case 'critical': return <ErrorIcon sx={{ fontSize: 48 }} />;
    case 'unknown': return <HelpOutline sx={{ fontSize: 48 }} />;
  }
};

/**
 * Get health score color
 */
const getHealthScoreColor = (score: number): string => {
  if (score >= 90) return '#4caf50';
  if (score >= 70) return '#ff9800';
  return '#f44336';
};

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export const SystemHealthOverview: React.FC<ISystemHealthOverviewProps> = ({ health }) => {
  const statusColor = getStatusColor(health.overallStatus);
  const scoreColor = getHealthScoreColor(health.healthScore);

  return (
    <Card
      sx={{
        background: `linear-gradient(135deg, ${statusColor}20 0%, rgba(255, 255, 255, 0.05) 100%)`,
        backdropFilter: 'blur(10px)',
        border: `2px solid ${statusColor}40`,
        borderRadius: 2,
        transition: 'all 0.3s ease',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: `0 12px 32px ${statusColor}30`
        }
      }}
    >
      <CardContent>
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: 3 }}>
          <Box sx={{ color: statusColor, mb: 1 }}>
            {getStatusIcon(health.overallStatus)}
          </Box>
          <Typography variant="h5" sx={{ color: '#fff', fontWeight: 600, mb: 0.5 }}>
            System Status: {health.overallStatus.toUpperCase()}
          </Typography>
          <Typography variant="caption" sx={{ color: 'rgba(255, 255, 255, 0.5)' }}>
            Last checked: {health.lastCheck.toLocaleTimeString()}
          </Typography>
        </Box>

        {/* Health Score */}
        <Box sx={{ textAlign: 'center', mb: 3 }}>
          <Box sx={{ position: 'relative', display: 'inline-flex' }}>
            <CircularProgress
              variant="determinate"
              value={health.healthScore}
              size={120}
              thickness={4}
              sx={{
                color: scoreColor,
                '& .MuiCircularProgress-circle': {
                  strokeLinecap: 'round'
                }
              }}
            />
            <Box
              sx={{
                top: 0,
                left: 0,
                bottom: 0,
                right: 0,
                position: 'absolute',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexDirection: 'column'
              }}
            >
              <Typography variant="h4" sx={{ color: '#fff', fontWeight: 700 }}>
                {health.healthScore}
              </Typography>
              <Typography variant="caption" sx={{ color: 'rgba(255, 255, 255, 0.5)' }}>
                Health Score
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Key Metrics Grid */}
        <Grid container spacing={2}>
          {/* Uptime */}
          <Grid item xs={6}>
            <Box
              sx={{
                background: 'rgba(255, 255, 255, 0.05)',
                borderRadius: 1,
                p: 2,
                textAlign: 'center',
                border: '1px solid rgba(255, 255, 255, 0.1)'
              }}
            >
              <TrendingUp sx={{ color: '#4caf50', fontSize: 32, mb: 1 }} />
              <Typography variant="h6" sx={{ color: '#fff', fontWeight: 600 }}>
                {health.uptime.toFixed(2)}%
              </Typography>
              <Typography variant="caption" sx={{ color: 'rgba(255, 255, 255, 0.5)' }}>
                Uptime
              </Typography>
            </Box>
          </Grid>

          {/* Active Alerts */}
          <Grid item xs={6}>
            <Box
              sx={{
                background: 'rgba(255, 255, 255, 0.05)',
                borderRadius: 1,
                p: 2,
                textAlign: 'center',
                border: '1px solid rgba(255, 255, 255, 0.1)'
              }}
            >
              <Notifications sx={{ color: health.activeAlerts > 0 ? '#ff9800' : '#4caf50', fontSize: 32, mb: 1 }} />
              <Typography variant="h6" sx={{ color: '#fff', fontWeight: 600 }}>
                {health.activeAlerts}
              </Typography>
              <Typography variant="caption" sx={{ color: 'rgba(255, 255, 255, 0.5)' }}>
                Active Alerts
              </Typography>
            </Box>
          </Grid>

          {/* Critical Issues */}
          <Grid item xs={12}>
            <Box
              sx={{
                background: health.criticalIssues > 0 ? 'rgba(244, 67, 54, 0.1)' : 'rgba(76, 175, 80, 0.1)',
                borderRadius: 1,
                p: 2,
                textAlign: 'center',
                border: `1px solid ${health.criticalIssues > 0 ? 'rgba(244, 67, 54, 0.3)' : 'rgba(76, 175, 80, 0.3)'}`
              }}
            >
              <BugReport sx={{ color: health.criticalIssues > 0 ? '#f44336' : '#4caf50', fontSize: 32, mb: 1 }} />
              <Typography variant="h6" sx={{ color: '#fff', fontWeight: 600 }}>
                {health.criticalIssues}
              </Typography>
              <Typography variant="caption" sx={{ color: 'rgba(255, 255, 255, 0.5)' }}>
                Critical Issues
              </Typography>
              {health.criticalIssues === 0 && (
                <Chip
                  label="ALL SYSTEMS OPERATIONAL"
                  size="small"
                  sx={{
                    mt: 1,
                    background: 'rgba(76, 175, 80, 0.2)',
                    color: '#4caf50',
                    border: '1px solid rgba(76, 175, 80, 0.3)',
                    fontWeight: 600
                  }}
                />
              )}
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default SystemHealthOverview;

