/**
 * ============================================================================
 * SYSTEM PERFORMANCE OVERVIEW WIDGET - M0.1 ENHANCEMENTS
 * ============================================================================
 * 
 * Overview panel showing aggregate performance metrics for all 136 M0 components
 * 
 * Features:
 * - Total components count
 * - Average response time
 * - Average memory usage
 * - Average CPU usage
 * - Total operations count
 * - Operations per second
 * - Error rate percentage
 * - Visual indicators with color coding
 * 
 * Authority: President & CEO, E.Z. Consultancy
 * Created: 2026-01-01
 * Phase: Phase 4 - Performance Analytics Dashboard
 * ============================================================================
 */

'use client';

import React from 'react';
import { Activity, Clock, Cpu, Database, TrendingUp, AlertCircle, CheckCircle } from 'lucide-react';

/**
 * System Metrics Interface
 */
interface ISystemMetrics {
  totalComponents: number;
  avgResponseTime: number;
  avgMemoryUsage: number;
  avgCpuUsage: number;
  totalOperations: number;
  operationsPerSecond: number;
  errorRate: number;
}

/**
 * Component Props
 */
interface ISystemPerformanceOverviewProps {
  metrics: ISystemMetrics;
}

/**
 * System Performance Overview Component
 */
export function SystemPerformanceOverview({ metrics }: ISystemPerformanceOverviewProps): JSX.Element {
  /**
   * Format number with commas
   */
  const formatNumber = (num: number): string => {
    return num.toLocaleString('en-US', { maximumFractionDigits: 2 });
  };

  /**
   * Get status color based on metric value (Dark Theme)
   */
  const getStatusColor = (value: number, thresholds: { good: number; warning: number }): string => {
    if (value <= thresholds.good) return 'text-teal-400';
    if (value <= thresholds.warning) return 'text-yellow-400';
    return 'text-red-400';
  };

  /**
   * Get status icon based on metric value (Dark Theme)
   */
  const getStatusIcon = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return <CheckCircle className="w-5 h-5 text-teal-400" />;
    if (value <= thresholds.warning) return <AlertCircle className="w-5 h-5 text-yellow-400" />;
    return <AlertCircle className="w-5 h-5 text-red-400" />;
  };

  return (
    <div className="bg-gradient-to-br from-[#1e232d]/60 to-[#252b37]/60 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/5">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-500/20 to-purple-500/20 border-b border-white/5 px-6 py-4 rounded-t-3xl">
        <h2 className="text-xl font-bold flex items-center text-white">
          <Activity className="w-6 h-6 mr-2 text-indigo-400" />
          System Performance Overview
        </h2>
        <p className="text-gray-400 text-sm mt-1">
          Aggregate metrics across all {metrics.totalComponents} M0 components
        </p>
      </div>

      {/* Metrics Grid */}
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Average Response Time */}
          <div className="bg-gradient-to-br from-blue-500/10 to-blue-600/10 rounded-2xl p-4 border border-blue-500/20 hover:bg-blue-500/20 transition-all duration-200">
            <div className="flex items-center justify-between mb-2">
              <Clock className="w-8 h-8 text-blue-400" />
              {getStatusIcon(metrics.avgResponseTime, { good: 10, warning: 50 })}
            </div>
            <p className="text-sm text-gray-400 mb-1">Avg Response Time</p>
            <p className={`text-2xl font-bold ${getStatusColor(metrics.avgResponseTime, { good: 10, warning: 50 })}`}>
              {formatNumber(metrics.avgResponseTime)}ms
            </p>
            <p className="text-xs text-gray-500 mt-1">Target: &lt;10ms</p>
          </div>

          {/* Average Memory Usage */}
          <div className="bg-gradient-to-br from-purple-500/10 to-purple-600/10 rounded-2xl p-4 border border-purple-500/20 hover:bg-purple-500/20 transition-all duration-200">
            <div className="flex items-center justify-between mb-2">
              <Database className="w-8 h-8 text-purple-400" />
              {getStatusIcon(metrics.avgMemoryUsage, { good: 50, warning: 100 })}
            </div>
            <p className="text-sm text-gray-400 mb-1">Avg Memory Usage</p>
            <p className={`text-2xl font-bold ${getStatusColor(metrics.avgMemoryUsage, { good: 50, warning: 100 })}`}>
              {formatNumber(metrics.avgMemoryUsage)}MB
            </p>
            <p className="text-xs text-gray-500 mt-1">Target: &lt;50MB</p>
          </div>

          {/* Average CPU Usage */}
          <div className="bg-gradient-to-br from-teal-500/10 to-teal-600/10 rounded-2xl p-4 border border-teal-500/20 hover:bg-teal-500/20 transition-all duration-200">
            <div className="flex items-center justify-between mb-2">
              <Cpu className="w-8 h-8 text-teal-400" />
              {getStatusIcon(metrics.avgCpuUsage, { good: 30, warning: 60 })}
            </div>
            <p className="text-sm text-gray-400 mb-1">Avg CPU Usage</p>
            <p className={`text-2xl font-bold ${getStatusColor(metrics.avgCpuUsage, { good: 30, warning: 60 })}`}>
              {formatNumber(metrics.avgCpuUsage)}%
            </p>
            <p className="text-xs text-gray-500 mt-1">Target: &lt;30%</p>
          </div>

          {/* Operations Per Second */}
          <div className="bg-gradient-to-br from-orange-500/10 to-orange-600/10 rounded-2xl p-4 border border-orange-500/20 hover:bg-orange-500/20 transition-all duration-200">
            <div className="flex items-center justify-between mb-2">
              <TrendingUp className="w-8 h-8 text-orange-400" />
              <CheckCircle className="w-5 h-5 text-teal-400" />
            </div>
            <p className="text-sm text-gray-400 mb-1">Operations/Second</p>
            <p className="text-2xl font-bold text-orange-400">
              {formatNumber(metrics.operationsPerSecond)}
            </p>
            <p className="text-xs text-gray-500 mt-1">Total: {formatNumber(metrics.totalOperations)}</p>
          </div>
        </div>

        {/* Error Rate Banner */}
        <div className={`mt-4 rounded-2xl p-4 border ${
          metrics.errorRate === 0
            ? 'bg-teal-500/10 border-teal-500/20'
            : metrics.errorRate < 1
            ? 'bg-yellow-500/10 border-yellow-500/20'
            : 'bg-red-500/10 border-red-500/20'
        }`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              {metrics.errorRate === 0 ? (
                <CheckCircle className="w-6 h-6 text-teal-400 mr-3" />
              ) : (
                <AlertCircle className="w-6 h-6 text-red-400 mr-3" />
              )}
              <div>
                <p className="font-semibold text-white">System Error Rate</p>
                <p className="text-sm text-gray-400">
                  {metrics.errorRate === 0 ? 'All systems operational' : 'Some errors detected'}
                </p>
              </div>
            </div>
            <div className="text-right">
              <p className={`text-3xl font-bold ${
                metrics.errorRate === 0 ? 'text-teal-400' : 'text-red-400'
              }`}>
                {formatNumber(metrics.errorRate)}%
              </p>
              <p className="text-xs text-gray-500">Target: 0%</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

