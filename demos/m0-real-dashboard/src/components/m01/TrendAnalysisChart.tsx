/**
 * ============================================================================
 * TREND ANALYSIS CHART COMPONENT
 * ============================================================================
 * 
 * Purpose: Display historical trend data with time-series visualization
 * Features: Line chart, prediction indicator, status zones
 * 
 * Author: AI Assistant (Enhancement 3.1 Implementation)
 * Created: 2026-01-03
 * ============================================================================
 */

'use client';

import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip
} from '@mui/material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceLine,
  Area,
  AreaChart
} from 'recharts';
import { TrendingUp, TrendingDown } from '@mui/icons-material';
import { IMetricTrend } from '@/lib/monitoring-service';

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface ITrendAnalysisChartProps {
  trend: IMetricTrend;
  warningThreshold?: number;
  criticalThreshold?: number;
  unit?: string;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export const TrendAnalysisChart: React.FC<ITrendAnalysisChartProps> = ({
  trend,
  warningThreshold,
  criticalThreshold,
  unit = ''
}) => {
  // Format data for chart
  const chartData = trend.data.map(point => ({
    time: point.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
    value: point.value,
    status: point.status
  }));

  // Calculate trend direction
  const firstValue = trend.data[0]?.value || 0;
  const lastValue = trend.data[trend.data.length - 1]?.value || 0;
  const trendDirection = lastValue > firstValue ? 'up' : 'down';
  const trendPercentage = Math.abs(((lastValue - firstValue) / firstValue) * 100);

  // Custom tooltip
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <Box
          sx={{
            background: 'rgba(0, 0, 0, 0.9)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            borderRadius: 1,
            p: 1.5
          }}
        >
          <Typography variant="caption" sx={{ color: '#fff', display: 'block', mb: 0.5 }}>
            {data.time}
          </Typography>
          <Typography variant="body2" sx={{ color: '#4caf50', fontWeight: 600 }}>
            {data.value.toFixed(2)} {unit}
          </Typography>
          <Chip
            label={data.status.toUpperCase()}
            size="small"
            sx={{
              mt: 0.5,
              height: 20,
              fontSize: '0.65rem',
              background: data.status === 'healthy' ? 'rgba(76, 175, 80, 0.2)' : 
                         data.status === 'warning' ? 'rgba(255, 152, 0, 0.2)' : 
                         'rgba(244, 67, 54, 0.2)',
              color: data.status === 'healthy' ? '#4caf50' : 
                     data.status === 'warning' ? '#ff9800' : 
                     '#f44336'
            }}
          />
        </Box>
      );
    }
    return null;
  };

  return (
    <Card
      sx={{
        background: 'rgba(255, 255, 255, 0.05)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255, 255, 255, 0.1)',
        borderRadius: 2
      }}
    >
      <CardContent>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box>
            <Typography variant="h6" sx={{ color: '#fff', fontWeight: 600 }}>
              {trend.metricName} - 24h Trend
            </Typography>
            <Typography variant="caption" sx={{ color: 'rgba(255, 255, 255, 0.5)' }}>
              Historical performance analysis
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {trendDirection === 'up' ? (
              <TrendingUp sx={{ color: '#f44336', fontSize: 24 }} />
            ) : (
              <TrendingDown sx={{ color: '#4caf50', fontSize: 24 }} />
            )}
            <Box>
              <Typography variant="h6" sx={{ color: trendDirection === 'up' ? '#f44336' : '#4caf50', fontWeight: 600 }}>
                {trendPercentage.toFixed(1)}%
              </Typography>
              <Typography variant="caption" sx={{ color: 'rgba(255, 255, 255, 0.5)' }}>
                {trendDirection === 'up' ? 'Increase' : 'Decrease'}
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Chart */}
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={chartData}>
            <defs>
              <linearGradient id="colorValue" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#4caf50" stopOpacity={0.3}/>
                <stop offset="95%" stopColor="#4caf50" stopOpacity={0}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="rgba(255, 255, 255, 0.1)" />
            <XAxis 
              dataKey="time" 
              stroke="rgba(255, 255, 255, 0.5)"
              tick={{ fill: 'rgba(255, 255, 255, 0.7)', fontSize: 12 }}
            />
            <YAxis 
              stroke="rgba(255, 255, 255, 0.5)"
              tick={{ fill: 'rgba(255, 255, 255, 0.7)', fontSize: 12 }}
            />
            <Tooltip content={<CustomTooltip />} />
            
            {/* Threshold lines */}
            {warningThreshold && (
              <ReferenceLine 
                y={warningThreshold} 
                stroke="#ff9800" 
                strokeDasharray="3 3"
                label={{ value: 'Warning', fill: '#ff9800', fontSize: 12 }}
              />
            )}
            {criticalThreshold && (
              <ReferenceLine 
                y={criticalThreshold} 
                stroke="#f44336" 
                strokeDasharray="3 3"
                label={{ value: 'Critical', fill: '#f44336', fontSize: 12 }}
              />
            )}
            
            <Area 
              type="monotone" 
              dataKey="value" 
              stroke="#4caf50" 
              strokeWidth={2}
              fill="url(#colorValue)"
            />
          </AreaChart>
        </ResponsiveContainer>

        {/* Prediction */}
        {trend.prediction && (
          <Box sx={{ mt: 2, p: 2, background: 'rgba(33, 150, 243, 0.1)', borderRadius: 1, border: '1px solid rgba(33, 150, 243, 0.3)' }}>
            <Typography variant="subtitle2" sx={{ color: '#2196f3', fontWeight: 600, mb: 0.5 }}>
              Predictive Analysis
            </Typography>
            <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
              Next predicted value: <strong>{trend.prediction.nextValue.toFixed(2)} {unit}</strong>
            </Typography>
            <Typography variant="caption" sx={{ color: 'rgba(255, 255, 255, 0.5)' }}>
              Confidence: {(trend.prediction.confidence * 100).toFixed(0)}%
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default TrendAnalysisChart;
