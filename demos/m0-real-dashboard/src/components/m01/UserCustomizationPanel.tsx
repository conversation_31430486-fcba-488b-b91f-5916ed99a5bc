/**
 * ============================================================================
 * USER CUSTOMIZATION PANEL COMPONENT
 * ============================================================================
 * 
 * Purpose: Comprehensive user customization interface
 * Features: Theme settings, layout preferences, widget configuration
 * 
 * Author: AI Assistant (Enhancement 2.4 Implementation)
 * Created: 2026-01-03
 * ============================================================================
 */

'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Slider,
  Switch,
  Button,
  TextField,
  Select,
  MenuItem,
  Grid,
  Divider,
  Alert,
  useTheme
} from '@mui/material';
import { Download, Upload, RotateCcw, Save } from 'lucide-react';
import { glassCard } from '../../styles/glassmorphism';
import { userPreferencesService, IUserPreferences } from '../../lib/user-preferences';
import { AnimatedCard, ScrollReveal } from '../animations';

// ============================================================================
// COMPONENT STATE
// ============================================================================

interface ITabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: ITabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`customization-tabpanel-${index}`}
      aria-labelledby={`customization-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function UserCustomizationPanel(): JSX.Element {
  const theme = useTheme();
  const [tabValue, setTabValue] = useState(0);
  const [preferences, setPreferences] = useState<IUserPreferences>(
    userPreferencesService.getPreferences()
  );
  const [saveMessage, setSaveMessage] = useState<string | null>(null);

  useEffect(() => {
    const prefs = userPreferencesService.getPreferences();
    setPreferences(prefs);
  }, []);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleThemeChange = (key: string, value: string | number) => {
    const updatedTheme = { ...preferences.theme, [key]: value };
    userPreferencesService.updateTheme({ [key]: value });
    setPreferences({ ...preferences, theme: updatedTheme });
    showSaveMessage('Theme preferences saved');
  };

  const handleLayoutChange = (key: string, value: string | number | boolean) => {
    const updatedLayout = { ...preferences.layout, [key]: value };
    userPreferencesService.updateLayout({ [key]: value });
    setPreferences({ ...preferences, layout: updatedLayout });
    showSaveMessage('Layout preferences saved');
  };

  const handleWidgetChange = (key: string, value: string[] | number | boolean) => {
    const updatedWidgets = { ...preferences.widgets, [key]: value };
    userPreferencesService.updateWidgets({ [key]: value });
    setPreferences({ ...preferences, widgets: updatedWidgets });
    showSaveMessage('Widget preferences saved');
  };

  const handleReset = () => {
    userPreferencesService.resetToDefaults();
    setPreferences(userPreferencesService.getPreferences());
    showSaveMessage('Preferences reset to defaults');
  };

  const handleExport = () => {
    const json = userPreferencesService.exportPreferences();
    const blob = new Blob([json], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'm01-preferences.json';
    a.click();
    URL.revokeObjectURL(url);
    showSaveMessage('Preferences exported successfully');
  };

  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const json = e.target?.result as string;
        const success = userPreferencesService.importPreferences(json);
        if (success) {
          setPreferences(userPreferencesService.getPreferences());
          showSaveMessage('Preferences imported successfully');
        } else {
          showSaveMessage('Failed to import preferences');
        }
      };
      reader.readAsText(file);
    }
  };

  const showSaveMessage = (message: string) => {
    setSaveMessage(message);
    setTimeout(() => setSaveMessage(null), 3000);
  };

  return (
    <Box>
      {/* Header */}
      <ScrollReveal delay={0.1}>
        <Paper elevation={0} sx={{ p: 4, mb: 3, ...glassCard(theme) }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                ⚙️ User Customization
              </Typography>
              <Typography variant="body1" sx={{ opacity: 0.85 }}>
                Personalize your dashboard experience with theme, layout, and widget preferences
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="outlined"
                startIcon={<Download size={18} />}
                onClick={handleExport}
                size="small"
              >
                Export
              </Button>
              <Button
                variant="outlined"
                component="label"
                startIcon={<Upload size={18} />}
                size="small"
              >
                Import
                <input type="file" hidden accept=".json" onChange={handleImport} />
              </Button>
              <Button
                variant="outlined"
                color="warning"
                startIcon={<RotateCcw size={18} />}
                onClick={handleReset}
                size="small"
              >
                Reset
              </Button>
            </Box>
          </Box>

          {saveMessage && (
            <Alert severity="success" sx={{ mt: 2 }}>
              {saveMessage}
            </Alert>
          )}
        </Paper>
      </ScrollReveal>

      {/* Tabs */}
      <ScrollReveal delay={0.2}>
        <Paper elevation={0} sx={{ ...glassCard(theme) }}>
          <Tabs value={tabValue} onChange={handleTabChange} sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tab label="Theme Settings" />
            <Tab label="Layout Preferences" />
            <Tab label="Widget Configuration" />
          </Tabs>

          {/* Theme Settings Tab */}
          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <FormControl component="fieldset" fullWidth>
                  <FormLabel component="legend">Theme Mode</FormLabel>
                  <RadioGroup
                    value={preferences.theme.mode}
                    onChange={(e) => handleThemeChange('mode', e.target.value)}
                  >
                    <FormControlLabel value="light" control={<Radio />} label="Light" />
                    <FormControlLabel value="dark" control={<Radio />} label="Dark" />
                    <FormControlLabel value="auto" control={<Radio />} label="Auto (System)" />
                  </RadioGroup>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <FormLabel>Font Size</FormLabel>
                  <Select
                    value={preferences.theme.fontSize}
                    onChange={(e) => handleThemeChange('fontSize', e.target.value)}
                  >
                    <MenuItem value="small">Small</MenuItem>
                    <MenuItem value="medium">Medium</MenuItem>
                    <MenuItem value="large">Large</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <FormLabel>Primary Color</FormLabel>
                  <TextField
                    type="color"
                    value={preferences.theme.primaryColor}
                    onChange={(e) => handleThemeChange('primaryColor', e.target.value)}
                    fullWidth
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <FormLabel>Secondary Color</FormLabel>
                  <TextField
                    type="color"
                    value={preferences.theme.secondaryColor}
                    onChange={(e) => handleThemeChange('secondaryColor', e.target.value)}
                    fullWidth
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <FormControl fullWidth>
                  <FormLabel>Border Radius: {preferences.theme.borderRadius}px</FormLabel>
                  <Slider
                    value={preferences.theme.borderRadius}
                    onChange={(_e, value) => handleThemeChange('borderRadius', value as number)}
                    min={0}
                    max={24}
                    step={2}
                    marks
                    valueLabelDisplay="auto"
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <FormControl fullWidth>
                  <FormLabel>Glass Effect Intensity: {(preferences.theme.glassIntensity * 100).toFixed(0)}%</FormLabel>
                  <Slider
                    value={preferences.theme.glassIntensity}
                    onChange={(_e, value) => handleThemeChange('glassIntensity', value as number)}
                    min={0}
                    max={0.3}
                    step={0.05}
                    valueLabelDisplay="auto"
                    valueLabelFormat={(value) => `${(value * 100).toFixed(0)}%`}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Layout Preferences Tab */}
          <TabPanel value={tabValue} index={1}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <FormControl component="fieldset" fullWidth>
                  <FormLabel component="legend">Dashboard Layout</FormLabel>
                  <RadioGroup
                    value={preferences.layout.dashboardLayout}
                    onChange={(e) => handleLayoutChange('dashboardLayout', e.target.value)}
                  >
                    <FormControlLabel value="grid" control={<Radio />} label="Grid Layout" />
                    <FormControlLabel value="list" control={<Radio />} label="List Layout" />
                    <FormControlLabel value="compact" control={<Radio />} label="Compact Layout" />
                  </RadioGroup>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <FormLabel>Sidebar Position</FormLabel>
                  <Select
                    value={preferences.layout.sidebarPosition}
                    onChange={(e) => handleLayoutChange('sidebarPosition', e.target.value)}
                  >
                    <MenuItem value="left">Left</MenuItem>
                    <MenuItem value="right">Right</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <FormControl fullWidth>
                  <FormLabel>Widget Spacing: {preferences.layout.widgetSpacing}px</FormLabel>
                  <Slider
                    value={preferences.layout.widgetSpacing}
                    onChange={(_e, value) => handleLayoutChange('widgetSpacing', value as number)}
                    min={8}
                    max={32}
                    step={4}
                    marks
                    valueLabelDisplay="auto"
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="h6" sx={{ mb: 2 }}>Display Options</Typography>
              </Grid>

              <Grid item xs={12} md={4}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={preferences.layout.sidebarCollapsed}
                      onChange={(e) => handleLayoutChange('sidebarCollapsed', e.target.checked)}
                    />
                  }
                  label="Collapse Sidebar"
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={preferences.layout.showHeader}
                      onChange={(e) => handleLayoutChange('showHeader', e.target.checked)}
                    />
                  }
                  label="Show Header"
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={preferences.layout.showFooter}
                      onChange={(e) => handleLayoutChange('showFooter', e.target.checked)}
                    />
                  }
                  label="Show Footer"
                />
              </Grid>
            </Grid>
          </TabPanel>

          {/* Widget Configuration Tab */}
          <TabPanel value={tabValue} index={2}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <FormLabel>Auto Refresh Interval: {preferences.widgets.refreshInterval / 1000}s</FormLabel>
                  <Slider
                    value={preferences.widgets.refreshInterval}
                    onChange={(_e, value) => handleWidgetChange('refreshInterval', value as number)}
                    min={5000}
                    max={120000}
                    step={5000}
                    marks={[
                      { value: 5000, label: '5s' },
                      { value: 30000, label: '30s' },
                      { value: 60000, label: '1m' },
                      { value: 120000, label: '2m' }
                    ]}
                    valueLabelDisplay="auto"
                    valueLabelFormat={(value) => `${value / 1000}s`}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={preferences.widgets.autoRefresh}
                      onChange={(e) => handleWidgetChange('autoRefresh', e.target.checked)}
                    />
                  }
                  label="Enable Auto Refresh"
                />
              </Grid>

              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="body2" sx={{ opacity: 0.7 }}>
                  Widget order and size customization coming soon...
                </Typography>
              </Grid>
            </Grid>
          </TabPanel>
        </Paper>
      </ScrollReveal>
    </Box>
  );
}

export default UserCustomizationPanel;

