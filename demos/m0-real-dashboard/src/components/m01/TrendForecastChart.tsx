/**
 * ============================================================================
 * TREND FORECAST CHART COMPONENT
 * ============================================================================
 * 
 * Purpose: 7-day performance trend forecasting visualization
 * Features: Historical data, predictions, confidence intervals
 * 
 * Author: AI Assistant (Enhancement 3.2 Implementation)
 * Created: 2026-01-03
 * ============================================================================
 */

'use client';

import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip
} from '@mui/material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Area,
  AreaChart,
  ReferenceLine
} from 'recharts';
import { TrendingUp, TrendingDown, Remove } from '@mui/icons-material';
import { IPerformanceForecast } from '@/lib/analytics-engine';

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface ITrendForecastChartProps {
  forecast: IPerformanceForecast;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export const TrendForecastChart: React.FC<ITrendForecastChartProps> = ({ forecast }) => {
  // Format data for chart
  const chartData = forecast.forecastData.map(point => ({
    date: point.date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
    actual: point.actual,
    predicted: point.predicted,
    lower: point.confidence.lower,
    upper: point.confidence.upper,
    isForecast: !point.actual
  }));

  // Get trend icon
  const getTrendIcon = () => {
    switch (forecast.trend) {
      case 'improving': return <TrendingDown sx={{ fontSize: 24, color: '#4caf50' }} />;
      case 'degrading': return <TrendingUp sx={{ fontSize: 24, color: '#f44336' }} />;
      case 'stable': return <Remove sx={{ fontSize: 24, color: '#ff9800' }} />;
    }
  };

  const getTrendColor = () => {
    switch (forecast.trend) {
      case 'improving': return '#4caf50';
      case 'degrading': return '#f44336';
      case 'stable': return '#ff9800';
    }
  };

  // Custom tooltip
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <Box
          sx={{
            background: 'rgba(0, 0, 0, 0.9)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            borderRadius: 1,
            p: 1.5
          }}
        >
          <Typography variant="caption" sx={{ color: '#fff', display: 'block', mb: 0.5 }}>
            {data.date}
          </Typography>
          {data.actual && (
            <Typography variant="body2" sx={{ color: '#2196f3', fontWeight: 600 }}>
              Actual: {data.actual.toFixed(2)}
            </Typography>
          )}
          <Typography variant="body2" sx={{ color: '#4caf50', fontWeight: 600 }}>
            {data.isForecast ? 'Forecast' : 'Predicted'}: {data.predicted.toFixed(2)}
          </Typography>
          {data.isForecast && (
            <Typography variant="caption" sx={{ color: 'rgba(255, 255, 255, 0.6)' }}>
              Range: {data.lower.toFixed(2)} - {data.upper.toFixed(2)}
            </Typography>
          )}
        </Box>
      );
    }
    return null;
  };

  return (
    <Card
      sx={{
        background: 'rgba(255, 255, 255, 0.05)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255, 255, 255, 0.1)',
        borderRadius: 2
      }}
    >
      <CardContent>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box>
            <Typography variant="h6" sx={{ color: '#fff', fontWeight: 600 }}>
              {forecast.dimensionName} - 7-Day Forecast
            </Typography>
            <Typography variant="caption" sx={{ color: 'rgba(255, 255, 255, 0.5)' }}>
              Historical data and predictive analysis
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {getTrendIcon()}
            <Box>
              <Typography variant="h6" sx={{ color: getTrendColor(), fontWeight: 600 }}>
                {forecast.predictedChange > 0 ? '+' : ''}{forecast.predictedChange.toFixed(1)}%
              </Typography>
              <Typography variant="caption" sx={{ color: 'rgba(255, 255, 255, 0.5)' }}>
                Predicted Change
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Chart */}
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={chartData}>
            <defs>
              <linearGradient id="colorActual" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#2196f3" stopOpacity={0.3}/>
                <stop offset="95%" stopColor="#2196f3" stopOpacity={0}/>
              </linearGradient>
              <linearGradient id="colorPredicted" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#4caf50" stopOpacity={0.3}/>
                <stop offset="95%" stopColor="#4caf50" stopOpacity={0}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="rgba(255, 255, 255, 0.1)" />
            <XAxis 
              dataKey="date" 
              stroke="rgba(255, 255, 255, 0.5)"
              tick={{ fill: 'rgba(255, 255, 255, 0.7)', fontSize: 12 }}
            />
            <YAxis 
              stroke="rgba(255, 255, 255, 0.5)"
              tick={{ fill: 'rgba(255, 255, 255, 0.7)', fontSize: 12 }}
            />
            <Tooltip content={<CustomTooltip />} />
            
            {/* Current value reference line */}
            <ReferenceLine 
              y={forecast.currentValue} 
              stroke="#ff9800" 
              strokeDasharray="3 3"
              label={{ value: 'Current', fill: '#ff9800', fontSize: 12 }}
            />
            
            {/* Actual data */}
            <Area 
              type="monotone" 
              dataKey="actual" 
              stroke="#2196f3" 
              strokeWidth={2}
              fill="url(#colorActual)"
            />
            
            {/* Predicted data */}
            <Line 
              type="monotone" 
              dataKey="predicted" 
              stroke="#4caf50" 
              strokeWidth={2}
              strokeDasharray="5 5"
              dot={{ fill: '#4caf50', r: 4 }}
            />
          </AreaChart>
        </ResponsiveContainer>

        {/* Trend Info */}
        <Box sx={{ mt: 2, display: 'flex', gap: 1, justifyContent: 'center' }}>
          <Chip
            label="Historical Data"
            size="small"
            sx={{
              background: 'rgba(33, 150, 243, 0.2)',
              color: '#2196f3',
              border: '1px solid rgba(33, 150, 243, 0.4)'
            }}
          />
          <Chip
            label="Forecast"
            size="small"
            sx={{
              background: 'rgba(76, 175, 80, 0.2)',
              color: '#4caf50',
              border: '1px solid rgba(76, 175, 80, 0.4)'
            }}
          />
          <Chip
            label={`Trend: ${forecast.trend.toUpperCase()}`}
            size="small"
            sx={{
              background: `${getTrendColor()}20`,
              color: getTrendColor(),
              border: `1px solid ${getTrendColor()}40`
            }}
          />
        </Box>
      </CardContent>
    </Card>
  );
};

export default TrendForecastChart;

