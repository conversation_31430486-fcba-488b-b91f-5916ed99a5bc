/**
 * ============================================================================
 * AI CONTEXT: ResponsiveContainer - M0 Demo Responsive Design Component
 * Purpose: Reusable responsive container with consistent breakpoints and styling
 * Complexity: Simple - Layout utility component
 * AI Navigation: 3 sections, responsive design domain
 * Lines: ~150 / Target limit: 300
 * Migrated: 2026-01-01 (m0-demo-dashboard → m0-real-dashboard)
 * ============================================================================
 */

import React from 'react';
import { Box, BoxProps, useTheme, useMediaQuery } from '@mui/material';

// ============================================================================
// SECTION 1: TYPE DEFINITIONS
// AI Context: Responsive container interfaces and types
// ============================================================================

export interface ResponsiveContainerProps extends Omit<BoxProps, 'sx'> {
  /** Custom spacing multiplier for padding */
  spacing?: 'compact' | 'normal' | 'spacious';
  /** Whether to add hover effects */
  interactive?: boolean;
  /** Custom breakpoint overrides */
  breakpoints?: {
    xs?: string | number;
    sm?: string | number;
    md?: string | number;
    lg?: string | number;
    xl?: string | number;
  };
  /** Additional sx props that will be merged with responsive defaults */
  sx?: BoxProps['sx'];
}

export interface ResponsiveGridProps extends Omit<BoxProps, 'sx'> {
  /** Number of columns at different breakpoints */
  columns?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  /** Gap between grid items */
  gap?: number | { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };
  /** Additional sx props */
  sx?: BoxProps['sx'];
}

// ============================================================================
// SECTION 2: RESPONSIVE CONTAINER COMPONENT
// AI Context: Main responsive container with consistent spacing and breakpoints
// ============================================================================

export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  spacing = 'normal',
  interactive = false,
  breakpoints,
  sx,
  children,
  ...props
}) => {
  const theme = useTheme();
  
  // Define spacing values
  const spacingValues = {
    compact: { xs: 1, sm: 1.5, md: 2 },
    normal: { xs: 1, sm: 2, md: 3 },
    spacious: { xs: 2, sm: 3, md: 4 }
  };

  const responsiveSx = {
    p: spacingValues[spacing],
    maxWidth: '100%',
    overflow: 'hidden',
    ...(interactive && {
      transition: 'all 0.3s ease-in-out',
      '&:hover': {
        transform: 'translateY(-1px)',
        boxShadow: 1
      }
    }),
    ...(breakpoints && {
      width: {
        xs: breakpoints.xs || '100%',
        sm: breakpoints.sm || '100%',
        md: breakpoints.md || '100%',
        lg: breakpoints.lg || '100%',
        xl: breakpoints.xl || '100%'
      }
    }),
    ...sx
  };

  return (
    <Box sx={responsiveSx} {...props}>
      {children}
    </Box>
  );
};

// ============================================================================
// SECTION 3: RESPONSIVE GRID COMPONENT
// AI Context: Grid layout component with responsive column management
// ============================================================================

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  columns = { xs: 1, sm: 2, md: 3, lg: 4 },
  gap = { xs: 2, sm: 2.5, md: 3 },
  sx,
  children,
  ...props
}) => {
  const gridSx = {
    display: 'grid',
    gridTemplateColumns: {
      xs: `repeat(${columns.xs || 1}, 1fr)`,
      sm: `repeat(${columns.sm || 2}, 1fr)`,
      md: `repeat(${columns.md || 3}, 1fr)`,
      lg: `repeat(${columns.lg || 4}, 1fr)`,
      xl: `repeat(${columns.xl || columns.lg || 4}, 1fr)`
    },
    gap: typeof gap === 'number' ? gap : {
      xs: gap.xs || 2,
      sm: gap.sm || 2.5,
      md: gap.md || 3,
      lg: gap.lg || gap.md || 3,
      xl: gap.xl || gap.lg || gap.md || 3
    },
    ...sx
  };

  return (
    <Box sx={gridSx} {...props}>
      {children}
    </Box>
  );
};

// ============================================================================
// RESPONSIVE HOOKS AND UTILITIES
// AI Context: Utility hooks for responsive behavior
// ============================================================================

export const useResponsiveBreakpoints = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));
  const isDesktop = useMediaQuery(theme.breakpoints.up('md'));
  const isLargeDesktop = useMediaQuery(theme.breakpoints.up('lg'));

  return {
    isMobile,
    isTablet,
    isDesktop,
    isLargeDesktop,
    currentBreakpoint: isMobile ? 'xs' : isTablet ? 'sm' : isDesktop ? 'md' : 'lg'
  };
};

export const getResponsiveSpacing = (
  base: number,
  multipliers: { xs?: number; sm?: number; md?: number; lg?: number } = {}
) => ({
  xs: base * (multipliers.xs || 0.5),
  sm: base * (multipliers.sm || 0.75),
  md: base * (multipliers.md || 1),
  lg: base * (multipliers.lg || 1.25)
});

export const getResponsiveFontSize = (
  base: string,
  adjustments: { xs?: string; sm?: string; md?: string; lg?: string } = {}
) => ({
  xs: adjustments.xs || '0.875rem',
  sm: adjustments.sm || '1rem',
  md: adjustments.md || base,
  lg: adjustments.lg || base
});

// Default export
export default ResponsiveContainer;

