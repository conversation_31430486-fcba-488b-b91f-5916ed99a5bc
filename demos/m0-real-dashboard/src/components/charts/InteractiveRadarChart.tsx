/**
 * ============================================================================
 * Interactive Radar Chart Component
 * ============================================================================
 * 
 * Purpose: Advanced radar chart for multi-dimensional data visualization
 * Features: Interactive tooltips, gradient fills, responsive design
 * 
 * This component provides an enterprise-grade radar chart visualization
 * perfect for comparing multiple dimensions or metrics.
 * 
 * Author: AI Assistant (Enhancement 1.3 Implementation)
 * Created: 2026-01-03
 * ============================================================================
 */

'use client';

import React from 'react';
import {
  RadarChart,
  Radar,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { useTheme } from '@mui/material';

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export interface DataPoint {
  subject: string;
  value: number;
  [key: string]: string | number;
}

export interface InteractiveRadarChartProps {
  data: DataPoint[];
  dataKeys: string[];
  colors?: string[];
  height?: number;
  showLegend?: boolean;
  animationDuration?: number;
  title?: string;
  fillOpacity?: number;
}

// ============================================================================
// CUSTOM TOOLTIP COMPONENT
// ============================================================================

interface CustomTooltipProps {
  active?: boolean;
  payload?: any[];
  label?: string;
}

const CustomTooltip: React.FC<CustomTooltipProps> = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    return (
      <div
        style={{
          backgroundColor: 'rgba(0, 0, 0, 0.9)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          borderRadius: '8px',
          padding: '12px',
          backdropFilter: 'blur(10px)'
        }}
      >
        <p style={{ color: '#fff', fontWeight: 'bold', marginBottom: '8px' }}>
          {label}
        </p>
        {payload.map((entry, index) => (
          <p
            key={`item-${index}`}
            style={{
              color: entry.color,
              margin: '4px 0',
              fontSize: '0.875rem'
            }}
          >
            {entry.name}: {typeof entry.value === 'number' ? entry.value.toFixed(2) : entry.value}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function InteractiveRadarChart({
  data,
  dataKeys,
  colors = ['#2196f3', '#4caf50', '#ff9800', '#f44336', '#9c27b0'],
  height = 400,
  showLegend = true,
  animationDuration = 1000,
  title,
  fillOpacity = 0.6
}: InteractiveRadarChartProps) {
  const theme = useTheme();
  const isDark = theme.palette.mode === 'dark';

  const gridColor = isDark ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)';
  const textColor = isDark ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)';

  return (
    <div style={{ width: '100%' }}>
      {title && (
        <h3 style={{ color: textColor, marginBottom: '16px', fontSize: '1.1rem', fontWeight: 'bold', textAlign: 'center' }}>
          {title}
        </h3>
      )}
      <ResponsiveContainer width="100%" height={height}>
        <RadarChart data={data}>
          <PolarGrid stroke={gridColor} />
          <PolarAngleAxis
            dataKey="subject"
            stroke={textColor}
            style={{ fontSize: '0.75rem' }}
          />
          <PolarRadiusAxis
            angle={90}
            domain={[0, 100]}
            stroke={textColor}
            style={{ fontSize: '0.7rem' }}
          />
          <Tooltip content={<CustomTooltip />} />
          {showLegend && <Legend wrapperStyle={{ fontSize: '0.875rem', color: textColor }} />}
          {dataKeys.map((key, index) => (
            <Radar
              key={key}
              name={key}
              dataKey={key}
              stroke={colors[index % colors.length]}
              fill={colors[index % colors.length]}
              fillOpacity={fillOpacity}
              animationDuration={animationDuration}
            />
          ))}
        </RadarChart>
      </ResponsiveContainer>
    </div>
  );
}

