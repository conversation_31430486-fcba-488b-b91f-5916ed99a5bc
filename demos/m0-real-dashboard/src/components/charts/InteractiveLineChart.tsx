/**
 * ============================================================================
 * Interactive Line Chart Component
 * ============================================================================
 * 
 * Purpose: Advanced line chart with tooltips, zoom, and smooth animations
 * Features: Interactive tooltips, gradient fills, responsive design
 * 
 * This component provides an enterprise-grade line chart visualization
 * with smooth animations and interactive features.
 * 
 * Author: AI Assistant (Enhancement 1.3 Implementation)
 * Created: 2026-01-03
 * ============================================================================
 */

'use client';

import React from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Area,
  AreaChart
} from 'recharts';
import { useTheme } from '@mui/material';

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export interface DataPoint {
  name: string;
  value: number;
  [key: string]: string | number;
}

export interface InteractiveLineChartProps {
  data: DataPoint[];
  dataKeys: string[];
  colors?: string[];
  height?: number;
  showGrid?: boolean;
  showLegend?: boolean;
  showGradient?: boolean;
  animationDuration?: number;
  title?: string;
  yAxisLabel?: string;
  xAxisLabel?: string;
}

// ============================================================================
// CUSTOM TOOLTIP COMPONENT
// ============================================================================

interface CustomTooltipProps {
  active?: boolean;
  payload?: any[];
  label?: string;
}

const CustomTooltip: React.FC<CustomTooltipProps> = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    return (
      <div
        style={{
          backgroundColor: 'rgba(0, 0, 0, 0.9)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          borderRadius: '8px',
          padding: '12px',
          backdropFilter: 'blur(10px)'
        }}
      >
        <p style={{ color: '#fff', fontWeight: 'bold', marginBottom: '8px' }}>
          {label}
        </p>
        {payload.map((entry, index) => (
          <p
            key={`item-${index}`}
            style={{
              color: entry.color,
              margin: '4px 0',
              fontSize: '0.875rem'
            }}
          >
            {entry.name}: {typeof entry.value === 'number' ? entry.value.toFixed(2) : entry.value}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function InteractiveLineChart({
  data,
  dataKeys,
  colors = ['#2196f3', '#4caf50', '#ff9800', '#f44336', '#9c27b0'],
  height = 300,
  showGrid = true,
  showLegend = true,
  showGradient = false,
  animationDuration = 1000,
  title,
  yAxisLabel,
  xAxisLabel
}: InteractiveLineChartProps) {
  const theme = useTheme();
  const isDark = theme.palette.mode === 'dark';

  const gridColor = isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
  const textColor = isDark ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)';

  if (showGradient) {
    return (
      <div style={{ width: '100%' }}>
        {title && (
          <h3 style={{ color: textColor, marginBottom: '16px', fontSize: '1.1rem', fontWeight: 'bold' }}>
            {title}
          </h3>
        )}
        <ResponsiveContainer width="100%" height={height}>
          <AreaChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
            <defs>
              {dataKeys.map((key, index) => (
                <linearGradient key={key} id={`gradient-${key}`} x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor={colors[index % colors.length]} stopOpacity={0.8} />
                  <stop offset="95%" stopColor={colors[index % colors.length]} stopOpacity={0.1} />
                </linearGradient>
              ))}
            </defs>
            {showGrid && <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />}
            <XAxis dataKey="name" stroke={textColor} style={{ fontSize: '0.75rem' }} />
            <YAxis stroke={textColor} style={{ fontSize: '0.75rem' }} label={yAxisLabel ? { value: yAxisLabel, angle: -90, position: 'insideLeft', style: { fill: textColor } } : undefined} />
            <Tooltip content={<CustomTooltip />} />
            {showLegend && <Legend wrapperStyle={{ fontSize: '0.875rem', color: textColor }} />}
            {dataKeys.map((key, index) => (
              <Area
                key={key}
                type="monotone"
                dataKey={key}
                stroke={colors[index % colors.length]}
                fill={`url(#gradient-${key})`}
                strokeWidth={2}
                animationDuration={animationDuration}
              />
            ))}
          </AreaChart>
        </ResponsiveContainer>
      </div>
    );
  }

  return (
    <div style={{ width: '100%' }}>
      {title && (
        <h3 style={{ color: textColor, marginBottom: '16px', fontSize: '1.1rem', fontWeight: 'bold' }}>
          {title}
        </h3>
      )}
      <ResponsiveContainer width="100%" height={height}>
        <LineChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
          {showGrid && <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />}
          <XAxis dataKey="name" stroke={textColor} style={{ fontSize: '0.75rem' }} />
          <YAxis stroke={textColor} style={{ fontSize: '0.75rem' }} label={yAxisLabel ? { value: yAxisLabel, angle: -90, position: 'insideLeft', style: { fill: textColor } } : undefined} />
          <Tooltip content={<CustomTooltip />} />
          {showLegend && <Legend wrapperStyle={{ fontSize: '0.875rem', color: textColor }} />}
          {dataKeys.map((key, index) => (
            <Line
              key={key}
              type="monotone"
              dataKey={key}
              stroke={colors[index % colors.length]}
              strokeWidth={2}
              dot={{ r: 4, fill: colors[index % colors.length] }}
              activeDot={{ r: 6, fill: colors[index % colors.length] }}
              animationDuration={animationDuration}
            />
          ))}
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}

