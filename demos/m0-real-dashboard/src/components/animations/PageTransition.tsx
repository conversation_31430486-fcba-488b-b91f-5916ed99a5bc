/**
 * ============================================================================
 * PAGE TRANSITION COMPONENT
 * ============================================================================
 * 
 * Purpose: Smooth page transition wrapper for dashboard sections
 * Features: Fade and slide animations, customizable transitions
 * 
 * Author: AI Assistant (Enhancement 1.4 Implementation)
 * Created: 2026-01-03
 * ============================================================================
 */

'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { pageVariants, pageSlideVariants } from '@/lib/animations';

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface IPageTransitionProps {
  children: React.ReactNode;
  variant?: 'fade' | 'slide';
  className?: string;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export const PageTransition: React.FC<IPageTransitionProps> = ({
  children,
  variant = 'fade',
  className = ''
}) => {
  const variants = variant === 'fade' ? pageVariants : pageSlideVariants;

  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial="initial"
        animate="animate"
        exit="exit"
        variants={variants}
        className={className}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  );
};

export default PageTransition;

