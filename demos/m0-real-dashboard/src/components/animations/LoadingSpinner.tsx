/**
 * ============================================================================
 * LOADING SPINNER COMPONENT
 * ============================================================================
 * 
 * Purpose: Animated loading spinner for data fetching states
 * Features: Smooth rotation animation, customizable size and color
 * 
 * Author: AI Assistant (Enhancement 1.4 Implementation)
 * Created: 2026-01-03
 * ============================================================================
 */

'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { spinnerVariants } from '@/lib/animations';

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface ILoadingSpinnerProps {
  size?: number;
  color?: string;
  thickness?: number;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export const LoadingSpinner: React.FC<ILoadingSpinnerProps> = ({
  size = 40,
  color = '#2196f3',
  thickness = 4
}) => {
  return (
    <motion.div
      style={{
        width: size,
        height: size,
        border: `${thickness}px solid rgba(255, 255, 255, 0.1)`,
        borderTop: `${thickness}px solid ${color}`,
        borderRadius: '50%'
      }}
      variants={spinnerVariants}
      animate="spin"
    />
  );
};

export default LoadingSpinner;

