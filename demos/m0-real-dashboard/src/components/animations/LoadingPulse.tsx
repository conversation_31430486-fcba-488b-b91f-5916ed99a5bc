/**
 * ============================================================================
 * LOADING PULSE COMPONENT
 * ============================================================================
 * 
 * Purpose: Animated pulse effect for loading states
 * Features: Smooth pulse animation, customizable appearance
 * 
 * Author: AI Assistant (Enhancement 1.4 Implementation)
 * Created: 2026-01-03
 * ============================================================================
 */

'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { pulseVariants } from '@/lib/animations';

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface ILoadingPulseProps {
  children: React.ReactNode;
  className?: string;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export const LoadingPulse: React.FC<ILoadingPulseProps> = ({
  children,
  className = ''
}) => {
  return (
    <motion.div
      variants={pulseVariants}
      animate="pulse"
      className={className}
    >
      {children}
    </motion.div>
  );
};

export default LoadingPulse;

