/**
 * ============================================================================
 * STAGGER CONTAINER COMPONENT
 * ============================================================================
 * 
 * Purpose: Container for staggered child animations
 * Features: Stagger delay, customizable timing
 * 
 * Author: AI Assistant (Enhancement 1.4 Implementation)
 * Created: 2026-01-03
 * ============================================================================
 */

'use client';

import React from 'react';
import { motion } from 'framer-motion';
import {
  staggerContainerVariants,
  fastStaggerContainerVariants,
  slowStaggerContainerVariants,
  staggerItemVariants
} from '@/lib/animations';

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface IStaggerContainerProps {
  children: React.ReactNode;
  speed?: 'fast' | 'normal' | 'slow';
  className?: string;
}

interface IStaggerItemProps {
  children: React.ReactNode;
  className?: string;
}

// ============================================================================
// STAGGER CONTAINER
// ============================================================================

export const StaggerContainer: React.FC<IStaggerContainerProps> = ({
  children,
  speed = 'normal',
  className = ''
}) => {
  const variants =
    speed === 'fast'
      ? fastStaggerContainerVariants
      : speed === 'slow'
      ? slowStaggerContainerVariants
      : staggerContainerVariants;

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={variants}
      className={className}
    >
      {children}
    </motion.div>
  );
};

// ============================================================================
// STAGGER ITEM
// ============================================================================

export const StaggerItem: React.FC<IStaggerItemProps> = ({
  children,
  className = ''
}) => {
  return (
    <motion.div variants={staggerItemVariants} className={className}>
      {children}
    </motion.div>
  );
};

export default StaggerContainer;

