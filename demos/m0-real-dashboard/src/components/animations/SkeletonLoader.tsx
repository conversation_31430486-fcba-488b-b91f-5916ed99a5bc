/**
 * ============================================================================
 * SKELETON LOADER COMPONENT
 * ============================================================================
 * 
 * Purpose: Animated skeleton loader for content placeholders
 * Features: Smooth pulse animation, customizable dimensions
 * 
 * Author: AI Assistant (Enhancement 1.4 Implementation)
 * Created: 2026-01-03
 * ============================================================================
 */

'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { skeletonVariants } from '@/lib/animations';
import { Box } from '@mui/material';

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface ISkeletonLoaderProps {
  width?: string | number;
  height?: string | number;
  borderRadius?: string | number;
  className?: string;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export const SkeletonLoader: React.FC<ISkeletonLoaderProps> = ({
  width = '100%',
  height = 20,
  borderRadius = 4,
  className = ''
}) => {
  return (
    <motion.div
      variants={skeletonVariants}
      animate="pulse"
      className={className}
    >
      <Box
        sx={{
          width,
          height,
          borderRadius,
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(10px)'
        }}
      />
    </motion.div>
  );
};

export default SkeletonLoader;

