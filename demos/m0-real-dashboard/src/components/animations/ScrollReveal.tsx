/**
 * ============================================================================
 * SCROLL REVEAL COMPONENT
 * ============================================================================
 * 
 * Purpose: Animate elements when they scroll into view
 * Features: Scroll-triggered animations, customizable variants
 * 
 * Author: AI Assistant (Enhancement 1.4 Implementation)
 * Created: 2026-01-03
 * ============================================================================
 */

'use client';

import React from 'react';
import { motion, useInView } from 'framer-motion';
import { scrollRevealVariants } from '@/lib/animations';

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface IScrollRevealProps {
  children: React.ReactNode;
  delay?: number;
  once?: boolean;
  amount?: number;
  className?: string;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export const ScrollReveal: React.FC<IScrollRevealProps> = ({
  children,
  delay = 0,
  once = true,
  amount = 0.3,
  className = ''
}) => {
  const ref = React.useRef(null);
  const isInView = useInView(ref, { once, amount });

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={isInView ? 'visible' : 'hidden'}
      variants={scrollRevealVariants}
      transition={{
        delay,
        duration: 0.6,
        ease: 'easeOut'
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
};

export default ScrollReveal;

