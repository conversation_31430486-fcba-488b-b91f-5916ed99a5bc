/**
 * ============================================================================
 * ANIMATED BUTTON COMPONENT
 * ============================================================================
 * 
 * Purpose: Reusable animated button with hover and tap effects
 * Features: Micro-interactions, customizable animations
 * 
 * Author: AI Assistant (Enhancement 1.4 Implementation)
 * Created: 2026-01-03
 * ============================================================================
 */

'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Button, ButtonProps } from '@mui/material';
import { buttonHoverVariants, buttonTapVariants } from '@/lib/animations';

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface IAnimatedButtonProps extends Omit<ButtonProps, 'component'> {
  enableHover?: boolean;
  enableTap?: boolean;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export const AnimatedButton: React.FC<IAnimatedButtonProps> = ({
  children,
  enableHover = true,
  enableTap = true,
  ...buttonProps
}) => {
  const MotionButton = motion(Button);

  return (
    <MotionButton
      whileHover={enableHover ? buttonHoverVariants.hover : undefined}
      whileTap={enableTap ? buttonTapVariants.tap : undefined}
      {...buttonProps}
    >
      {children}
    </MotionButton>
  );
};

export default AnimatedButton;

