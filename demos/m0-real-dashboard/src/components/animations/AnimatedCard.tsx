/**
 * ============================================================================
 * ANIMATED CARD COMPONENT
 * ============================================================================
 * 
 * Purpose: Reusable animated card wrapper with hover and tap effects
 * Features: Entrance animation, hover effects, tap feedback
 * 
 * Author: AI Assistant (Enhancement 1.4 Implementation)
 * Created: 2026-01-03
 * ============================================================================
 */

'use client';

import React from 'react';
import { motion, MotionProps } from 'framer-motion';
import {
  fadeInScaleVariants,
  cardHoverVariants,
  cardTapVariants
} from '@/lib/animations';

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface IAnimatedCardProps extends Omit<MotionProps, 'variants'> {
  children: React.ReactNode;
  delay?: number;
  enableHover?: boolean;
  enableTap?: boolean;
  className?: string;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export const AnimatedCard: React.FC<IAnimatedCardProps> = ({
  children,
  delay = 0,
  enableHover = true,
  enableTap = true,
  className = '',
  ...motionProps
}) => {
  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={fadeInScaleVariants}
      transition={{
        delay,
        duration: 0.3,
        ease: 'easeOut'
      }}
      whileHover={enableHover ? cardHoverVariants.hover : undefined}
      whileTap={enableTap ? cardTapVariants.tap : undefined}
      className={className}
      {...motionProps}
    >
      {children}
    </motion.div>
  );
};

export default AnimatedCard;

