/**
 * ============================================================================
 * DEPENDENCY GRAPH COMPONENT
 * ============================================================================
 *
 * Visualizes component dependencies and relationships
 *
 * Features:
 * - Component dependency visualization
 * - Status-based color coding
 * - Interactive node display
 * - Dependency type indicators
 * - Responsive layout
 *
 * Author: AI Assistant (Phase 3D Implementation)
 * Created: 2025-10-22
 * Authority: President & CEO, E.Z. Consultancy
 * ============================================================================
 */

'use client';

import React from 'react';
import { GitBranch, ArrowRight, Circle } from 'lucide-react';
import type { IIntegrationComponent } from '../../types/integration-types';

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface IDependencyGraphProps {
  components: IIntegrationComponent[];
}

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

interface IDependencyNode {
  id: string;
  name: string;
  status: string;
  type: string;
  dependencies: string[];
}

// ============================================================================
// COMPONENT IMPLEMENTATION
// ============================================================================

export function DependencyGraph({ components }: IDependencyGraphProps): React.ReactElement {
  /**
   * Build dependency nodes from components
   */
  const buildDependencyNodes = (): IDependencyNode[] => {
    return components.slice(0, 10).map(component => ({
      id: component.id,
      name: component.name,
      status: component.status,
      type: component.integrationType,
      dependencies: component.dependencies || [],
    }));
  };

  const nodes = buildDependencyNodes();

  /**
   * Get status color
   */
  const getStatusColor = (status: string): string => {
    const colors: Record<string, string> = {
      healthy: 'bg-green-500',
      warning: 'bg-yellow-500',
      error: 'bg-red-500',
      offline: 'bg-gray-500',
    };
    return colors[status] || 'bg-gray-500';
  };

  /**
   * Get type color
   */
  const getTypeColor = (type: string): string => {
    const colors: Record<string, string> = {
      bridge: 'border-blue-500 text-blue-700 dark:text-blue-300',
      coordinator: 'border-purple-500 text-purple-700 dark:text-purple-300',
      monitor: 'border-green-500 text-green-700 dark:text-green-300',
      validator: 'border-orange-500 text-orange-700 dark:text-orange-300',
    };
    return colors[type] || 'border-gray-500 text-gray-700 dark:text-gray-300';
  };

  /**
   * Get type badge color
   */
  const getTypeBadgeColor = (type: string): string => {
    const colors: Record<string, string> = {
      bridge: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
      coordinator: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
      monitor: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
      validator: 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300',
    };
    return colors[type] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
  };

  /**
   * Find component by ID
   */
  const findComponent = (id: string): IDependencyNode | undefined => {
    return nodes.find(node => node.id === id);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-2">
          <GitBranch className="w-5 h-5 text-purple-600" />
          Dependency Graph
        </h2>
        <div className="text-sm text-gray-600 dark:text-gray-400">
          {nodes.length} components
        </div>
      </div>

      {/* Legend */}
      <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
        <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Component Types
        </div>
        <div className="flex flex-wrap gap-3">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-blue-500" />
            <span className="text-xs text-gray-600 dark:text-gray-400">Bridge</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-purple-500" />
            <span className="text-xs text-gray-600 dark:text-gray-400">Coordinator</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-green-500" />
            <span className="text-xs text-gray-600 dark:text-gray-400">Monitor</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-orange-500" />
            <span className="text-xs text-gray-600 dark:text-gray-400">Validator</span>
          </div>
        </div>
      </div>

      {/* Dependency Graph */}
      <div className="space-y-4 max-h-96 overflow-y-auto">
        {nodes.map(node => (
          <div key={node.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            {/* Node Header */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-3">
                {/* Status Indicator */}
                <div className={`w-3 h-3 rounded-full ${getStatusColor(node.status)}`} />

                {/* Node Name */}
                <div>
                  <div className="font-semibold text-gray-900 dark:text-white">
                    {node.name}
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    {node.id}
                  </div>
                </div>
              </div>

              {/* Type Badge */}
              <span className={`text-xs px-2 py-1 rounded-full ${getTypeBadgeColor(node.type)}`}>
                {node.type}
              </span>
            </div>

            {/* Dependencies */}
            {node.dependencies.length > 0 && (
              <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Dependencies ({node.dependencies.length})
                </div>
                <div className="space-y-2">
                  {node.dependencies.map((depId, index) => {
                    const depComponent = findComponent(depId);
                    return (
                      <div
                        key={index}
                        className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400"
                      >
                        <ArrowRight className="w-4 h-4 text-gray-400" />
                        <Circle className={`w-2 h-2 ${depComponent ? getStatusColor(depComponent.status) : 'bg-gray-400'}`} />
                        <span>{depComponent?.name || depId}</span>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* No Dependencies */}
            {node.dependencies.length === 0 && (
              <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                <div className="text-xs text-gray-500 dark:text-gray-500 italic">
                  No dependencies
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Summary */}
      <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
              {nodes.filter(n => n.type === 'bridge').length}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">Bridges</div>
          </div>
          <div>
            <div className="text-lg font-bold text-purple-600 dark:text-purple-400">
              {nodes.filter(n => n.type === 'coordinator').length}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">Coordinators</div>
          </div>
          <div>
            <div className="text-lg font-bold text-green-600 dark:text-green-400">
              {nodes.filter(n => n.type === 'monitor').length}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">Monitors</div>
          </div>
          <div>
            <div className="text-lg font-bold text-orange-600 dark:text-orange-400">
              {nodes.filter(n => n.type === 'validator').length}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">Validators</div>
          </div>
        </div>
      </div>
    </div>
  );
}

