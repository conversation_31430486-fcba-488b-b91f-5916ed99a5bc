/**
 * ============================================================================
 * TEST EXECUTION PANEL COMPONENT
 * ============================================================================
 *
 * Controls for executing integration operations
 *
 * Features:
 * - Three operation buttons (bridge-test, coordination-check, integration-health)
 * - Operation status indicators
 * - Loading states
 * - Disabled states during execution
 *
 * Author: AI Assistant (Phase 3D Implementation)
 * Created: 2025-10-22
 * Authority: President & CEO, E.Z. Consultancy
 * ============================================================================
 */

'use client';

import React from 'react';
import { Link, GitBranch, Activity, Loader, CheckCircle, XCircle, Circle } from 'lucide-react';
import type { TIntegrationOperation, TOperationStatus } from '../../types/integration-types';

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface ITestExecutionPanelProps {
  onExecute: (operation: TIntegrationOperation) => Promise<void>;
  operationStatus: TOperationStatus;
  currentOperation?: TIntegrationOperation;
}

// ============================================================================
// OPERATION DEFINITIONS
// ============================================================================

interface IOperationDefinition {
  operation: TIntegrationOperation;
  label: string;
  description: string;
  icon: React.ReactElement;
  color: string;
}

const OPERATIONS: IOperationDefinition[] = [
  {
    operation: 'bridge-test',
    label: 'Bridge Test',
    description: 'Test bridge connectivity and message flow',
    icon: <Link className="w-5 h-5" />,
    color: 'blue',
  },
  {
    operation: 'coordination-check',
    label: 'Coordination Check',
    description: 'Check component coordination and synchronization',
    icon: <GitBranch className="w-5 h-5" />,
    color: 'purple',
  },
  {
    operation: 'integration-health',
    label: 'Integration Health',
    description: 'Analyze overall integration health',
    icon: <Activity className="w-5 h-5" />,
    color: 'green',
  },
];

// ============================================================================
// COMPONENT IMPLEMENTATION
// ============================================================================

export function TestExecutionPanel({
  onExecute,
  operationStatus,
  currentOperation,
}: ITestExecutionPanelProps): React.ReactElement {
  /**
   * Get button color classes
   */
  const getButtonColor = (color: string): string => {
    const colors: Record<string, string> = {
      blue: 'bg-blue-600 hover:bg-blue-700 text-white',
      purple: 'bg-purple-600 hover:bg-purple-700 text-white',
      green: 'bg-green-600 hover:bg-green-700 text-white',
    };
    return colors[color] || 'bg-gray-600 hover:bg-gray-700 text-white';
  };

  /**
   * Get status icon
   */
  const getStatusIcon = (status: TOperationStatus): React.ReactElement => {
    switch (status) {
      case 'running':
        return <Loader className="w-4 h-4 text-blue-600 animate-spin" />;
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-600" />;
      default:
        return <Circle className="w-4 h-4 text-gray-400" />;
    }
  };

  /**
   * Get status text
   */
  const getStatusText = (status: TOperationStatus): string => {
    const texts: Record<TOperationStatus, string> = {
      idle: 'Ready',
      running: 'Running...',
      success: 'Success',
      error: 'Error',
    };
    return texts[status] || 'Unknown';
  };

  /**
   * Check if operation is disabled
   */
  const isOperationDisabled = (operation: TIntegrationOperation): boolean => {
    return operationStatus === 'running';
  };

  /**
   * Check if operation is currently running
   */
  const isOperationRunning = (operation: TIntegrationOperation): boolean => {
    return operationStatus === 'running' && currentOperation === operation;
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      {/* Header */}
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Integration Operations
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Execute integration tests and health checks
        </p>
      </div>

      {/* Status Indicator */}
      <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getStatusIcon(operationStatus)}
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Status: {getStatusText(operationStatus)}
            </span>
          </div>
          {currentOperation && operationStatus === 'running' && (
            <span className="text-xs text-gray-600 dark:text-gray-400">
              Running {currentOperation}...
            </span>
          )}
        </div>
      </div>

      {/* Operation Buttons */}
      <div className="space-y-4">
        {OPERATIONS.map(({ operation, label, description, icon, color }) => {
          const isDisabled = isOperationDisabled(operation);
          const isRunning = isOperationRunning(operation);

          return (
            <div
              key={operation}
              className="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
            >
              {/* Operation Info */}
              <div className="mb-3">
                <div className="flex items-center gap-2 mb-1">
                  {icon}
                  <h3 className="font-semibold text-gray-900 dark:text-white">
                    {label}
                  </h3>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {description}
                </p>
              </div>

              {/* Execute Button */}
              <button
                onClick={() => onExecute(operation)}
                disabled={isDisabled}
                className={`w-full flex items-center justify-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                  isDisabled
                    ? 'bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                    : getButtonColor(color)
                }`}
              >
                {isRunning ? (
                  <>
                    <Loader className="w-4 h-4 animate-spin" />
                    Running...
                  </>
                ) : (
                  <>
                    {icon}
                    Execute {label}
                  </>
                )}
              </button>
            </div>
          );
        })}
      </div>

      {/* Help Text */}
      <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <div className="text-sm text-blue-800 dark:text-blue-300">
          <strong>Tip:</strong> Operations test different aspects of integration health.
          Run them periodically to ensure system stability.
        </div>
      </div>
    </div>
  );
}

