/**
 * ============================================================================
 * INTEGRATION OVERVIEW PANEL COMPONENT
 * ============================================================================
 *
 * Displays high-level integration metrics and status overview
 *
 * Features:
 * - Total integration components count
 * - Healthy/error component breakdown
 * - Integration health percentage with progress bar
 * - Key integration metrics (bridges, throughput, calls)
 * - Responsive design
 *
 * Author: AI Assistant (Phase 3D Implementation)
 * Created: 2025-10-22
 * Authority: President & CEO, E.Z. Consultancy
 * ============================================================================
 */

'use client';

import React from 'react';
import { Activity, Link, TrendingUp, GitBranch } from 'lucide-react';
import type { IIntegrationData } from '../../types/integration-types';

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface IIntegrationOverviewPanelProps {
  data: IIntegrationData;
}

// ============================================================================
// COMPONENT IMPLEMENTATION
// ============================================================================

export function IntegrationOverviewPanel({ data }: IIntegrationOverviewPanelProps): React.ReactElement {
  const {
    totalIntegrationComponents,
    healthyComponents,
    errorComponents,
    metrics
  } = data;

  // Calculate health percentage
  const healthPercentage = totalIntegrationComponents > 0
    ? Math.round((healthyComponents / totalIntegrationComponents) * 100)
    : 0;

  // Determine health status color
  const getHealthColor = (): string => {
    if (healthPercentage >= 80) return 'text-green-600';
    if (healthPercentage >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getHealthBgColor = (): string => {
    if (healthPercentage >= 80) return 'bg-green-600';
    if (healthPercentage >= 60) return 'bg-yellow-600';
    return 'bg-red-600';
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-2">
          <Activity className="w-5 h-5 text-purple-600" />
          Integration Overview
        </h2>
      </div>

      {/* Component Counts */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        {/* Total Components */}
        <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
          <div className="text-sm text-purple-600 dark:text-purple-400 mb-1">
            Total Components
          </div>
          <div className="text-2xl font-bold text-purple-900 dark:text-purple-100">
            {totalIntegrationComponents}
          </div>
        </div>

        {/* Healthy Components */}
        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
          <div className="text-sm text-green-600 dark:text-green-400 mb-1">
            Healthy
          </div>
          <div className="text-2xl font-bold text-green-900 dark:text-green-100">
            {healthyComponents}
          </div>
        </div>

        {/* Error Components */}
        <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
          <div className="text-sm text-red-600 dark:text-red-400 mb-1">
            Errors
          </div>
          <div className="text-2xl font-bold text-red-900 dark:text-red-100">
            {errorComponents}
          </div>
        </div>
      </div>

      {/* Health Progress Bar */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Integration Health
          </span>
          <span className={`text-sm font-bold ${getHealthColor()}`}>
            {healthPercentage}%
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
          <div
            className={`${getHealthBgColor()} h-3 rounded-full transition-all duration-500`}
            style={{ width: `${healthPercentage}%` }}
          />
        </div>
      </div>

      {/* Integration Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Active Bridges */}
        <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
            <Link className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <div className="text-xs text-gray-600 dark:text-gray-400">
              Active Bridges
            </div>
            <div className="text-lg font-semibold text-gray-900 dark:text-white">
              {metrics.activeBridges}
            </div>
          </div>
        </div>

        {/* Messages Throughput */}
        <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
          <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
            <TrendingUp className="w-5 h-5 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <div className="text-xs text-gray-600 dark:text-gray-400">
              Throughput
            </div>
            <div className="text-lg font-semibold text-gray-900 dark:text-white">
              {formatThroughput(metrics.messagesThroughput)}
            </div>
          </div>
        </div>

        {/* Cross-Component Calls */}
        <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
          <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
            <GitBranch className="w-5 h-5 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <div className="text-xs text-gray-600 dark:text-gray-400">
              Cross-Component Calls
            </div>
            <div className="text-lg font-semibold text-gray-900 dark:text-white">
              {formatNumber(metrics.crossComponentCalls)}
            </div>
          </div>
        </div>

        {/* Integration Health Score */}
        <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
          <div className="p-2 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
            <Activity className="w-5 h-5 text-orange-600 dark:text-orange-400" />
          </div>
          <div>
            <div className="text-xs text-gray-600 dark:text-gray-400">
              Health Score
            </div>
            <div className="text-lg font-semibold text-gray-900 dark:text-white">
              {metrics.integrationHealth}%
            </div>
          </div>
        </div>
      </div>

      {/* Last Integration Test */}
      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="text-xs text-gray-600 dark:text-gray-400">
          Last Integration Test: {formatTimestamp(metrics.lastIntegrationTest)}
        </div>
      </div>
    </div>
  );
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Format throughput for display
 */
function formatThroughput(throughput: number): string {
  if (throughput >= 1000000) {
    return `${(throughput / 1000000).toFixed(1)}M/min`;
  }
  if (throughput >= 1000) {
    return `${(throughput / 1000).toFixed(1)}K/min`;
  }
  return `${throughput}/min`;
}

/**
 * Format number with commas
 */
function formatNumber(num: number): string {
  return num.toLocaleString();
}

/**
 * Format timestamp for display
 */
function formatTimestamp(timestamp: string): string {
  const date = new Date(timestamp);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMins = Math.floor(diffMs / 60000);

  if (diffMins < 1) return 'just now';
  if (diffMins === 1) return '1 minute ago';
  if (diffMins < 60) return `${diffMins} minutes ago`;

  const diffHours = Math.floor(diffMins / 60);
  if (diffHours === 1) return '1 hour ago';
  if (diffHours < 24) return `${diffHours} hours ago`;

  return date.toLocaleString();
}

