/**
 * ============================================================================
 * INTEGRATION STATUS GRID COMPONENT
 * ============================================================================
 *
 * Displays grid of integration components with filtering
 *
 * Features:
 * - Filterable grid by type and status
 * - Component cards with health scores
 * - Status indicators
 * - Integration type badges
 * - Responsive grid layout
 *
 * Author: AI Assistant (Phase 3D Implementation)
 * Created: 2025-10-22
 * Authority: President & CEO, E.Z. Consultancy
 * ============================================================================
 */

'use client';

import React, { useState, useMemo } from 'react';
import { Filter, Link, GitBranch, Activity, Shield } from 'lucide-react';
import type { IIntegrationComponent, TIntegrationType } from '../../types/integration-types';

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface IIntegrationStatusGridProps {
  components: IIntegrationComponent[];
}

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

type TFilterType = 'all' | TIntegrationType;
type TFilterStatus = 'all' | 'healthy' | 'warning' | 'error' | 'offline';

// ============================================================================
// COMPONENT IMPLEMENTATION
// ============================================================================

export function IntegrationStatusGrid({ components }: IIntegrationStatusGridProps): React.ReactElement {
  const [filterType, setFilterType] = useState<TFilterType>('all');
  const [filterStatus, setFilterStatus] = useState<TFilterStatus>('all');

  /**
   * Filter components based on selected filters
   */
  const filteredComponents = useMemo(() => {
    return components.filter(component => {
      const typeMatch = filterType === 'all' || component.integrationType === filterType;
      const statusMatch = filterStatus === 'all' || component.status === filterStatus;
      return typeMatch && statusMatch;
    });
  }, [components, filterType, filterStatus]);

  /**
   * Get status color
   */
  const getStatusColor = (status: string): string => {
    const colors: Record<string, string> = {
      healthy: 'bg-green-500',
      warning: 'bg-yellow-500',
      error: 'bg-red-500',
      offline: 'bg-gray-500',
    };
    return colors[status] || 'bg-gray-500';
  };

  /**
   * Get health color
   */
  const getHealthColor = (healthScore: number): string => {
    if (healthScore >= 80) return 'bg-green-600';
    if (healthScore >= 60) return 'bg-yellow-600';
    return 'bg-red-600';
  };

  /**
   * Get type badge color
   */
  const getTypeBadgeColor = (type: string): string => {
    const colors: Record<string, string> = {
      bridge: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
      coordinator: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
      monitor: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
      validator: 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300',
    };
    return colors[type] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
  };

  /**
   * Get type icon
   */
  const getTypeIcon = (type: string): React.ReactElement => {
    const icons: Record<string, React.ReactElement> = {
      bridge: <Link className="w-4 h-4" />,
      coordinator: <GitBranch className="w-4 h-4" />,
      monitor: <Activity className="w-4 h-4" />,
      validator: <Shield className="w-4 h-4" />,
    };
    return icons[type] || <Activity className="w-4 h-4" />;
  };

  /**
   * Format timestamp
   */
  const formatTimestamp = (timestamp: string): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) return 'just now';
    if (diffMins === 1) return '1 min ago';
    if (diffMins < 60) return `${diffMins} mins ago`;

    const diffHours = Math.floor(diffMins / 60);
    if (diffHours === 1) return '1 hour ago';
    if (diffHours < 24) return `${diffHours} hours ago`;

    return date.toLocaleDateString();
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-2">
          <Filter className="w-5 h-5 text-purple-600" />
          Integration Components
        </h2>
        <div className="text-sm text-gray-600 dark:text-gray-400">
          {filteredComponents.length} of {components.length}
        </div>
      </div>

      {/* Filters */}
      <div className="mb-6 space-y-4">
        {/* Type Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Filter by Type
          </label>
          <div className="flex flex-wrap gap-2">
            {(['all', 'bridge', 'coordinator', 'monitor', 'validator'] as const).map(type => (
              <button
                key={type}
                onClick={() => setFilterType(type)}
                className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                  filterType === type
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                }`}
              >
                {type === 'all' ? 'All Types' : type.charAt(0).toUpperCase() + type.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* Status Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Filter by Status
          </label>
          <div className="flex flex-wrap gap-2">
            {(['all', 'healthy', 'warning', 'error', 'offline'] as const).map(status => (
              <button
                key={status}
                onClick={() => setFilterStatus(status)}
                className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                  filterStatus === status
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                }`}
              >
                {status === 'all' ? 'All Status' : status.charAt(0).toUpperCase() + status.slice(1)}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Component Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredComponents.map(component => (
          <div
            key={component.id}
            className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-purple-300 dark:hover:border-purple-600 transition-colors"
          >
            {/* Component Header */}
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-2">
                {/* Status Indicator */}
                <div className={`w-3 h-3 rounded-full ${getStatusColor(component.status)}`} />

                {/* Component Name */}
                <div>
                  <div className="font-semibold text-gray-900 dark:text-white text-sm">
                    {component.name}
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    {component.id}
                  </div>
                </div>
              </div>
            </div>

            {/* Type Badge */}
            <div className="mb-3">
              <span className={`inline-flex items-center gap-1 text-xs px-2 py-1 rounded-full ${getTypeBadgeColor(component.integrationType)}`}>
                {getTypeIcon(component.integrationType)}
                {component.integrationType}
              </span>
            </div>

            {/* Health Score */}
            <div className="mb-3">
              <div className="flex items-center justify-between mb-1">
                <span className="text-xs text-gray-600 dark:text-gray-400">Health</span>
                <span className="text-xs font-semibold text-gray-900 dark:text-white">
                  {component.healthScore}%
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className={`${getHealthColor(component.healthScore)} h-2 rounded-full transition-all duration-500`}
                  style={{ width: `${component.healthScore}%` }}
                />
              </div>
            </div>

            {/* Metrics */}
            {component.metrics && (
              <div className="grid grid-cols-2 gap-2 mb-3 text-xs">
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded p-2">
                  <div className="text-gray-600 dark:text-gray-400">Operations</div>
                  <div className="font-semibold text-gray-900 dark:text-white">
                    {component.metrics.operationCount || 0}
                  </div>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded p-2">
                  <div className="text-gray-600 dark:text-gray-400">Success Rate</div>
                  <div className="font-semibold text-gray-900 dark:text-white">
                    {component.metrics.successRate || 0}%
                  </div>
                </div>
              </div>
            )}

            {/* Last Update */}
            <div className="text-xs text-gray-500 dark:text-gray-500">
              Updated {formatTimestamp(component.lastUpdate)}
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {filteredComponents.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 dark:text-gray-600 mb-2">
            <Filter className="w-12 h-12 mx-auto" />
          </div>
          <div className="text-gray-600 dark:text-gray-400">
            No components match the selected filters
          </div>
        </div>
      )}
    </div>
  );
}

