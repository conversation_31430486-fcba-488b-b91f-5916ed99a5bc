/**
 * ============================================================================
 * TEST RESULTS DISPLAY COMPONENT
 * ============================================================================
 *
 * Displays integration operation results
 *
 * Features:
 * - Success/error status display
 * - Operation metadata
 * - Duration tracking
 * - Collapsible JSON data viewer
 * - Key metrics extraction
 *
 * Author: AI Assistant (Phase 3D Implementation)
 * Created: 2025-10-22
 * Authority: President & CEO, E.Z. Consultancy
 * ============================================================================
 */

'use client';

import React, { useState } from 'react';
import { CheckCircle, XCircle, ChevronDown, ChevronUp, Clock } from 'lucide-react';
import type { TOperationResult, TIntegrationOperation } from '../../types/integration-types';

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface ITestResultsDisplayProps {
  operation: TIntegrationOperation;
  result: TOperationResult | null;
  error: string | null;
  duration?: number;
}

// ============================================================================
// COMPONENT IMPLEMENTATION
// ============================================================================

export function TestResultsDisplay({
  operation,
  result,
  error,
  duration,
}: ITestResultsDisplayProps): React.ReactElement {
  const [isExpanded, setIsExpanded] = useState<boolean>(false);

  /**
   * Extract key metrics from result
   */
  const extractKeyMetrics = (): Array<{ label: string; value: string | number }> => {
    if (!result) return [];

    const metrics: Array<{ label: string; value: string | number }> = [];

    if ('bridgeStatus' in result) {
      metrics.push(
        { label: 'Bridge Status', value: result.bridgeStatus },
        { label: 'Connection Latency', value: `${result.connectionLatency}ms` },
        { label: 'Messages Sent', value: result.messagesSent.toLocaleString() },
        { label: 'Messages Received', value: result.messagesReceived.toLocaleString() },
        { label: 'Error Rate', value: `${result.errorRate}%` }
      );
    }

    if ('coordinationStatus' in result) {
      metrics.push(
        { label: 'Coordination Status', value: result.coordinationStatus },
        { label: 'Connected Components', value: result.connectedComponents },
        { label: 'Events Synchronized', value: result.eventsSynchronized.toLocaleString() },
        { label: 'Sync Latency', value: `${result.syncLatency}ms` },
        { label: 'Conflict Resolutions', value: result.conflictResolutions }
      );
    }

    if ('integrationHealth' in result) {
      metrics.push(
        { label: 'Integration Health', value: `${result.integrationHealth}%` },
        { label: 'System-Wide Health', value: `${result.systemWideHealth}%` },
        { label: 'Dependency Status', value: result.dependencyStatus },
        { label: 'Integration Points', value: result.integrationPoints },
        { label: 'Data Flow Rate', value: `${result.dataFlowRate} records/s` }
      );
    }

    return metrics;
  };

  const keyMetrics = extractKeyMetrics();

  /**
   * Get operation display name
   */
  const getOperationName = (): string => {
    const names: Record<TIntegrationOperation, string> = {
      'bridge-test': 'Bridge Test',
      'coordination-check': 'Coordination Check',
      'integration-health': 'Integration Health',
    };
    return names[operation] || operation;
  };

  // Don't render if no result or error
  if (!result && !error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          No results to display. Execute an operation to see results.
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      {/* Header */}
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Operation Results
        </h2>
        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
          <span>{getOperationName()}</span>
          {duration && (
            <>
              <span>•</span>
              <Clock className="w-4 h-4" />
              <span>{duration}ms</span>
            </>
          )}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="flex items-start gap-3">
            <XCircle className="w-5 h-5 text-red-600 dark:text-red-400 flex-shrink-0 mt-0.5" />
            <div>
              <div className="font-semibold text-red-900 dark:text-red-100 mb-1">
                Operation Failed
              </div>
              <div className="text-sm text-red-700 dark:text-red-300">
                {error}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Success Display */}
      {result && !error && (
        <>
          {/* Success Banner */}
          <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
            <div className="flex items-center gap-3">
              <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
              <div className="font-semibold text-green-900 dark:text-green-100">
                Operation Completed Successfully
              </div>
            </div>
          </div>

          {/* Key Metrics */}
          {keyMetrics.length > 0 && (
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                Key Metrics
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {keyMetrics.map((metric, index) => (
                  <div
                    key={index}
                    className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3"
                  >
                    <div className="text-xs text-gray-600 dark:text-gray-400 mb-1">
                      {metric.label}
                    </div>
                    <div className="text-sm font-semibold text-gray-900 dark:text-white">
                      {metric.value}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Recommendations (for integration-health) */}
          {'recommendations' in result && result.recommendations && (
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                Recommendations
              </h3>
              <ul className="space-y-2">
                {result.recommendations.map((recommendation, index) => (
                  <li
                    key={index}
                    className="flex items-start gap-2 text-sm text-gray-700 dark:text-gray-300"
                  >
                    <span className="text-green-600 dark:text-green-400 mt-0.5">•</span>
                    <span>{recommendation}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Raw Data (Collapsible) */}
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg">
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="w-full flex items-center justify-between p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
            >
              <span className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                Raw Data
              </span>
              {isExpanded ? (
                <ChevronUp className="w-4 h-4 text-gray-600 dark:text-gray-400" />
              ) : (
                <ChevronDown className="w-4 h-4 text-gray-600 dark:text-gray-400" />
              )}
            </button>

            {isExpanded && (
              <div className="border-t border-gray-200 dark:border-gray-700 p-4">
                <pre className="text-xs text-gray-700 dark:text-gray-300 overflow-x-auto bg-gray-50 dark:bg-gray-900/50 p-4 rounded">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}

