/**
 * ============================================================================
 * OA FRAMEWORK - M0 REAL DASHBOARD
 * Compliance Operations Panel Component
 * ============================================================================
 * 
 * @fileoverview Control panel for governance operations
 * @module components/governance/ComplianceOperationsPanel
 * @version 1.0.0
 * @since 2025-10-22
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2025 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

'use client';

import React from 'react';
import type { TGovernanceOperation, TOperationStatus } from '@/types/governance-types';
import { Play, CheckCircle, FileCheck, Shield, Loader2 } from 'lucide-react';

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface IComplianceOperationsPanelProps {
  /** Current operation status */
  operationStatus: TOperationStatus;
  
  /** Run compliance check */
  onComplianceCheck: () => void;
  
  /** Run rule validation */
  onRuleValidation: () => void;
  
  /** Run framework audit */
  onFrameworkAudit: () => void;
  
  /** Optional custom className */
  className?: string;
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Compliance Operations Panel Component
 * 
 * Provides controls for:
 * - Compliance check
 * - Rule validation
 * - Framework audit
 * 
 * @param props - Component props
 * @returns Operations panel
 */
export function ComplianceOperationsPanel({
  operationStatus,
  onComplianceCheck,
  onRuleValidation,
  onFrameworkAudit,
  className = '',
}: IComplianceOperationsPanelProps): JSX.Element {
  const isRunning = operationStatus === 'running';

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 ${className}`}>
      {/* Header */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
          <Play className="w-5 h-5 text-purple-600 dark:text-purple-400" />
          Governance Operations
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Execute compliance checks and audits
        </p>
      </div>

      {/* Operations Grid */}
      <div className="space-y-4">
        {/* Compliance Check */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-start gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <CheckCircle className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h4 className="text-sm font-semibold text-gray-900 dark:text-white">
                  Compliance Check
                </h4>
                <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                  Verify compliance status across all frameworks
                </p>
              </div>
            </div>
          </div>
          <button
            onClick={onComplianceCheck}
            disabled={isRunning}
            className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg text-sm font-medium transition-colors flex items-center justify-center gap-2"
          >
            {isRunning ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                Running...
              </>
            ) : (
              <>
                <Play className="w-4 h-4" />
                Run Check
              </>
            )}
          </button>
        </div>

        {/* Rule Validation */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-start gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                <FileCheck className="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h4 className="text-sm font-semibold text-gray-900 dark:text-white">
                  Rule Validation
                </h4>
                <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                  Validate all active governance rules
                </p>
              </div>
            </div>
          </div>
          <button
            onClick={onRuleValidation}
            disabled={isRunning}
            className="w-full px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg text-sm font-medium transition-colors flex items-center justify-center gap-2"
          >
            {isRunning ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                Running...
              </>
            ) : (
              <>
                <Play className="w-4 h-4" />
                Validate Rules
              </>
            )}
          </button>
        </div>

        {/* Framework Audit */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-start gap-3">
              <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                <Shield className="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <h4 className="text-sm font-semibold text-gray-900 dark:text-white">
                  Framework Audit
                </h4>
                <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                  Perform comprehensive framework audit
                </p>
              </div>
            </div>
          </div>
          <button
            onClick={onFrameworkAudit}
            disabled={isRunning}
            className="w-full px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white rounded-lg text-sm font-medium transition-colors flex items-center justify-center gap-2"
          >
            {isRunning ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                Running...
              </>
            ) : (
              <>
                <Play className="w-4 h-4" />
                Run Audit
              </>
            )}
          </button>
        </div>
      </div>

      {/* Status Indicator */}
      {operationStatus !== 'idle' && (
        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2">
            {operationStatus === 'running' && (
              <>
                <Loader2 className="w-4 h-4 text-blue-600 dark:text-blue-400 animate-spin" />
                <span className="text-sm text-blue-600 dark:text-blue-400 font-medium">
                  Operation in progress...
                </span>
              </>
            )}
            {operationStatus === 'success' && (
              <>
                <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400" />
                <span className="text-sm text-green-600 dark:text-green-400 font-medium">
                  Operation completed successfully
                </span>
              </>
            )}
            {operationStatus === 'error' && (
              <>
                <CheckCircle className="w-4 h-4 text-red-600 dark:text-red-400" />
                <span className="text-sm text-red-600 dark:text-red-400 font-medium">
                  Operation failed
                </span>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

