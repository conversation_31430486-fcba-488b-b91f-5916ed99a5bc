/**
 * ============================================================================
 * OA FRAMEWORK - M0 REAL DASHBOARD
 * Framework Status Grid Component
 * ============================================================================
 * 
 * @fileoverview Grid layout showing all governance frameworks with status indicators
 * @module components/governance/FrameworkStatusGrid
 * @version 1.0.0
 * @since 2025-10-22
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2025 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

'use client';

import React, { useState } from 'react';
import type { IGovernanceData, TGovernanceType } from '@/types/governance-types';
import { GOVERNANCE_TYPE_NAMES, GOVERNANCE_TYPE_COLORS } from '@/types/governance-types';
import { Shield, CheckCircle, AlertTriangle, XCircle, Filter } from 'lucide-react';

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface IFrameworkStatusGridProps {
  /** Governance data */
  data: IGovernanceData;
  
  /** Optional custom className */
  className?: string;
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Framework Status Grid Component
 * 
 * Displays all governance frameworks in a grid layout with:
 * - Status indicators (healthy/warning/error/offline)
 * - Framework type badges
 * - Health scores
 * - Filtering by type and status
 * 
 * @param props - Component props
 * @returns Framework status grid
 */
export function FrameworkStatusGrid({
  data,
  className = '',
}: IFrameworkStatusGridProps): JSX.Element {
  const [filterType, setFilterType] = useState<TGovernanceType | 'all'>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  // Filter components
  const filteredComponents = data.components.filter(component => {
    const typeMatch = filterType === 'all' || component.governanceType === filterType;
    const statusMatch = filterStatus === 'all' || component.status === filterStatus;
    return typeMatch && statusMatch;
  });

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-600 dark:text-red-400" />;
      default:
        return <XCircle className="w-5 h-5 text-gray-600 dark:text-gray-400" />;
    }
  };

  // Get status color
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'healthy':
        return 'border-green-500 bg-green-50 dark:bg-green-900/20';
      case 'warning':
        return 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20';
      case 'error':
        return 'border-red-500 bg-red-50 dark:bg-red-900/20';
      default:
        return 'border-gray-500 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
          <Shield className="w-5 h-5 text-purple-600 dark:text-purple-400" />
          Governance Frameworks
        </h3>
        <div className="flex items-center gap-2">
          <Filter className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {filteredComponents.length} of {data.components.length}
          </span>
        </div>
      </div>

      {/* Filters */}
      <div className="mb-6 flex flex-wrap gap-3">
        {/* Type Filter */}
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
            Type:
          </label>
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value as TGovernanceType | 'all')}
            className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="all">All Types</option>
            <option value="rule-engine">Rule Engine</option>
            <option value="compliance">Compliance</option>
            <option value="framework">Framework</option>
            <option value="analytics">Analytics</option>
            <option value="reporting">Reporting</option>
          </select>
        </div>

        {/* Status Filter */}
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
            Status:
          </label>
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="all">All Status</option>
            <option value="healthy">Healthy</option>
            <option value="warning">Warning</option>
            <option value="error">Error</option>
            <option value="offline">Offline</option>
          </select>
        </div>
      </div>

      {/* Framework Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
        {filteredComponents.length > 0 ? (
          filteredComponents.map(component => (
            <div
              key={component.id}
              className={`border-l-4 rounded-lg p-4 ${getStatusColor(component.status)}`}
            >
              {/* Component Header */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-semibold text-gray-900 dark:text-white truncate">
                    {component.name}
                  </h4>
                  <span className={`inline-block mt-1 px-2 py-0.5 text-xs font-medium rounded ${GOVERNANCE_TYPE_COLORS[component.governanceType]}`}>
                    {GOVERNANCE_TYPE_NAMES[component.governanceType]}
                  </span>
                </div>
                <div className="ml-2 flex-shrink-0">
                  {getStatusIcon(component.status)}
                </div>
              </div>

              {/* Component Metrics */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Health Score</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {component.healthScore}%
                  </span>
                </div>
                {component.responseTime !== undefined && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Response Time</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {component.responseTime}ms
                    </span>
                  </div>
                )}
                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-500 dark:text-gray-400">Last Update</span>
                  <span className="text-gray-500 dark:text-gray-400">
                    {new Date(component.lastUpdate).toLocaleTimeString()}
                  </span>
                </div>
              </div>

              {/* Health Progress Bar */}
              <div className="mt-3">
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                  <div
                    className={`h-1.5 rounded-full ${
                      component.healthScore >= 90
                        ? 'bg-green-600'
                        : component.healthScore >= 70
                        ? 'bg-yellow-600'
                        : 'bg-red-600'
                    }`}
                    style={{ width: `${component.healthScore}%` }}
                  />
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="col-span-full text-center py-8 text-gray-500 dark:text-gray-400">
            No frameworks match the selected filters
          </div>
        )}
      </div>

      {/* Summary */}
      <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-4 gap-4 text-center">
          <div>
            <p className="text-2xl font-bold text-green-600 dark:text-green-400">
              {data.components.filter(c => c.status === 'healthy').length}
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-400">Healthy</p>
          </div>
          <div>
            <p className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
              {data.components.filter(c => c.status === 'warning').length}
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-400">Warning</p>
          </div>
          <div>
            <p className="text-2xl font-bold text-red-600 dark:text-red-400">
              {data.components.filter(c => c.status === 'error').length}
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-400">Error</p>
          </div>
          <div>
            <p className="text-2xl font-bold text-gray-600 dark:text-gray-400">
              {data.components.filter(c => c.status === 'offline').length}
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-400">Offline</p>
          </div>
        </div>
      </div>
    </div>
  );
}

