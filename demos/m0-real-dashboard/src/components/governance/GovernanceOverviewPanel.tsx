/**
 * ============================================================================
 * OA FRAMEWORK - M0 REAL DASHBOARD
 * Governance Overview Panel Component
 * ============================================================================
 * 
 * @fileoverview Governance overview panel displaying key governance metrics
 * @module components/governance/GovernanceOverviewPanel
 * @version 1.0.0
 * @since 2025-10-22
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2025 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

'use client';

import React from 'react';
import type { IGovernanceData } from '@/types/governance-types';
import { Shield, CheckCircle, AlertTriangle, XCircle, FileCheck } from 'lucide-react';

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface IGovernanceOverviewPanelProps {
  /** Governance data */
  data: IGovernanceData;
  
  /** Optional custom className */
  className?: string;
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Governance Overview Panel Component
 * 
 * Displays high-level governance metrics including:
 * - Total governance components
 * - Healthy/warning/error breakdown
 * - Compliance score
 * - Rule count and violations
 * - Active frameworks
 * 
 * @param props - Component props
 * @returns Governance overview panel
 */
export function GovernanceOverviewPanel({
  data,
  className = '',
}: IGovernanceOverviewPanelProps): JSX.Element {
  // Calculate warning components
  const warningComponents = data.totalGovernanceComponents - data.healthyComponents - data.errorComponents;
  
  // Calculate health percentage
  const healthPercentage = data.totalGovernanceComponents > 0
    ? (data.healthyComponents / data.totalGovernanceComponents) * 100
    : 0;

  // Determine compliance status color
  const getComplianceColor = (score: number): string => {
    if (score >= 90) return 'text-green-600 dark:text-green-400';
    if (score >= 70) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
          <Shield className="w-6 h-6 text-purple-600 dark:text-purple-400" />
          Governance Overview
        </h2>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {/* Total Components */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
              <Shield className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Components
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {data.totalGovernanceComponents}
              </p>
            </div>
          </div>
        </div>

        {/* Healthy Components */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
              <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Healthy
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {data.healthyComponents}
              </p>
            </div>
          </div>
        </div>

        {/* Compliance Score */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <FileCheck className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Compliance Score
              </p>
              <p className={`text-2xl font-bold ${getComplianceColor(data.metrics.complianceScore)}`}>
                {data.metrics.complianceScore}%
              </p>
            </div>
          </div>
        </div>

        {/* Active Frameworks */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-indigo-100 dark:bg-indigo-900 rounded-lg">
              <Shield className="w-6 h-6 text-indigo-600 dark:text-indigo-400" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Active Frameworks
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {data.metrics.frameworksActive}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Health Progress Bar */}
      <div className="mt-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
            Overall Health
          </span>
          <span className="text-sm font-medium text-gray-900 dark:text-white">
            {healthPercentage.toFixed(1)}%
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
          <div className="flex h-3 rounded-full overflow-hidden">
            <div
              className="bg-green-600 dark:bg-green-400"
              style={{ width: `${(data.healthyComponents / data.totalGovernanceComponents) * 100}%` }}
            />
            <div
              className="bg-yellow-600 dark:bg-yellow-400"
              style={{ width: `${(warningComponents / data.totalGovernanceComponents) * 100}%` }}
            />
            <div
              className="bg-red-600 dark:bg-red-400"
              style={{ width: `${(data.errorComponents / data.totalGovernanceComponents) * 100}%` }}
            />
          </div>
        </div>
        <div className="flex items-center justify-between mt-2 text-xs text-gray-500 dark:text-gray-400">
          <span>Healthy: {data.healthyComponents}</span>
          <span>Warning: {warningComponents}</span>
          <span>Error: {data.errorComponents}</span>
        </div>
      </div>

      {/* Additional Metrics */}
      <div className="mt-6 grid grid-cols-2 gap-4">
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
          <div className="flex items-center gap-2 mb-1">
            <FileCheck className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
              Active Rules
            </span>
          </div>
          <p className="text-xl font-bold text-gray-900 dark:text-white">
            {data.metrics.ruleCount}
          </p>
        </div>

        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
          <div className="flex items-center gap-2 mb-1">
            {data.metrics.violationCount > 0 ? (
              <XCircle className="w-4 h-4 text-red-600 dark:text-red-400" />
            ) : (
              <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400" />
            )}
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
              Violations
            </span>
          </div>
          <p className={`text-xl font-bold ${data.metrics.violationCount > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}`}>
            {data.metrics.violationCount}
          </p>
        </div>
      </div>

      {/* Last Audit */}
      <div className="mt-4 text-xs text-gray-500 dark:text-gray-400 text-center">
        Last Audit: {new Date(data.metrics.lastAudit).toLocaleString()}
      </div>
    </div>
  );
}

