/**
 * ============================================================================
 * OA FRAMEWORK - M0 REAL DASHBOARD
 * Rule Engine Status Component
 * ============================================================================
 * 
 * @fileoverview Display active rule engines, rule count, and validation status
 * @module components/governance/RuleEngineStatus
 * @version 1.0.0
 * @since 2025-10-22
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2025 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

'use client';

import React from 'react';
import type { IGovernanceData } from '@/types/governance-types';
import { Settings, CheckCircle, AlertTriangle, Activity } from 'lucide-react';

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface IRuleEngineStatusProps {
  /** Governance data */
  data: IGovernanceData;
  
  /** Optional custom className */
  className?: string;
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Rule Engine Status Component
 * 
 * Displays:
 * - Active rule engines count
 * - Total rules count
 * - Rule validation status
 * - Rule engine components list
 * 
 * @param props - Component props
 * @returns Rule engine status display
 */
export function RuleEngineStatus({
  data,
  className = '',
}: IRuleEngineStatusProps): JSX.Element {
  // Filter rule engine components
  const ruleEngineComponents = data.components.filter(
    component => component.governanceType === 'rule-engine'
  );

  const activeRuleEngines = ruleEngineComponents.filter(
    component => component.status === 'healthy'
  ).length;

  const warningRuleEngines = ruleEngineComponents.filter(
    component => component.status === 'warning'
  ).length;

  const errorRuleEngines = ruleEngineComponents.filter(
    component => component.status === 'error'
  ).length;

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
          <Settings className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          Rule Engine Status
        </h3>
        <div className="flex items-center gap-2">
          <Activity className="w-4 h-4 text-green-600 dark:text-green-400" />
          <span className="text-sm font-medium text-green-600 dark:text-green-400">
            Active
          </span>
        </div>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        {/* Active Rule Engines */}
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <Settings className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
              Rule Engines
            </span>
          </div>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {ruleEngineComponents.length}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {activeRuleEngines} active
          </p>
        </div>

        {/* Total Rules */}
        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400" />
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
              Active Rules
            </span>
          </div>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {data.metrics.ruleCount}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Validated
          </p>
        </div>
      </div>

      {/* Status Breakdown */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-3">
          Engine Status Breakdown
        </h4>
        <div className="space-y-2">
          {/* Healthy */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400" />
              <span className="text-sm text-gray-600 dark:text-gray-400">Healthy</span>
            </div>
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {activeRuleEngines}
            </span>
          </div>

          {/* Warning */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <AlertTriangle className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
              <span className="text-sm text-gray-600 dark:text-gray-400">Warning</span>
            </div>
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {warningRuleEngines}
            </span>
          </div>

          {/* Error */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <AlertTriangle className="w-4 h-4 text-red-600 dark:text-red-400" />
              <span className="text-sm text-gray-600 dark:text-gray-400">Error</span>
            </div>
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {errorRuleEngines}
            </span>
          </div>
        </div>
      </div>

      {/* Rule Engine Components List */}
      <div>
        <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-3">
          Rule Engine Components
        </h4>
        <div className="space-y-2 max-h-48 overflow-y-auto">
          {ruleEngineComponents.length > 0 ? (
            ruleEngineComponents.map(component => (
              <div
                key={component.id}
                className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded"
              >
                <div className="flex items-center gap-2 flex-1 min-w-0">
                  {component.status === 'healthy' ? (
                    <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400 flex-shrink-0" />
                  ) : component.status === 'warning' ? (
                    <AlertTriangle className="w-4 h-4 text-yellow-600 dark:text-yellow-400 flex-shrink-0" />
                  ) : (
                    <AlertTriangle className="w-4 h-4 text-red-600 dark:text-red-400 flex-shrink-0" />
                  )}
                  <span className="text-sm text-gray-900 dark:text-white truncate">
                    {component.name}
                  </span>
                </div>
                <span className="text-xs font-medium text-gray-500 dark:text-gray-400 ml-2">
                  {component.healthScore}%
                </span>
              </div>
            ))
          ) : (
            <div className="text-center py-4 text-sm text-gray-500 dark:text-gray-400">
              No rule engine components found
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

