/**
 * ============================================================================
 * OA FRAMEWORK - M0 REAL DASHBOARD
 * Alert Notification Component
 * ============================================================================
 * 
 * @fileoverview Toast-style notification for governance alerts
 * @module components/governance/AlertNotification
 * @version 1.0.0
 * @since 2025-10-22
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2025 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

'use client';

import React from 'react';
import type { IGovernanceAlert } from '@/types/governance-types';
import { Info, AlertTriangle, XCircle, X, Check } from 'lucide-react';

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface IAlertNotificationProps {
  /** Alert to display */
  alert: IGovernanceAlert;
  
  /** Acknowledge alert callback */
  onAcknowledge: (alertId: string) => void;
  
  /** Dismiss alert callback */
  onDismiss: (alertId: string) => void;
  
  /** Optional custom className */
  className?: string;
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Alert Notification Component
 * 
 * Toast-style notification with:
 * - Severity-based styling
 * - Acknowledge and dismiss actions
 * - Component information
 * - Timestamp
 * 
 * @param props - Component props
 * @returns Alert notification
 */
export function AlertNotification({
  alert,
  onAcknowledge,
  onDismiss,
  className = '',
}: IAlertNotificationProps): JSX.Element {
  // Get severity styling
  const getSeverityStyles = () => {
    switch (alert.severity) {
      case 'critical':
        return {
          bg: 'bg-red-50 dark:bg-red-900/20',
          border: 'border-red-500',
          icon: <XCircle className="w-5 h-5 text-red-600 dark:text-red-400" />,
          text: 'text-red-900 dark:text-red-100',
          subtext: 'text-red-700 dark:text-red-300',
        };
      case 'warning':
        return {
          bg: 'bg-yellow-50 dark:bg-yellow-900/20',
          border: 'border-yellow-500',
          icon: <AlertTriangle className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />,
          text: 'text-yellow-900 dark:text-yellow-100',
          subtext: 'text-yellow-700 dark:text-yellow-300',
        };
      case 'info':
        return {
          bg: 'bg-blue-50 dark:bg-blue-900/20',
          border: 'border-blue-500',
          icon: <Info className="w-5 h-5 text-blue-600 dark:text-blue-400" />,
          text: 'text-blue-900 dark:text-blue-100',
          subtext: 'text-blue-700 dark:text-blue-300',
        };
      default:
        return {
          bg: 'bg-gray-50 dark:bg-gray-900/20',
          border: 'border-gray-500',
          icon: <Info className="w-5 h-5 text-gray-600 dark:text-gray-400" />,
          text: 'text-gray-900 dark:text-gray-100',
          subtext: 'text-gray-700 dark:text-gray-300',
        };
    }
  };

  const styles = getSeverityStyles();

  return (
    <div
      className={`${styles.bg} border-l-4 ${styles.border} rounded-lg shadow-lg p-4 ${className}`}
      role="alert"
      aria-live="polite"
    >
      <div className="flex items-start gap-3">
        {/* Icon */}
        <div className="flex-shrink-0 mt-0.5">
          {styles.icon}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          {/* Alert Message */}
          <p className={`text-sm font-medium ${styles.text} mb-1`}>
            {alert.message}
          </p>

          {/* Alert Details */}
          <div className={`text-xs ${styles.subtext} space-y-0.5`}>
            {alert.componentName && (
              <p>Component: {alert.componentName}</p>
            )}
            <p>Type: {alert.type}</p>
            <p>Time: {alert.timestamp.toLocaleTimeString()}</p>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2 mt-3">
            {!alert.acknowledged && (
              <button
                onClick={() => onAcknowledge(alert.id)}
                className={`px-3 py-1 text-xs font-medium rounded ${
                  alert.severity === 'critical'
                    ? 'bg-red-600 hover:bg-red-700 text-white'
                    : alert.severity === 'warning'
                    ? 'bg-yellow-600 hover:bg-yellow-700 text-white'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
                } transition-colors flex items-center gap-1`}
                aria-label="Acknowledge alert"
              >
                <Check className="w-3 h-3" />
                Acknowledge
              </button>
            )}
            {alert.acknowledged && (
              <span className="px-3 py-1 text-xs font-medium rounded bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                Acknowledged
              </span>
            )}
          </div>
        </div>

        {/* Dismiss Button */}
        <button
          onClick={() => onDismiss(alert.id)}
          className="flex-shrink-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
          aria-label="Dismiss alert"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
}

