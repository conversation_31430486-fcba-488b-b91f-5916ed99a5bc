/**
 * ============================================================================
 * OA FRAMEWORK - M0 REAL DASHBOARD
 * Operation Results Display Component
 * ============================================================================
 * 
 * @fileoverview Display governance operation results
 * @module components/governance/OperationResultsDisplay
 * @version 1.0.0
 * @since 2025-10-22
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2025 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

'use client';

import React from 'react';
import type { IGovernanceOperationResult } from '@/types/governance-types';
import { CheckCircle, XCircle, Clock, Activity } from 'lucide-react';

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface IOperationResultsDisplayProps {
  /** Operation result */
  result: IGovernanceOperationResult | null;
  
  /** Optional custom className */
  className?: string;
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Operation Results Display Component
 * 
 * Displays operation results with:
 * - Success/error status
 * - Operation duration
 * - Detailed data
 * - Timestamp
 * 
 * @param props - Component props
 * @returns Operation results display
 */
export function OperationResultsDisplay({
  result,
  className = '',
}: IOperationResultsDisplayProps): JSX.Element {
  if (!result) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 ${className}`}>
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          <Activity className="w-12 h-12 mx-auto mb-3 opacity-50" />
          <p className="text-sm">No operation results yet</p>
          <p className="text-xs mt-1">Run an operation to see results here</p>
        </div>
      </div>
    );
  }

  const isSuccess = result.status === 'success';
  const operationName = result.operation.split('-').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ');

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Operation Results
        </h3>
        <div className="flex items-center gap-2">
          {isSuccess ? (
            <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
          ) : (
            <XCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
          )}
          <span className={`text-sm font-medium ${
            isSuccess 
              ? 'text-green-600 dark:text-green-400' 
              : 'text-red-600 dark:text-red-400'
          }`}>
            {isSuccess ? 'Success' : 'Failed'}
          </span>
        </div>
      </div>

      {/* Operation Info */}
      <div className="space-y-4">
        {/* Operation Type */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
              Operation
            </span>
            <span className="text-sm font-semibold text-gray-900 dark:text-white">
              {operationName}
            </span>
          </div>
        </div>

        {/* Component ID (if applicable) */}
        {result.componentId && (
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Component
              </span>
              <span className="text-sm font-mono text-gray-900 dark:text-white">
                {result.componentId}
              </span>
            </div>
          </div>
        )}

        {/* Duration */}
        {result.duration !== undefined && (
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Duration
                </span>
              </div>
              <span className="text-sm font-semibold text-gray-900 dark:text-white">
                {result.duration}ms
              </span>
            </div>
          </div>
        )}

        {/* Timestamp */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
              Timestamp
            </span>
            <span className="text-sm text-gray-900 dark:text-white">
              {new Date(result.timestamp).toLocaleString()}
            </span>
          </div>
        </div>

        {/* Error Message (if failed) */}
        {!isSuccess && result.error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div className="flex items-start gap-2">
              <XCircle className="w-5 h-5 text-red-600 dark:text-red-400 flex-shrink-0 mt-0.5" />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-red-900 dark:text-red-200 mb-1">
                  Error Details
                </p>
                <p className="text-sm text-red-700 dark:text-red-300">
                  {result.error}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Success Data (if available) */}
        {isSuccess && result.data && Object.keys(result.data).length > 0 && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
            <div className="flex items-start gap-2">
              <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 flex-shrink-0 mt-0.5" />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-green-900 dark:text-green-200 mb-2">
                  Operation Data
                </p>
                <div className="space-y-2">
                  {Object.entries(result.data).map(([key, value]) => (
                    <div key={key} className="flex items-start justify-between text-sm">
                      <span className="text-green-700 dark:text-green-300 font-medium">
                        {key}:
                      </span>
                      <span className="text-green-900 dark:text-green-100 ml-2 text-right">
                        {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

