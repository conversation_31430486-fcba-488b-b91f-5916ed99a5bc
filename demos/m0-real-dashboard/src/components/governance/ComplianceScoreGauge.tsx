/**
 * ============================================================================
 * OA FRAMEWORK - M0 REAL DASHBOARD
 * Compliance Score Gauge Component
 * ============================================================================
 * 
 * @fileoverview Circular gauge displaying compliance score with color-coded thresholds
 * @module components/governance/ComplianceScoreGauge
 * @version 1.0.0
 * @since 2025-10-22
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2025 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

'use client';

import React from 'react';
import { PieChart, Pie, Cell, ResponsiveContainer } from 'recharts';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface IComplianceScoreGaugeProps {
  /** Compliance score (0-100) */
  score: number;
  
  /** Previous score for trend calculation (optional) */
  previousScore?: number;
  
  /** Optional custom className */
  className?: string;
  
  /** Show trend indicator */
  showTrend?: boolean;
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Compliance Score Gauge Component
 * 
 * Displays compliance score as a circular gauge with:
 * - Color-coded thresholds (green ≥90%, yellow ≥70%, red <70%)
 * - Trend indicator showing improvement/decline
 * - Percentage display in center
 * 
 * @param props - Component props
 * @returns Compliance score gauge
 */
export function ComplianceScoreGauge({
  score,
  previousScore,
  className = '',
  showTrend = true,
}: IComplianceScoreGaugeProps): JSX.Element {
  // Ensure score is within valid range
  const validScore = Math.max(0, Math.min(100, score));
  
  // Calculate trend
  const trend = previousScore !== undefined ? validScore - previousScore : 0;
  const hasTrend = showTrend && previousScore !== undefined;

  // Determine color based on score
  const getScoreColor = (scoreValue: number): string => {
    if (scoreValue >= 90) return '#10b981'; // green-500
    if (scoreValue >= 70) return '#f59e0b'; // yellow-500
    return '#ef4444'; // red-500
  };

  const scoreColor = getScoreColor(validScore);

  // Prepare data for pie chart
  const data = [
    { name: 'Score', value: validScore },
    { name: 'Remaining', value: 100 - validScore },
  ];

  // Get status text
  const getStatusText = (scoreValue: number): string => {
    if (scoreValue >= 90) return 'Excellent';
    if (scoreValue >= 70) return 'Good';
    if (scoreValue >= 50) return 'Fair';
    return 'Poor';
  };

  const statusText = getStatusText(validScore);

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 ${className}`}>
      {/* Header */}
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Compliance Score
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Overall compliance rating
        </p>
      </div>

      {/* Gauge Chart */}
      <div className="relative">
        <ResponsiveContainer width="100%" height={200}>
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              startAngle={180}
              endAngle={0}
              innerRadius={60}
              outerRadius={80}
              paddingAngle={0}
              dataKey="value"
            >
              <Cell fill={scoreColor} />
              <Cell fill="#e5e7eb" />
            </Pie>
          </PieChart>
        </ResponsiveContainer>

        {/* Center Score Display */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <div className="text-4xl font-bold" style={{ color: scoreColor }}>
            {validScore}%
          </div>
          <div className="text-sm font-medium text-gray-600 dark:text-gray-400 mt-1">
            {statusText}
          </div>
        </div>
      </div>

      {/* Trend Indicator */}
      {hasTrend && (
        <div className="mt-4 flex items-center justify-center gap-2">
          {trend > 0 ? (
            <>
              <TrendingUp className="w-5 h-5 text-green-600 dark:text-green-400" />
              <span className="text-sm font-medium text-green-600 dark:text-green-400">
                +{trend.toFixed(1)}% from last audit
              </span>
            </>
          ) : trend < 0 ? (
            <>
              <TrendingDown className="w-5 h-5 text-red-600 dark:text-red-400" />
              <span className="text-sm font-medium text-red-600 dark:text-red-400">
                {trend.toFixed(1)}% from last audit
              </span>
            </>
          ) : (
            <>
              <Minus className="w-5 h-5 text-gray-600 dark:text-gray-400" />
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                No change from last audit
              </span>
            </>
          )}
        </div>
      )}

      {/* Score Thresholds Legend */}
      <div className="mt-6 space-y-2">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-green-500" />
            <span className="text-gray-600 dark:text-gray-400">Excellent</span>
          </div>
          <span className="text-gray-900 dark:text-white font-medium">≥90%</span>
        </div>
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-yellow-500" />
            <span className="text-gray-600 dark:text-gray-400">Good</span>
          </div>
          <span className="text-gray-900 dark:text-white font-medium">70-89%</span>
        </div>
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-red-500" />
            <span className="text-gray-600 dark:text-gray-400">Needs Attention</span>
          </div>
          <span className="text-gray-900 dark:text-white font-medium">&lt;70%</span>
        </div>
      </div>
    </div>
  );
}

