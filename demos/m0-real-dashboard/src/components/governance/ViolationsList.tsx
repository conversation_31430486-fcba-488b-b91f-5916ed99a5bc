/**
 * ============================================================================
 * OA FRAMEWORK - M0 REAL DASHBOARD
 * Violations List Component
 * ============================================================================
 * 
 * @fileoverview Scrollable list of compliance violations with severity badges
 * @module components/governance/ViolationsList
 * @version 1.0.0
 * @since 2025-10-22
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2025 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

'use client';

import React, { useState, useMemo } from 'react';
import type { IGovernanceData } from '@/types/governance-types';
import { AlertTriangle, XCircle, Info, Filter } from 'lucide-react';

// ============================================================================
// MOCK VIOLATIONS DATA
// ============================================================================

interface IViolation {
  id: string;
  ruleId: string;
  ruleName: string;
  componentId: string;
  componentName: string;
  severity: 'info' | 'warning' | 'critical';
  message: string;
  timestamp: string;
  resolved: boolean;
}

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface IViolationsListProps {
  /** Governance data */
  data: IGovernanceData;
  
  /** Optional custom className */
  className?: string;
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Violations List Component
 * 
 * Displays compliance violations with:
 * - Severity badges (info/warning/critical)
 * - Component names
 * - Timestamps
 * - Filtering by severity and status
 * 
 * @param props - Component props
 * @returns Violations list
 */
export function ViolationsList({
  data,
  className = '',
}: IViolationsListProps): JSX.Element {
  const [filterSeverity, setFilterSeverity] = useState<'all' | 'info' | 'warning' | 'critical'>('all');
  const [filterResolved, setFilterResolved] = useState<'all' | 'active' | 'resolved'>('active');

  // Generate mock violations based on governance data
  const violations = useMemo((): IViolation[] => {
    const mockViolations: IViolation[] = [];
    const violationCount = data.metrics.violationCount;

    // Generate violations for error components
    data.components
      .filter(c => c.status === 'error')
      .forEach((component, index) => {
        if (index < violationCount) {
          mockViolations.push({
            id: `violation-${component.id}-${index}`,
            ruleId: `RULE-${Math.floor(Math.random() * 1000)}`,
            ruleName: `Compliance Rule ${index + 1}`,
            componentId: component.id,
            componentName: component.name,
            severity: 'critical',
            message: `Critical compliance violation detected in ${component.name}`,
            timestamp: component.lastUpdate,
            resolved: false,
          });
        }
      });

    // Generate violations for warning components
    data.components
      .filter(c => c.status === 'warning')
      .forEach((component, index) => {
        if (mockViolations.length < violationCount) {
          mockViolations.push({
            id: `violation-${component.id}-${index}`,
            ruleId: `RULE-${Math.floor(Math.random() * 1000)}`,
            ruleName: `Compliance Rule ${mockViolations.length + 1}`,
            componentId: component.id,
            componentName: component.name,
            severity: 'warning',
            message: `Compliance warning in ${component.name}`,
            timestamp: component.lastUpdate,
            resolved: false,
          });
        }
      });

    // Add some resolved violations
    const resolvedCount = Math.floor(violationCount * 0.3);
    for (let i = 0; i < resolvedCount; i++) {
      mockViolations.push({
        id: `violation-resolved-${i}`,
        ruleId: `RULE-${Math.floor(Math.random() * 1000)}`,
        ruleName: `Compliance Rule ${mockViolations.length + 1}`,
        componentId: `component-${i}`,
        componentName: `Component ${i}`,
        severity: i % 2 === 0 ? 'warning' : 'info',
        message: `Resolved compliance issue`,
        timestamp: new Date(Date.now() - i * 3600000).toISOString(),
        resolved: true,
      });
    }

    return mockViolations;
  }, [data]);

  // Filter violations
  const filteredViolations = violations.filter(violation => {
    const severityMatch = filterSeverity === 'all' || violation.severity === filterSeverity;
    const resolvedMatch =
      filterResolved === 'all' ||
      (filterResolved === 'active' && !violation.resolved) ||
      (filterResolved === 'resolved' && violation.resolved);
    return severityMatch && resolvedMatch;
  });

  // Get severity icon and color
  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <XCircle className="w-5 h-5 text-red-600 dark:text-red-400" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />;
      case 'info':
        return <Info className="w-5 h-5 text-blue-600 dark:text-blue-400" />;
      default:
        return <Info className="w-5 h-5 text-gray-600 dark:text-gray-400" />;
    }
  };

  const getSeverityBadge = (severity: string): string => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'info':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
          <AlertTriangle className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
          Compliance Violations
        </h3>
        <div className="flex items-center gap-2">
          <Filter className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {filteredViolations.length} violations
          </span>
        </div>
      </div>

      {/* Filters */}
      <div className="mb-4 flex flex-wrap gap-3">
        {/* Severity Filter */}
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
            Severity:
          </label>
          <select
            value={filterSeverity}
            onChange={(e) => setFilterSeverity(e.target.value as typeof filterSeverity)}
            className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="all">All</option>
            <option value="critical">Critical</option>
            <option value="warning">Warning</option>
            <option value="info">Info</option>
          </select>
        </div>

        {/* Status Filter */}
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
            Status:
          </label>
          <select
            value={filterResolved}
            onChange={(e) => setFilterResolved(e.target.value as typeof filterResolved)}
            className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="all">All</option>
            <option value="active">Active</option>
            <option value="resolved">Resolved</option>
          </select>
        </div>
      </div>

      {/* Violations List */}
      <div className="space-y-3 max-h-96 overflow-y-auto">
        {filteredViolations.length > 0 ? (
          filteredViolations.map(violation => (
            <div
              key={violation.id}
              className={`p-4 rounded-lg border ${
                violation.resolved
                  ? 'bg-gray-50 dark:bg-gray-700 border-gray-300 dark:border-gray-600 opacity-60'
                  : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700'
              }`}
            >
              {/* Violation Header */}
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-start gap-3 flex-1 min-w-0">
                  {getSeverityIcon(violation.severity)}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className={`px-2 py-0.5 text-xs font-medium rounded ${getSeverityBadge(violation.severity)}`}>
                        {violation.severity.toUpperCase()}
                      </span>
                      {violation.resolved && (
                        <span className="px-2 py-0.5 text-xs font-medium rounded bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                          RESOLVED
                        </span>
                      )}
                    </div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {violation.message}
                    </p>
                  </div>
                </div>
              </div>

              {/* Violation Details */}
              <div className="ml-8 space-y-1">
                <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
                  <span className="font-medium">Rule:</span>
                  <span>{violation.ruleName} ({violation.ruleId})</span>
                </div>
                <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
                  <span className="font-medium">Component:</span>
                  <span>{violation.componentName}</span>
                </div>
                <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                  <span className="font-medium">Timestamp:</span>
                  <span>{new Date(violation.timestamp).toLocaleString()}</span>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            No violations match the selected filters
          </div>
        )}
      </div>
    </div>
  );
}

