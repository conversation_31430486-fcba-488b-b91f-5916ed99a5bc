/**
 * ============================================================================
 * OA FRAMEWORK - M0 REAL DASHBOARD
 * Alert History Panel Component
 * ============================================================================
 * 
 * @fileoverview Scrollable panel showing alert history with filtering
 * @module components/governance/AlertHistoryPanel
 * @version 1.0.0
 * @since 2025-10-22
 * 
 * <AUTHOR> Consultancy - Development Team
 * @copyright 2025 E.Z. Consultancy. All rights reserved.
 * 
 * ============================================================================
 */

'use client';

import React, { useState } from 'react';
import type { IGovernanceAlert, TAlertSeverity } from '@/types/governance-types';
import { History, Info, AlertTriangle, XCircle, Filter, Trash2 } from 'lucide-react';

// ============================================================================
// COMPONENT PROPS
// ============================================================================

interface IAlertHistoryPanelProps {
  /** Array of alerts */
  alerts: IGovernanceAlert[];
  
  /** Clear all alerts callback */
  onClearAll: () => void;
  
  /** Optional custom className */
  className?: string;
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Alert History Panel Component
 * 
 * Displays alert history with:
 * - Filtering by severity and type
 * - Acknowledged/unacknowledged status
 * - Timestamps
 * - Clear all functionality
 * 
 * @param props - Component props
 * @returns Alert history panel
 */
export function AlertHistoryPanel({
  alerts,
  onClearAll,
  className = '',
}: IAlertHistoryPanelProps): JSX.Element {
  const [filterSeverity, setFilterSeverity] = useState<TAlertSeverity | 'all'>('all');
  const [filterType, setFilterType] = useState<string>('all');
  const [showAcknowledged, setShowAcknowledged] = useState<boolean>(true);

  // Filter alerts
  const filteredAlerts = alerts.filter(alert => {
    const severityMatch = filterSeverity === 'all' || alert.severity === filterSeverity;
    const typeMatch = filterType === 'all' || alert.type === filterType;
    const acknowledgedMatch = showAcknowledged || !alert.acknowledged;
    return severityMatch && typeMatch && acknowledgedMatch;
  });

  // Get severity icon
  const getSeverityIcon = (severity: TAlertSeverity) => {
    switch (severity) {
      case 'critical':
        return <XCircle className="w-4 h-4 text-red-600 dark:text-red-400" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />;
      case 'info':
        return <Info className="w-4 h-4 text-blue-600 dark:text-blue-400" />;
    }
  };

  // Get severity badge
  const getSeverityBadge = (severity: TAlertSeverity): string => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'info':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
    }
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
          <History className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          Alert History
        </h3>
        <div className="flex items-center gap-3">
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {filteredAlerts.length} alerts
          </span>
          {alerts.length > 0 && (
            <button
              onClick={onClearAll}
              className="px-3 py-1 text-xs font-medium text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 flex items-center gap-1"
              aria-label="Clear all alerts"
            >
              <Trash2 className="w-3 h-3" />
              Clear All
            </button>
          )}
        </div>
      </div>

      {/* Filters */}
      <div className="mb-4 flex flex-wrap gap-3">
        {/* Severity Filter */}
        <div className="flex items-center gap-2">
          <Filter className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          <select
            value={filterSeverity}
            onChange={(e) => setFilterSeverity(e.target.value as TAlertSeverity | 'all')}
            className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="all">All Severities</option>
            <option value="critical">Critical</option>
            <option value="warning">Warning</option>
            <option value="info">Info</option>
          </select>
        </div>

        {/* Type Filter */}
        <div className="flex items-center gap-2">
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="all">All Types</option>
            <option value="compliance">Compliance</option>
            <option value="rule">Rule</option>
            <option value="framework">Framework</option>
            <option value="general">General</option>
          </select>
        </div>

        {/* Show Acknowledged Toggle */}
        <label className="flex items-center gap-2 cursor-pointer">
          <input
            type="checkbox"
            checked={showAcknowledged}
            onChange={(e) => setShowAcknowledged(e.target.checked)}
            className="rounded border-gray-300 dark:border-gray-600"
          />
          <span className="text-sm text-gray-600 dark:text-gray-400">
            Show acknowledged
          </span>
        </label>
      </div>

      {/* Alert List */}
      <div className="space-y-2 max-h-96 overflow-y-auto">
        {filteredAlerts.length > 0 ? (
          filteredAlerts.map(alert => (
            <div
              key={alert.id}
              className={`p-3 rounded-lg border ${
                alert.acknowledged
                  ? 'bg-gray-50 dark:bg-gray-700 border-gray-300 dark:border-gray-600 opacity-60'
                  : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700'
              }`}
            >
              <div className="flex items-start gap-3">
                {/* Icon */}
                <div className="flex-shrink-0 mt-0.5">
                  {getSeverityIcon(alert.severity)}
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className={`px-2 py-0.5 text-xs font-medium rounded ${getSeverityBadge(alert.severity)}`}>
                      {alert.severity.toUpperCase()}
                    </span>
                    <span className="px-2 py-0.5 text-xs font-medium rounded bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                      {alert.type}
                    </span>
                    {alert.acknowledged && (
                      <span className="px-2 py-0.5 text-xs font-medium rounded bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        ACK
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-900 dark:text-white mb-1">
                    {alert.message}
                  </p>
                  <div className="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
                    {alert.componentName && (
                      <span>{alert.componentName}</span>
                    )}
                    <span>{alert.timestamp.toLocaleString()}</span>
                  </div>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <History className="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p className="text-sm">No alerts match the selected filters</p>
          </div>
        )}
      </div>
    </div>
  );
}

