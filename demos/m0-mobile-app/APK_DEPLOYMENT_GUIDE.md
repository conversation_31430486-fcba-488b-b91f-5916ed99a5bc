# OA Framework Mobile Demo - APK Deployment Guide

## Prerequisites

- Android SDK installed
- ADB (Android Debug Bridge) configured
- Android device or emulator with USB debugging enabled
- Expo CLI installed (`npm install -g eas-cli`)
- M0 Real Dashboard backend running on `http://localhost:3000`

## Building the APK

### Option 1: Using EAS Build (Recommended)

```bash
cd demos/m0-mobile-app

# Build for production
eas build --platform android --profile production

# Build for preview/testing
eas build --platform android --profile preview

# Build for development
eas build --platform android --profile development
```

The APK will be generated and available for download from the EAS dashboard.

### Option 2: Local Build with Gradle

```bash
cd demos/m0-mobile-app

# Generate native Android project
npx expo prebuild --clean

# Build APK
cd android
./gradlew assembleRelease
cd ..

# APK location: android/app/build/outputs/apk/release/app-release.apk
```

## Installing the APK

### Method 1: Using ADB (Recommended)

```bash
# List connected devices
adb devices

# Install APK on device
adb install path/to/app.apk

# Install on specific device
adb -s <device_id> install path/to/app.apk

# Uninstall app
adb uninstall com.ezconsultancy.oaframeworkmobiledemo
```

### Method 2: Manual Installation

1. Transfer APK to Android device via USB
2. Open file manager on device
3. Navigate to Downloads folder
4. Tap the APK file
5. Grant permissions when prompted
6. Tap "Install"

### Method 3: Using Android Studio

1. Open Android Studio
2. Go to Run → Select Device
3. Choose your device/emulator
4. Click Run

## Running the App

### Prerequisites for Running

1. **Start M0 Real Dashboard Backend**
   ```bash
   cd demos/m0-real-dashboard
   npm install
   npm start
   ```
   Backend will run on `http://localhost:3000`

2. **For Physical Device (Not Localhost)**
   - Update API_BASE_URL in `src/services/api.ts`
   - Change from `http://localhost:3000/api` to `http://<your-machine-ip>:3000/api`
   - Rebuild and reinstall APK

3. **Launch the App**
   - Tap the "OA Framework Mobile Demo" icon on your device
   - App will connect to backend and load data

## Troubleshooting

### App Won't Connect to Backend

**Issue:** "Error loading data" messages
**Solution:** 
- Verify M0 Real Dashboard is running
- Check API_BASE_URL matches your backend address
- For physical device, use machine IP instead of localhost
- Ensure device is on same network as backend

### ADB Not Found

**Solution:**
```bash
# Add Android SDK tools to PATH
export PATH=$PATH:~/Android/Sdk/platform-tools
```

### APK Installation Fails

**Solution:**
```bash
# Clear app data
adb shell pm clear com.ezconsultancy.oaframeworkmobiledemo

# Uninstall and reinstall
adb uninstall com.ezconsultancy.oaframeworkmobiledemo
adb install path/to/app.apk
```

### App Crashes on Launch

**Solution:**
- Check logcat for errors: `adb logcat`
- Verify all dependencies are installed: `npm install`
- Rebuild APK with clean build: `eas build --platform android --profile production --clear-cache`

## App Features

### Home Screen
- Overview of all 4 milestones
- Quick navigation to detailed screens
- Completion status indicators

### M0 Foundation Dashboard
- 184 components overview
- Governance compliance metrics
- Security monitoring status
- Real-time tracking data

### M0.1 Enterprise Analytics
- Advanced analytics engine
- ML pattern recognition
- Session tracking
- Performance predictions

### M0.2 Performance Metrics
- Response time metrics
- Cache hit rate statistics
- Load balancing visualization
- Notification delivery stats

### M0.3 Logging Console
- Real-time audit log stream
- Compliance profile display
- Log filtering capabilities
- Configuration status

### Milestone Interactions
- Data flow visualization
- Integration point highlights
- Real-time event stream
- Cross-milestone dependencies

## Performance Metrics

- App launch time: <2 seconds
- Screen navigation: <300ms
- API response time: <500ms
- Smooth 60fps animations
- APK size: <30MB

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review M0 Real Dashboard logs
3. Check device logcat: `adb logcat | grep OA`
4. Verify network connectivity between device and backend

## Next Steps

1. Build APK using EAS or local Gradle
2. Install on Android device/emulator
3. Start M0 Real Dashboard backend
4. Launch app and verify data loads
5. Test all screens and navigation
6. Verify API connectivity
