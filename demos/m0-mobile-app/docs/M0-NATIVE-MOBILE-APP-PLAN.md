# M0 Native Mobile Application - Implementation Plan

**Document**: M0 Native Mobile Application Implementation Plan
**Authority**: President & CEO, <PERSON><PERSON><PERSON><PERSON> Consultancy
**Status**: PLANNING PHASE
**Created**: 2026-01-12
**Version**: 1.0
**Milestone**: M0 (Milestone 0)
**Target Platform**: React Native with TypeScript Strict Mode
**Project Location**: `./demos/m0-mobile-app/`

---

## 📋 **TABLE OF CONTENTS**

1. [Executive Summary](#executive-summary)
2. [Technical Architecture](#technical-architecture)
3. [Dashboard Migration Strategy](#dashboard-migration-strategy)
4. [Component Reuse Analysis](#component-reuse-analysis)
5. [API Integration Plan](#api-integration-plan)
6. [UI/UX Design Specifications](#uiux-design-specifications)
7. [Development Timeline](#development-timeline)
8. [Testing Strategy](#testing-strategy)
9. [Deployment Plan](#deployment-plan)
10. [Success Metrics](#success-metrics)

---

## 1️⃣ **EXECUTIVE SUMMARY**

### **Project Overview**

The M0 Native Mobile Application is a production-ready mobile companion to the existing M0 Real Dashboard web application. It provides mobile-optimized access to all 4 core M0 dashboards, enabling real-time monitoring of 136 operational M0 components from iOS and Android devices.

### **Scope & Objectives**

**In Scope**:
- ✅ 4 Core Dashboards: Security, Governance, Tracking, Integration Console
- ✅ Real-time data integration with existing M0 backend (136 components)
- ✅ Mobile-optimized UI/UX for phone and tablet form factors
- ✅ Offline capability with data caching
- ✅ Push notifications for critical alerts
- ✅ Cross-platform support (iOS 13+, Android 8.0+)

**Out of Scope**:
- ❌ M1+ enhanced features (future milestones)
- ❌ Administrative configuration changes (read-only mobile access)
- ❌ Component deployment or infrastructure management
- ❌ Backend modifications (uses existing M0 Real Dashboard APIs)

### **Success Criteria**

| Criterion | Target | Measurement |
|-----------|--------|-------------|
| **Platform Coverage** | iOS + Android | App store availability |
| **Dashboard Parity** | 100% feature coverage | All 4 dashboards functional |
| **Performance** | <2s initial load, <500ms navigation | Performance monitoring |
| **Data Accuracy** | 100% match with web dashboard | API integration tests |
| **User Experience** | 4.5+ star rating | App store reviews |
| **Code Quality** | TypeScript strict mode, 80%+ coverage | Automated testing |

### **Strategic Value**

1. **Mobile-First Monitoring**: Enable on-the-go system monitoring for DevOps teams
2. **Real-Time Alerts**: Push notifications for critical system events
3. **Accessibility**: Expand M0 framework visibility to mobile stakeholders
4. **Competitive Advantage**: First-to-market mobile monitoring for OA Framework
5. **User Engagement**: Increase platform adoption through mobile convenience

---

## 2️⃣ **TECHNICAL ARCHITECTURE**

### **Technology Stack**

| Layer | Technology | Version | Justification |
|-------|-----------|---------|---------------|
| **Framework** | React Native | 0.73+ | Cross-platform, large ecosystem, TypeScript support |
| **Language** | TypeScript | 5.0+ | Type safety, OA Framework compliance |
| **Navigation** | React Navigation | 6.x | Industry standard, deep linking support |
| **State Management** | Redux Toolkit + RTK Query | 2.0+ | Predictable state, API caching, real-time updates |
| **UI Components** | React Native Paper | 5.x | Material Design, accessibility, theming |
| **Charts** | Victory Native | 36.x | Mobile-optimized charts, SVG rendering |
| **Networking** | Axios + RTK Query | Latest | HTTP client, request/response interceptors |
| **Storage** | AsyncStorage + MMKV | Latest | Offline data persistence, high performance |
| **Push Notifications** | React Native Firebase | 18.x | Cross-platform notifications |
| **Testing** | Jest + React Native Testing Library | Latest | Unit and integration testing |
| **Build** | Expo (managed workflow) | SDK 50+ | Simplified build process, OTA updates |

### **Application Architecture**

```
┌─────────────────────────────────────────────────────────────┐
│                 PRESENTATION LAYER                          │
│  React Native Screens │ Navigation │ UI Components         │
├─────────────────────────────────────────────────────────────┤
│                 STATE MANAGEMENT LAYER                      │
│  Redux Store │ RTK Query │ Selectors │ Actions             │
├─────────────────────────────────────────────────────────────┤
│                 BUSINESS LOGIC LAYER                        │
│  Hooks │ Services │ Utilities │ Transformers               │
├─────────────────────────────────────────────────────────────┤
│                 DATA LAYER                                  │
│  API Client │ Cache │ Offline Storage │ Push Notifications │
├─────────────────────────────────────────────────────────────┤
│                 INTEGRATION LAYER                           │
│  M0 Backend APIs (136 Components) │ Real-Time WebSocket    │
└─────────────────────────────────────────────────────────────┘
```

### **Project Directory Structure**

```
demos/m0-mobile-app/
├── src/
│   ├── screens/                      # Screen components
│   │   ├── SecurityDashboard/        # Security monitoring
│   │   ├── GovernanceDashboard/      # Governance control
│   │   ├── TrackingDashboard/        # Progress tracking
│   │   ├── IntegrationConsole/       # System integration
│   │   └── Home/                     # Main dashboard
│   ├── components/                   # Reusable components
│   │   ├── common/                   # Shared UI components
│   │   ├── charts/                   # Chart components
│   │   ├── cards/                    # Dashboard cards
│   │   └── widgets/                  # Dashboard widgets
│   ├── navigation/                   # Navigation configuration
│   │   ├── RootNavigator.tsx         # Root navigation
│   │   ├── DashboardNavigator.tsx    # Dashboard tabs
│   │   └── types.ts                  # Navigation types
│   ├── store/                        # Redux store
│   │   ├── slices/                   # Redux slices
│   │   ├── api/                      # RTK Query APIs
│   │   └── index.ts                  # Store configuration
│   ├── services/                     # Business logic services
│   │   ├── api/                      # API client
│   │   ├── storage/                  # Offline storage
│   │   ├── notifications/            # Push notifications
│   │   └── analytics/                # Analytics tracking
│   ├── hooks/                        # Custom React hooks
│   ├── utils/                        # Utility functions
│   ├── types/                        # TypeScript types
│   ├── constants/                    # App constants
│   ├── theme/                        # Theme configuration
│   └── App.tsx                       # Root component
├── assets/                           # Static assets
│   ├── images/                       # Images
│   ├── icons/                        # App icons
│   └── fonts/                        # Custom fonts
├── android/                          # Android native code
├── ios/                              # iOS native code
├── __tests__/                        # Test files
├── docs/                             # Documentation
├── app.json                          # Expo configuration
├── package.json                      # Dependencies
├── tsconfig.json                     # TypeScript config
└── README.md                         # Project README
```

### **State Management Architecture**

**Redux Store Structure**:
```typescript
{
  // Dashboard data slices
  security: {
    data: ISecurityData | null,
    loading: boolean,
    error: string | null,
    lastUpdate: string
  },
  governance: {
    data: IGovernanceData | null,
    loading: boolean,
    error: string | null,
    lastUpdate: string
  },
  tracking: {
    data: ITrackingData | null,
    loading: boolean,
    error: string | null,
    lastUpdate: string
  },
  integration: {
    data: IIntegrationData | null,
    loading: boolean,
    error: string | null,
    lastUpdate: string
  },

  // App state
  app: {
    isOnline: boolean,
    theme: 'light' | 'dark',
    refreshInterval: number,
    notifications: INotification[]
  },

  // RTK Query API cache
  api: {
    queries: { ... },
    mutations: { ... }
  }
}
```

### **API Integration Architecture**

**Backend Endpoints** (Reusing existing M0 Real Dashboard APIs):
```
Base URL: http://localhost:3000/api (development)
          https://oa-framework.com/api (production)

GET  /api/m0-components          # All 136 components overview
GET  /api/m0-security             # Security dashboard data
POST /api/m0-security             # Security operations
GET  /api/m0-governance           # Governance dashboard data
POST /api/m0-governance           # Governance operations
GET  /api/m0-tracking             # Tracking dashboard data
POST /api/m0-tracking             # Tracking operations
GET  /api/m0-integration          # Integration console data
POST /api/m0-integration          # Integration operations
GET  /api/m0-stream               # Server-Sent Events (SSE) for real-time updates
```

**Real-Time Data Flow**:
```
Mobile App → RTK Query → API Client → M0 Backend → 136 Components
     ↑                                      ↓
     └──────── WebSocket/SSE ←──────────────┘
```

---

## 3️⃣ **DASHBOARD MIGRATION STRATEGY**

### **Migration Approach**

**Strategy**: **Adaptive Reuse** - Port business logic and data structures while redesigning UI for mobile UX.

### **Dashboard 1: Security Dashboard**

**Web Dashboard Features** (from `demos/m0-real-dashboard/src/app/security-dashboard/page.tsx`):
- Total security components count (136)
- Memory usage monitoring
- Buffer utilization charts
- Threat level indicators
- Component status grid
- Security operations panel
- Alert history

**Mobile Adaptation**:

| Web Component | Mobile Equivalent | Adaptation Strategy |
|---------------|-------------------|---------------------|
| `SecurityOverviewPanel` | `SecurityOverviewCard` | Compact card with key metrics |
| `MemoryUsageMonitor` | `MemoryUsageChart` | Simplified line chart, swipeable |
| `BufferUtilizationChart` | `BufferUtilizationMiniChart` | Sparkline chart in card |
| `ThreatLevelIndicator` | `ThreatLevelBadge` | Color-coded badge with icon |
| `ComponentStatusGrid` | `ComponentStatusList` | Scrollable list with filters |
| `SecurityOperationsPanel` | `SecurityActionsSheet` | Bottom sheet with action buttons |
| `AlertHistoryPanel` | `AlertHistoryList` | Paginated list with pull-to-refresh |

**Mobile Screen Layout**:
```
┌─────────────────────────┐
│ Security Dashboard      │ ← Header with refresh
├─────────────────────────┤
│ 📊 Overview Card        │ ← Total components, health score
├─────────────────────────┤
│ 📈 Memory Usage Chart   │ ← Swipeable chart (last 30 min)
├─────────────────────────┤
│ 🔴 Threat Level: LOW    │ ← Color-coded badge
├─────────────────────────┤
│ 📋 Component Status     │ ← Scrollable list
│   ├─ Component 1 ✅     │
│   ├─ Component 2 ✅     │
│   └─ Component 3 ⚠️     │
├─────────────────────────┤
│ 🔔 Recent Alerts (3)    │ ← Expandable section
└─────────────────────────┘
```

### **Dashboard 2: Governance Dashboard**

**Web Dashboard Features** (from `demos/m0-real-dashboard/src/app/governance-dashboard/page.tsx`):
- Compliance score gauge
- Rule engine status
- Framework status grid
- Violations list
- Governance operations
- Alert management

**Mobile Adaptation**:

| Web Component | Mobile Equivalent | Adaptation Strategy |
|---------------|-------------------|---------------------|
| `GovernanceOverviewPanel` | `GovernanceOverviewCard` | Compact metrics card |
| `ComplianceScoreGauge` | `ComplianceScoreCircle` | Circular progress indicator |
| `RuleEngineStatus` | `RuleEngineStatusCard` | Card with status indicators |
| `FrameworkStatusGrid` | `FrameworkStatusList` | Scrollable list with icons |
| `ViolationsList` | `ViolationsAccordion` | Expandable accordion list |
| `ComplianceOperationsPanel` | `ComplianceActionsSheet` | Bottom sheet with actions |

**Mobile Screen Layout**:
```
┌─────────────────────────┐
│ Governance Dashboard    │ ← Header with refresh
├─────────────────────────┤
│ ⭕ Compliance: 95%      │ ← Circular progress
├─────────────────────────┤
│ 📊 Rule Engine Status   │ ← Status card
│   Active Rules: 42      │
│   Violations: 3         │
├─────────────────────────┤
│ 🏛️ Framework Status     │ ← Scrollable list
│   ├─ Security ✅        │
│   ├─ Tracking ✅        │
│   └─ Integration ⚠️     │
├─────────────────────────┤
│ ⚠️ Violations (3)       │ ← Expandable accordion
│   └─ [Tap to expand]    │
└─────────────────────────┘
```

### **Dashboard 3: Tracking Dashboard**

**Web Dashboard Features** (from `demos/m0-real-dashboard/src/app/tracking-dashboard/page.tsx`):
- Session analytics chart
- Component health monitor
- Event timeline chart
- Component status grid
- Tracking operations

**Mobile Adaptation**:

| Web Component | Mobile Equivalent | Adaptation Strategy |
|---------------|-------------------|---------------------|
| `TrackingOverviewPanel` | `TrackingOverviewCard` | Compact metrics card |
| `SessionAnalyticsChart` | `SessionAnalyticsMiniChart` | Simplified bar chart |
| `ComponentHealthMonitor` | `ComponentHealthList` | Scrollable list with health indicators |
| `EventTimelineChart` | `EventTimelineList` | Chronological list with timestamps |
| `ComponentStatusGrid` | `ComponentStatusList` | Filterable scrollable list |
| `TrackingOperationsPanel` | `TrackingActionsSheet` | Bottom sheet with operations |

**Mobile Screen Layout**:
```
┌─────────────────────────┐
│ Tracking Dashboard      │ ← Header with refresh
├─────────────────────────┤
│ 📊 Session Analytics    │ ← Mini bar chart
│   Active: 12 | Total: 45│
├─────────────────────────┤
│ 💚 Component Health     │ ← Health summary
│   Healthy: 130/136      │
├─────────────────────────┤
│ 📅 Event Timeline       │ ← Recent events list
│   ├─ 10:45 - Event 1    │
│   ├─ 10:42 - Event 2    │
│   └─ 10:38 - Event 3    │
├─────────────────────────┤
│ 📋 Component Status     │ ← Scrollable list
│   [Filter: All ▼]       │
│   ├─ Component 1 ✅     │
│   └─ Component 2 ✅     │
└─────────────────────────┘
```

### **Dashboard 4: Integration Console**

**Web Dashboard Features** (from `demos/m0-real-dashboard/src/app/integration-console/page.tsx`):
- Integration overview panel
- Cross-component test panel
- Dependency graph
- Integration status grid
- Test execution panel
- Test results display

**Mobile Adaptation**:

| Web Component | Mobile Equivalent | Adaptation Strategy |
|---------------|-------------------|---------------------|
| `IntegrationOverviewPanel` | `IntegrationOverviewCard` | Compact metrics card |
| `CrossComponentTestPanel` | `CrossComponentTestSheet` | Bottom sheet with test options |
| `DependencyGraph` | `DependencyTreeView` | Collapsible tree view |
| `IntegrationStatusGrid` | `IntegrationStatusList` | Scrollable list with status |
| `TestExecutionPanel` | `TestExecutionSheet` | Bottom sheet with test controls |
| `TestResultsDisplay` | `TestResultsModal` | Full-screen modal with results |

**Mobile Screen Layout**:
```
┌─────────────────────────┐
│ Integration Console     │ ← Header with refresh
├─────────────────────────┤
│ 🔗 Integration Overview │ ← Metrics card
│   Bridges: 15           │
│   Coordinators: 8       │
├─────────────────────────┤
│ 🌳 Dependency Tree      │ ← Collapsible tree
│   ├─ ▼ Governance       │
│   │   ├─ Rule Engine    │
│   │   └─ Compliance     │
│   └─ ▶ Tracking         │
├─────────────────────────┤
│ 📋 Integration Status   │ ← Scrollable list
│   ├─ Bridge 1 ✅        │
│   ├─ Bridge 2 ✅        │
│   └─ Bridge 3 ⚠️        │
├─────────────────────────┤
│ 🧪 Run Tests            │ ← Action button
└─────────────────────────┘
```

---

## 4️⃣ **COMPONENT REUSE ANALYSIS**

### **Reusability Assessment**

**Reuse Categories**:
1. **Direct Port** (80%+ code reuse): Business logic, data structures, API calls
2. **Adaptive Reuse** (40-80% code reuse): UI components with mobile adaptations
3. **Mobile-Specific** (0-40% code reuse): Navigation, gestures, mobile-only features

### **Business Logic Reuse (Direct Port)**

**From Web Dashboard** → **To Mobile App**:

| Web Component | Mobile Equivalent | Reuse % | Notes |
|---------------|-------------------|---------|-------|
| `useSecurityData` hook | `useSecurityData` hook | 95% | Same API calls, add offline caching |
| `useGovernanceData` hook | `useGovernanceData` hook | 95% | Same API calls, add offline caching |
| `useTrackingData` hook | `useTrackingData` hook | 95% | Same API calls, add offline caching |
| `useIntegrationData` hook | `useIntegrationData` hook | 95% | Same API calls, add offline caching |
| `useSecurityOperations` hook | `useSecurityOperations` hook | 90% | Same operations, add mobile feedback |
| `useGovernanceOperations` hook | `useGovernanceOperations` hook | 90% | Same operations, add mobile feedback |
| `useTrackingOperations` hook | `useTrackingOperations` hook | 90% | Same operations, add mobile feedback |
| `useIntegrationOperations` hook | `useIntegrationOperations` hook | 90% | Same operations, add mobile feedback |

**TypeScript Types Reuse**:

| Web Type Definition | Mobile Equivalent | Reuse % | Notes |
|---------------------|-------------------|---------|-------|
| `security-types.ts` | `security.types.ts` | 100% | Exact copy, no changes needed |
| `governance-types.ts` | `governance.types.ts` | 100% | Exact copy, no changes needed |
| `tracking-types.ts` | `tracking.types.ts` | 100% | Exact copy, no changes needed |
| `integration-types.ts` | `integration.types.ts` | 100% | Exact copy, no changes needed |

**API Client Reuse**:

```typescript
// Web: demos/m0-real-dashboard/src/hooks/useSecurityData.ts
// Mobile: demos/m0-mobile-app/src/hooks/useSecurityData.ts

// REUSABLE: API endpoint, data fetching logic, error handling
const API_ENDPOINT = '/api/m0-security';
const AUTO_REFRESH_INTERVAL = 30000; // 30 seconds

// MOBILE ADAPTATION: Add offline caching, network status check
const fetchData = async () => {
  // Check network status (mobile-specific)
  const isOnline = await NetInfo.fetch().then(state => state.isConnected);

  if (!isOnline) {
    // Load from cache (mobile-specific)
    return await loadFromCache('security-data');
  }

  // Same API call as web
  const response = await fetch(API_ENDPOINT);
  const data = await response.json();

  // Save to cache (mobile-specific)
  await saveToCache('security-data', data);

  return data;
};
```

### **UI Component Reuse (Adaptive Reuse)**

**Chart Components**:

| Web Component (Recharts) | Mobile Component (Victory Native) | Reuse % | Adaptation |
|--------------------------|-----------------------------------|---------|------------|
| `MemoryUsageMonitor` | `MemoryUsageChart` | 60% | Same data, different chart library |
| `BufferUtilizationChart` | `BufferUtilizationChart` | 60% | Same data, different chart library |
| `SessionAnalyticsChart` | `SessionAnalyticsChart` | 60% | Same data, different chart library |
| `EventTimelineChart` | `EventTimelineList` | 40% | Timeline → List view for mobile |

**Data Display Components**:

| Web Component | Mobile Component | Reuse % | Adaptation |
|---------------|------------------|---------|------------|
| `SecurityOverviewPanel` | `SecurityOverviewCard` | 70% | Same data, compact layout |
| `GovernanceOverviewPanel` | `GovernanceOverviewCard` | 70% | Same data, compact layout |
| `TrackingOverviewPanel` | `TrackingOverviewCard` | 70% | Same data, compact layout |
| `IntegrationOverviewPanel` | `IntegrationOverviewCard` | 70% | Same data, compact layout |
| `ComponentStatusGrid` | `ComponentStatusList` | 50% | Grid → List for mobile scrolling |
| `AlertHistoryPanel` | `AlertHistoryList` | 60% | Panel → List with pull-to-refresh |

### **Mobile-Specific Components (New Development)**

**Navigation Components**:
- `RootNavigator.tsx` - Root navigation stack
- `DashboardNavigator.tsx` - Bottom tab navigation
- `DrawerNavigator.tsx` - Side drawer menu

**Mobile UI Components**:
- `BottomSheet.tsx` - Bottom sheet for actions
- `PullToRefresh.tsx` - Pull-to-refresh component
- `SwipeableCard.tsx` - Swipeable dashboard cards
- `FloatingActionButton.tsx` - FAB for quick actions
- `NotificationBanner.tsx` - In-app notification banner

**Gesture Components**:
- `SwipeableList.tsx` - Swipeable list items
- `PinchZoomChart.tsx` - Pinch-to-zoom charts
- `DoubleTapRefresh.tsx` - Double-tap to refresh

### **Code Sharing Strategy**

**Shared Code Package** (Optional):
```
shared/
├── types/                    # Shared TypeScript types
│   ├── security.types.ts     # Security data types
│   ├── governance.types.ts   # Governance data types
│   ├── tracking.types.ts     # Tracking data types
│   └── integration.types.ts  # Integration data types
├── utils/                    # Shared utilities
│   ├── formatters.ts         # Data formatters
│   ├── validators.ts         # Data validators
│   └── constants.ts          # Shared constants
└── api/                      # API client (platform-agnostic)
    ├── endpoints.ts          # API endpoints
    └── types.ts              # API response types
```

**Reuse Summary**:
- **Business Logic**: 90-95% reuse (hooks, API calls, data transformations)
- **TypeScript Types**: 100% reuse (exact copy from web)
- **UI Components**: 40-70% reuse (data logic reused, UI adapted for mobile)
- **Mobile-Specific**: 0% reuse (new development for mobile UX)

---

## 5️⃣ **API INTEGRATION PLAN**

### **Backend API Endpoints**

**Existing M0 Real Dashboard APIs** (100% reuse):

| Endpoint | Method | Purpose | Response Time | Mobile Optimization |
|----------|--------|---------|---------------|---------------------|
| `/api/m0-components` | GET | All 136 components overview | <500ms | Add pagination support |
| `/api/m0-security` | GET | Security dashboard data | <300ms | Add field filtering |
| `/api/m0-security` | POST | Security operations | <1s | Add operation queuing |
| `/api/m0-governance` | GET | Governance dashboard data | <300ms | Add field filtering |
| `/api/m0-governance` | POST | Governance operations | <1s | Add operation queuing |
| `/api/m0-tracking` | GET | Tracking dashboard data | <300ms | Add field filtering |
| `/api/m0-tracking` | POST | Tracking operations | <1s | Add operation queuing |
| `/api/m0-integration` | GET | Integration console data | <300ms | Add field filtering |
| `/api/m0-integration` | POST | Integration operations | <1s | Add operation queuing |
| `/api/m0-stream` | GET | Server-Sent Events (SSE) | Real-time | Replace with WebSocket for mobile |

### **Mobile API Optimizations**

**1. Field Filtering** (Reduce payload size):
```typescript
// Web: Fetch all fields
GET /api/m0-security

// Mobile: Fetch only required fields
GET /api/m0-security?fields=totalComponents,healthScore,metrics.memoryUsage

// Response size reduction: ~70% smaller payload
```

**2. Pagination** (Reduce initial load):
```typescript
// Web: Fetch all 136 components
GET /api/m0-components

// Mobile: Fetch paginated components
GET /api/m0-components?page=1&limit=20

// Response time improvement: 80% faster initial load
```

**3. Data Compression** (Reduce bandwidth):
```typescript
// Request headers
{
  'Accept-Encoding': 'gzip, deflate, br',
  'Content-Type': 'application/json'
}

// Response compression: ~60% bandwidth reduction
```

**4. Offline Caching** (Improve perceived performance):
```typescript
// RTK Query cache configuration
const securityApi = createApi({
  reducerPath: 'securityApi',
  baseQuery: fetchBaseQuery({ baseUrl: API_BASE_URL }),
  endpoints: (builder) => ({
    getSecurityData: builder.query<ISecurityData, void>({
      query: () => '/m0-security',
      // Cache for 5 minutes
      keepUnusedDataFor: 300,
      // Serve stale data while revalidating
      providesTags: ['Security'],
    }),
  }),
});

// Offline storage with MMKV
const cacheSecurityData = async (data: ISecurityData) => {
  await MMKV.setItem('security-data', JSON.stringify(data));
  await MMKV.setItem('security-data-timestamp', Date.now().toString());
};
```

### **Real-Time Data Integration**

**WebSocket Connection** (Replace SSE for mobile):

```typescript
// Web: Server-Sent Events (SSE)
const eventSource = new EventSource('/api/m0-stream');

// Mobile: WebSocket (better mobile support)
import { io } from 'socket.io-client';

const socket = io(API_BASE_URL, {
  transports: ['websocket'],
  reconnection: true,
  reconnectionDelay: 1000,
  reconnectionAttempts: 5,
});

// Subscribe to real-time updates
socket.on('component-status-change', (data) => {
  dispatch(updateComponentStatus(data));
});

socket.on('health-score-change', (data) => {
  dispatch(updateHealthScore(data));
});

socket.on('error-detected', (data) => {
  dispatch(addAlert(data));
  // Show push notification
  showPushNotification({
    title: 'Error Detected',
    body: data.message,
    data: { componentId: data.componentId },
  });
});
```

### **API Client Architecture**

**RTK Query API Slices**:

```typescript
// src/store/api/securityApi.ts
export const securityApi = createApi({
  reducerPath: 'securityApi',
  baseQuery: fetchBaseQuery({
    baseUrl: API_BASE_URL,
    prepareHeaders: (headers) => {
      // Add authentication token
      const token = getAuthToken();
      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['Security'],
  endpoints: (builder) => ({
    getSecurityData: builder.query<ISecurityData, void>({
      query: () => '/m0-security',
      providesTags: ['Security'],
    }),
    runSecurityOperation: builder.mutation<IOperationResult, ISecurityOperation>({
      query: (operation) => ({
        url: '/m0-security',
        method: 'POST',
        body: operation,
      }),
      invalidatesTags: ['Security'],
    }),
  }),
});

// Auto-generated hooks
export const { useGetSecurityDataQuery, useRunSecurityOperationMutation } = securityApi;
```

### **Network Error Handling**

**Retry Strategy**:
```typescript
const customBaseQuery = retry(
  fetchBaseQuery({ baseUrl: API_BASE_URL }),
  {
    maxRetries: 3,
    backoff: (attempt) => {
      // Exponential backoff: 1s, 2s, 4s
      return Math.pow(2, attempt) * 1000;
    },
  }
);
```

**Offline Detection**:
```typescript
import NetInfo from '@react-native-community/netinfo';

// Monitor network status
NetInfo.addEventListener(state => {
  dispatch(setNetworkStatus({
    isConnected: state.isConnected,
    isInternetReachable: state.isInternetReachable,
    type: state.type,
  }));

  if (state.isConnected) {
    // Sync cached data when back online
    dispatch(syncCachedData());
  }
});
```

---

## 6️⃣ **UI/UX DESIGN SPECIFICATIONS**

### **Design System**

**OA Framework Branding** (from existing M0 Real Dashboard):

| Element | Specification | Usage |
|---------|---------------|-------|
| **Primary Color** | Electric Blue `#0075FF` | Primary actions, active states, links |
| **Secondary Color** | Purple `#7928CA` | Secondary actions, accents |
| **Success Color** | Cyan `#01B574` | Success states, healthy status |
| **Warning Color** | Orange `#F49342` | Warning states, alerts |
| **Error Color** | Pink `#E31A89` | Error states, critical alerts |
| **Background Dark** | Dark Navy `#0B1437` | Primary background (dark mode) |
| **Background Light** | White `#FFFFFF` | Primary background (light mode) |
| **Card Background** | `#060B28` (dark) / `#F5F5F5` (light) | Card backgrounds |
| **Text Primary** | `#FFFFFF` (dark) / `#000000` (light) | Primary text |
| **Text Secondary** | `rgba(255,255,255,0.7)` (dark) / `rgba(0,0,0,0.6)` (light) | Secondary text |

**Typography**:
```typescript
const typography = {
  h1: { fontSize: 32, fontWeight: '700', lineHeight: 40 },
  h2: { fontSize: 28, fontWeight: '700', lineHeight: 36 },
  h3: { fontSize: 24, fontWeight: '600', lineHeight: 32 },
  h4: { fontSize: 20, fontWeight: '600', lineHeight: 28 },
  h5: { fontSize: 18, fontWeight: '600', lineHeight: 24 },
  h6: { fontSize: 16, fontWeight: '600', lineHeight: 22 },
  body1: { fontSize: 16, fontWeight: '400', lineHeight: 24 },
  body2: { fontSize: 14, fontWeight: '400', lineHeight: 20 },
  caption: { fontSize: 12, fontWeight: '400', lineHeight: 16 },
  button: { fontSize: 16, fontWeight: '600', lineHeight: 24 },
};
```

**Spacing System** (8px base unit):
```typescript
const spacing = {
  xs: 4,   // 0.5 units
  sm: 8,   // 1 unit
  md: 16,  // 2 units
  lg: 24,  // 3 units
  xl: 32,  // 4 units
  xxl: 48, // 6 units
};
```

**Border Radius**:
```typescript
const borderRadius = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  full: 9999,
};
```

### **Mobile-First Design Patterns**

**1. Bottom Navigation** (Primary navigation):
```
┌─────────────────────────┐
│                         │
│   Dashboard Content     │
│                         │
├─────────────────────────┤
│ 🛡️  📊  📈  🔗         │ ← Bottom tabs
│ Sec  Gov  Trk  Int      │
└─────────────────────────┘
```

**2. Pull-to-Refresh** (Data refresh):
```
┌─────────────────────────┐
│   ↓ Pull to refresh     │ ← Pull indicator
├─────────────────────────┤
│   Dashboard Content     │
│                         │
└─────────────────────────┘
```

**3. Bottom Sheet** (Actions and operations):
```
┌─────────────────────────┐
│   Dashboard Content     │
├─────────────────────────┤
│ ╭───────────────────╮   │ ← Bottom sheet
│ │ Security Operations│   │
│ │ ├─ Memory Scan     │   │
│ │ ├─ Buffer Analysis │   │
│ │ └─ Security Audit  │   │
│ ╰───────────────────╯   │
└─────────────────────────┘
```

**4. Swipeable Cards** (Quick actions):
```
┌─────────────────────────┐
│ ← Swipe for actions     │
│ Component Name      ✅  │ ← Swipe left for delete
│ Health: 100%            │   Swipe right for details
└─────────────────────────┘
```

**5. Floating Action Button** (Primary action):
```
┌─────────────────────────┐
│   Dashboard Content     │
│                         │
│                    ⊕    │ ← FAB for quick refresh
└─────────────────────────┘
```

### **Responsive Layouts**

**Phone Layout** (320-428px width):
- Single column layout
- Bottom tab navigation
- Compact cards
- Scrollable lists

**Tablet Layout** (768-1024px width):
- Two-column layout
- Side drawer navigation
- Expanded cards
- Grid views

**Landscape Mode**:
- Horizontal scrolling for charts
- Side-by-side card layout
- Expanded navigation drawer

### **Accessibility Compliance (WCAG 2.1 AA)**

**Requirements**:
- ✅ Minimum contrast ratio 4.5:1 for text
- ✅ Touch targets minimum 44x44 points
- ✅ Screen reader support (VoiceOver, TalkBack)
- ✅ Dynamic font sizing support
- ✅ Keyboard navigation support
- ✅ Focus indicators for interactive elements
- ✅ Alternative text for images and icons
- ✅ Semantic HTML/native components

**Implementation**:
```typescript
// Accessible button component
<TouchableOpacity
  accessible={true}
  accessibilityLabel="Refresh security data"
  accessibilityHint="Double tap to refresh the security dashboard"
  accessibilityRole="button"
  onPress={handleRefresh}
>
  <RefreshIcon />
</TouchableOpacity>

// Accessible text with dynamic sizing
<Text
  style={[styles.title, { fontSize: fontScale * 20 }]}
  accessibilityRole="header"
>
  Security Dashboard
</Text>
```

### **Animation & Transitions**

**Screen Transitions**:
```typescript
// Stack navigation transitions
const screenOptions = {
  headerShown: true,
  cardStyleInterpolator: CardStyleInterpolators.forHorizontalIOS,
  transitionSpec: {
    open: { animation: 'timing', config: { duration: 300 } },
    close: { animation: 'timing', config: { duration: 300 } },
  },
};
```

**Component Animations**:
```typescript
// Fade in animation for cards
const fadeIn = useRef(new Animated.Value(0)).current;

useEffect(() => {
  Animated.timing(fadeIn, {
    toValue: 1,
    duration: 300,
    useNativeDriver: true,
  }).start();
}, []);

// Slide up animation for bottom sheet
const slideUp = useRef(new Animated.Value(300)).current;

const showBottomSheet = () => {
  Animated.spring(slideUp, {
    toValue: 0,
    useNativeDriver: true,
  }).start();
};
```

**Loading States**:
- Skeleton loaders for content
- Shimmer effect for loading cards
- Spinner for operations
- Progress bars for long operations

### **Dark Mode Support**

**Theme Configuration**:
```typescript
const lightTheme = {
  colors: {
    primary: '#0075FF',
    background: '#FFFFFF',
    card: '#F5F5F5',
    text: '#000000',
    border: '#E0E0E0',
  },
};

const darkTheme = {
  colors: {
    primary: '#0075FF',
    background: '#0B1437',
    card: '#060B28',
    text: '#FFFFFF',
    border: '#1E2A4A',
  },
};

// Auto-detect system theme
const colorScheme = useColorScheme();
const theme = colorScheme === 'dark' ? darkTheme : lightTheme;
```

---

## 7️⃣ **DEVELOPMENT TIMELINE**

### **Phase-by-Phase Implementation Schedule**

**Total Duration**: 10-12 weeks
**Team Size**: 1 Senior Mobile Developer + 1 AI Assistant
**Work Schedule**: 40 hours/week

### **Phase 1: Project Setup & Foundation (Week 1-2)**

**Duration**: 2 weeks (80 hours)

| Task | Effort | Deliverable | Status |
|------|--------|-------------|--------|
| **1.1 Project Initialization** | 8h | Expo project with TypeScript | ⏳ Pending |
| - Initialize Expo managed workflow | 2h | `expo init` with TypeScript template | |
| - Configure TypeScript strict mode | 2h | `tsconfig.json` with strict settings | |
| - Set up ESLint + Prettier | 2h | Code quality tools | |
| - Configure Git repository | 2h | `.gitignore`, branch strategy | |
| **1.2 Navigation Setup** | 12h | Navigation structure | ⏳ Pending |
| - Install React Navigation | 2h | Navigation dependencies | |
| - Create root navigator | 4h | Stack + Tab navigation | |
| - Create dashboard navigator | 4h | Bottom tab navigation | |
| - Configure deep linking | 2h | URL scheme configuration | |
| **1.3 State Management** | 16h | Redux store with RTK Query | ⏳ Pending |
| - Install Redux Toolkit | 2h | Redux dependencies | |
| - Configure store | 4h | Store setup with middleware | |
| - Create API slices | 6h | RTK Query API definitions | |
| - Set up offline storage | 4h | MMKV integration | |
| **1.4 Theme & Design System** | 12h | Theme configuration | ⏳ Pending |
| - Install React Native Paper | 2h | UI component library | |
| - Configure OA Framework theme | 4h | Colors, typography, spacing | |
| - Create theme provider | 2h | Theme context and hooks | |
| - Implement dark mode | 4h | Dark/light theme switching | |
| **1.5 API Client Setup** | 16h | API integration layer | ⏳ Pending |
| - Configure API base URL | 2h | Environment configuration | |
| - Create API client | 6h | Axios + interceptors | |
| - Implement error handling | 4h | Error boundaries, retry logic | |
| - Set up network monitoring | 4h | NetInfo integration | |
| **1.6 Testing Infrastructure** | 16h | Testing setup | ⏳ Pending |
| - Configure Jest | 4h | Jest configuration | |
| - Set up React Native Testing Library | 4h | Testing utilities | |
| - Create test utilities | 4h | Mock data, test helpers | |
| - Write sample tests | 4h | Example test cases | |

**Phase 1 Deliverables**:
- ✅ Expo project with TypeScript strict mode
- ✅ Navigation structure (Stack + Tab)
- ✅ Redux store with RTK Query
- ✅ OA Framework theme configuration
- ✅ API client with error handling
- ✅ Testing infrastructure

**Phase 1 Success Criteria**:
- [ ] Project builds successfully on iOS and Android
- [ ] Navigation works between screens
- [ ] Redux DevTools shows state updates
- [ ] API client can connect to backend
- [ ] Tests run successfully with >80% coverage

---

### **Phase 2: Security Dashboard (Week 3-4)**

**Duration**: 2 weeks (80 hours)

| Task | Effort | Deliverable | Status |
|------|--------|-------------|--------|
| **2.1 Security Data Integration** | 12h | Security API integration | ⏳ Pending |
| - Create security API slice | 4h | RTK Query endpoints | |
| - Implement data fetching hooks | 4h | `useSecurityData` hook | |
| - Add offline caching | 4h | MMKV cache layer | |
| **2.2 Security Overview Screen** | 16h | Main security screen | ⏳ Pending |
| - Create screen component | 4h | Screen layout | |
| - Implement overview card | 4h | Metrics display | |
| - Add pull-to-refresh | 4h | Refresh functionality | |
| - Implement error handling | 4h | Error states | |
| **2.3 Memory Usage Chart** | 12h | Memory monitoring chart | ⏳ Pending |
| - Install Victory Native | 2h | Chart library | |
| - Create chart component | 6h | Line chart with data | |
| - Add chart interactions | 4h | Zoom, pan gestures | |
| **2.4 Component Status List** | 12h | Component list view | ⏳ Pending |
| - Create list component | 4h | FlatList with items | |
| - Add filtering | 4h | Filter by status | |
| - Implement search | 4h | Search functionality | |
| **2.5 Security Operations** | 16h | Operations bottom sheet | ⏳ Pending |
| - Create bottom sheet | 4h | Bottom sheet component | |
| - Implement operations | 8h | Memory scan, buffer analysis | |
| - Add operation feedback | 4h | Loading, success, error states | |
| **2.6 Alert Management** | 12h | Alert list and notifications | ⏳ Pending |
| - Create alert list | 4h | Alert history list | |
| - Implement push notifications | 6h | Firebase Cloud Messaging | |
| - Add notification handling | 2h | Notification tap actions | |

**Phase 2 Deliverables**:
- ✅ Security Dashboard screen
- ✅ Memory usage chart
- ✅ Component status list
- ✅ Security operations
- ✅ Alert management
- ✅ Push notifications

**Phase 2 Success Criteria**:
- [ ] Security dashboard displays real data from backend
- [ ] Charts render correctly with smooth animations
- [ ] Operations execute successfully
- [ ] Push notifications work on iOS and Android
- [ ] Offline mode shows cached data

---

### **Phase 3: Governance Dashboard (Week 5-6)**

**Duration**: 2 weeks (80 hours)

| Task | Effort | Deliverable | Status |
|------|--------|-------------|--------|
| **3.1 Governance Data Integration** | 12h | Governance API integration | ⏳ Pending |
| **3.2 Governance Overview Screen** | 16h | Main governance screen | ⏳ Pending |
| **3.3 Compliance Score Display** | 12h | Circular progress indicator | ⏳ Pending |
| **3.4 Framework Status List** | 12h | Framework status view | ⏳ Pending |
| **3.5 Violations Management** | 16h | Violations accordion | ⏳ Pending |
| **3.6 Governance Operations** | 12h | Operations bottom sheet | ⏳ Pending |

**Phase 3 Deliverables**:
- ✅ Governance Dashboard screen
- ✅ Compliance score display
- ✅ Framework status list
- ✅ Violations management
- ✅ Governance operations

---

### **Phase 4: Tracking Dashboard (Week 7-8)**

**Duration**: 2 weeks (80 hours)

| Task | Effort | Deliverable | Status |
|------|--------|-------------|--------|
| **4.1 Tracking Data Integration** | 12h | Tracking API integration | ⏳ Pending |
| **4.2 Tracking Overview Screen** | 16h | Main tracking screen | ⏳ Pending |
| **4.3 Session Analytics Chart** | 12h | Bar chart component | ⏳ Pending |
| **4.4 Component Health Monitor** | 12h | Health status list | ⏳ Pending |
| **4.5 Event Timeline** | 16h | Timeline list view | ⏳ Pending |
| **4.6 Tracking Operations** | 12h | Operations bottom sheet | ⏳ Pending |

**Phase 4 Deliverables**:
- ✅ Tracking Dashboard screen
- ✅ Session analytics chart
- ✅ Component health monitor
- ✅ Event timeline
- ✅ Tracking operations

---

### **Phase 5: Integration Console (Week 9-10)**

**Duration**: 2 weeks (80 hours)

| Task | Effort | Deliverable | Status |
|------|--------|-------------|--------|
| **5.1 Integration Data Integration** | 12h | Integration API integration | ⏳ Pending |
| **5.2 Integration Overview Screen** | 16h | Main integration screen | ⏳ Pending |
| **5.3 Dependency Tree View** | 16h | Collapsible tree component | ⏳ Pending |
| **5.4 Integration Status List** | 12h | Status list view | ⏳ Pending |
| **5.5 Cross-Component Testing** | 16h | Test execution UI | ⏳ Pending |
| **5.6 Test Results Display** | 8h | Results modal | ⏳ Pending |

**Phase 5 Deliverables**:
- ✅ Integration Console screen
- ✅ Dependency tree view
- ✅ Integration status list
- ✅ Cross-component testing
- ✅ Test results display

---

### **Phase 6: Polish & Deployment (Week 11-12)**

**Duration**: 2 weeks (80 hours)

| Task | Effort | Deliverable | Status |
|------|--------|-------------|--------|
| **6.1 Performance Optimization** | 16h | Optimized app | ⏳ Pending |
| - Profile app performance | 4h | Performance metrics | |
| - Optimize re-renders | 4h | React.memo, useMemo | |
| - Optimize bundle size | 4h | Code splitting | |
| - Optimize images | 4h | Image compression | |
| **6.2 Testing & QA** | 24h | Comprehensive testing | ⏳ Pending |
| - Write unit tests | 8h | Component tests | |
| - Write integration tests | 8h | API integration tests | |
| - Manual testing | 8h | Device testing | |
| **6.3 Documentation** | 12h | User and developer docs | ⏳ Pending |
| - User guide | 4h | End-user documentation | |
| - Developer guide | 4h | Setup and development docs | |
| - API documentation | 4h | API integration docs | |
| **6.4 App Store Preparation** | 16h | Store assets | ⏳ Pending |
| - Create app icons | 4h | Icon design and generation | |
| - Create screenshots | 4h | Store screenshots | |
| - Write store descriptions | 4h | App descriptions | |
| - Prepare privacy policy | 4h | Legal documentation | |
| **6.5 APK/IPA Generation** | 16h | Production builds | ⏳ Pending |
| - Configure Android APK build | 4h | APK build configuration | |
| - Generate signed APK | 2h | Release APK generation | |
| - Configure iOS IPA build | 4h | IPA build configuration | |
| - Generate signed IPA | 2h | Release IPA generation | |
| - Test APK on devices | 2h | Android device testing | |
| - Test IPA on devices | 2h | iOS device testing | |
| **6.6 App Store Submission** | 8h | Store deployment | ⏳ Pending |
| - Prepare App Store metadata | 2h | iOS store listing | |
| - Prepare Play Store metadata | 2h | Android store listing | |
| - Submit IPA to App Store | 2h | iOS submission | |
| - Submit APK to Play Store | 2h | Android submission | |

**Phase 6 Deliverables**:
- ✅ Optimized production app
- ✅ Comprehensive test coverage (>80%)
- ✅ Complete documentation
- ✅ App store assets
- ✅ **Signed APK for Android** (release-ready)
- ✅ **Signed IPA for iOS** (release-ready)
- ✅ Production builds submitted to app stores

---

## 8️⃣ **TESTING STRATEGY**

### **Testing Pyramid**

```
        ┌─────────────┐
        │   E2E (5%)  │  ← Detox (critical user flows)
        ├─────────────┤
        │ Integration │  ← React Native Testing Library (20%)
        │    (20%)    │
        ├─────────────┤
        │   Unit      │  ← Jest (75%)
        │   (75%)     │
        └─────────────┘
```

### **Unit Testing (75% of tests)**

**Target Coverage**: 80%+ code coverage

**Testing Tools**:
- Jest (test runner)
- React Native Testing Library (component testing)
- @testing-library/react-hooks (hook testing)

**Test Categories**:

**1. Component Tests**:
```typescript
// src/components/SecurityOverviewCard.test.tsx
import { render, screen } from '@testing-library/react-native';
import { SecurityOverviewCard } from './SecurityOverviewCard';

describe('SecurityOverviewCard', () => {
  it('should render security metrics correctly', () => {
    const mockData = {
      totalComponents: 136,
      healthyComponents: 130,
      healthScore: 95.6,
    };

    render(<SecurityOverviewCard data={mockData} />);

    expect(screen.getByText('136')).toBeTruthy();
    expect(screen.getByText('95.6%')).toBeTruthy();
  });

  it('should show loading state', () => {
    render(<SecurityOverviewCard loading={true} />);
    expect(screen.getByTestId('loading-spinner')).toBeTruthy();
  });

  it('should show error state', () => {
    render(<SecurityOverviewCard error="Failed to load data" />);
    expect(screen.getByText('Failed to load data')).toBeTruthy();
  });
});
```

**2. Hook Tests**:
```typescript
// src/hooks/useSecurityData.test.ts
import { renderHook, waitFor } from '@testing-library/react-hooks';
import { useSecurityData } from './useSecurityData';

describe('useSecurityData', () => {
  it('should fetch security data successfully', async () => {
    const { result } = renderHook(() => useSecurityData());

    await waitFor(() => expect(result.current.loading).toBe(false));

    expect(result.current.data).toBeDefined();
    expect(result.current.error).toBeNull();
  });

  it('should handle API errors', async () => {
    // Mock API failure
    jest.spyOn(global, 'fetch').mockRejectedValueOnce(new Error('API Error'));

    const { result } = renderHook(() => useSecurityData());

    await waitFor(() => expect(result.current.loading).toBe(false));

    expect(result.current.error).toBe('API Error');
    expect(result.current.data).toBeNull();
  });
});
```

**3. Redux Tests**:
```typescript
// src/store/slices/security.test.ts
import { configureStore } from '@reduxjs/toolkit';
import securityReducer, { updateSecurityData } from './securitySlice';

describe('securitySlice', () => {
  it('should update security data', () => {
    const store = configureStore({ reducer: { security: securityReducer } });

    const mockData = { totalComponents: 136, healthScore: 95.6 };
    store.dispatch(updateSecurityData(mockData));

    expect(store.getState().security.data).toEqual(mockData);
  });
});
```

**4. Utility Tests**:
```typescript
// src/utils/formatters.test.ts
import { formatHealthScore, formatTimestamp } from './formatters';

describe('formatters', () => {
  it('should format health score correctly', () => {
    expect(formatHealthScore(95.678)).toBe('95.7%');
    expect(formatHealthScore(100)).toBe('100%');
  });

  it('should format timestamp correctly', () => {
    const timestamp = new Date('2026-01-12T10:30:00Z');
    expect(formatTimestamp(timestamp)).toBe('10:30 AM');
  });
});
```

### **Integration Testing (20% of tests)**

**Target**: Test component interactions and API integration

**Test Categories**:

**1. Screen Integration Tests**:
```typescript
// src/screens/SecurityDashboard/SecurityDashboard.test.tsx
import { render, screen, waitFor } from '@testing-library/react-native';
import { SecurityDashboard } from './SecurityDashboard';
import { Provider } from 'react-redux';
import { store } from '../../store';

describe('SecurityDashboard Integration', () => {
  it('should load and display security data', async () => {
    render(
      <Provider store={store}>
        <SecurityDashboard />
      </Provider>
    );

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('136')).toBeTruthy();
    });

    // Verify all sections are rendered
    expect(screen.getByText('Memory Usage')).toBeTruthy();
    expect(screen.getByText('Component Status')).toBeTruthy();
  });
});
```

**2. API Integration Tests**:
```typescript
// src/store/api/securityApi.test.ts
import { setupApiStore } from '../../test-utils/setupApiStore';
import { securityApi } from './securityApi';

describe('securityApi Integration', () => {
  it('should fetch security data from API', async () => {
    const storeRef = setupApiStore(securityApi);

    const result = await storeRef.store.dispatch(
      securityApi.endpoints.getSecurityData.initiate()
    );

    expect(result.data).toBeDefined();
    expect(result.data.totalComponents).toBe(136);
  });
});
```

**3. Navigation Tests**:
```typescript
// src/navigation/RootNavigator.test.tsx
import { render, fireEvent } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
import { RootNavigator } from './RootNavigator';

describe('Navigation Integration', () => {
  it('should navigate between dashboards', () => {
    const { getByText } = render(
      <NavigationContainer>
        <RootNavigator />
      </NavigationContainer>
    );

    // Tap on Governance tab
    fireEvent.press(getByText('Governance'));

    // Verify navigation
    expect(getByText('Governance Dashboard')).toBeTruthy();
  });
});
```

### **End-to-End Testing (5% of tests)**

**Target**: Test critical user flows on real devices

**Testing Tool**: Detox (React Native E2E testing framework)

**Test Scenarios**:

**1. Dashboard Navigation Flow**:
```typescript
// e2e/dashboardNavigation.e2e.ts
describe('Dashboard Navigation', () => {
  beforeAll(async () => {
    await device.launchApp();
  });

  it('should navigate through all dashboards', async () => {
    // Start on Security dashboard
    await expect(element(by.text('Security Dashboard'))).toBeVisible();

    // Navigate to Governance
    await element(by.text('Governance')).tap();
    await expect(element(by.text('Governance Dashboard'))).toBeVisible();

    // Navigate to Tracking
    await element(by.text('Tracking')).tap();
    await expect(element(by.text('Tracking Dashboard'))).toBeVisible();

    // Navigate to Integration
    await element(by.text('Integration')).tap();
    await expect(element(by.text('Integration Console'))).toBeVisible();
  });
});
```

**2. Data Refresh Flow**:
```typescript
// e2e/dataRefresh.e2e.ts
describe('Data Refresh', () => {
  it('should refresh dashboard data', async () => {
    await element(by.id('security-dashboard')).swipe('down', 'fast');
    await waitFor(element(by.id('refresh-indicator')))
      .toBeVisible()
      .withTimeout(2000);
    await waitFor(element(by.id('refresh-indicator')))
      .not.toBeVisible()
      .withTimeout(5000);
  });
});
```

**3. Offline Mode Flow**:
```typescript
// e2e/offlineMode.e2e.ts
describe('Offline Mode', () => {
  it('should show cached data when offline', async () => {
    // Load data while online
    await expect(element(by.text('136'))).toBeVisible();

    // Go offline
    await device.setNetworkCondition('offline');

    // Refresh app
    await device.reloadReactNative();

    // Verify cached data is shown
    await expect(element(by.text('136'))).toBeVisible();
    await expect(element(by.text('Offline Mode'))).toBeVisible();
  });
});
```

### **Performance Testing**

**Metrics to Monitor**:
- App launch time: <2 seconds
- Screen navigation time: <300ms
- API response time: <500ms
- Memory usage: <150MB
- CPU usage: <30% average
- Battery drain: <5% per hour

**Performance Testing Tools**:
- React Native Performance Monitor
- Flipper (debugging and profiling)
- Xcode Instruments (iOS)
- Android Profiler (Android)

**Performance Tests**:
```typescript
// __tests__/performance/appLaunch.perf.test.ts
import { measurePerformance } from 'react-native-performance';

describe('Performance Tests', () => {
  it('should launch app within 2 seconds', async () => {
    const startTime = Date.now();
    await device.launchApp();
    const launchTime = Date.now() - startTime;

    expect(launchTime).toBeLessThan(2000);
  });

  it('should navigate between screens within 300ms', async () => {
    const startTime = Date.now();
    await element(by.text('Governance')).tap();
    const navigationTime = Date.now() - startTime;

    expect(navigationTime).toBeLessThan(300);
  });
});
```

### **Device Testing Matrix**

**iOS Devices**:
- iPhone SE (2nd gen) - iOS 13.0 (minimum supported)
- iPhone 12 - iOS 15.0
- iPhone 14 Pro - iOS 16.0
- iPhone 15 Pro Max - iOS 17.0 (latest)
- iPad Air (4th gen) - iPadOS 15.0
- iPad Pro 12.9" - iPadOS 17.0

**Android Devices**:
- Samsung Galaxy S10 - Android 8.0 (minimum supported)
- Google Pixel 5 - Android 11.0
- Samsung Galaxy S21 - Android 12.0
- Google Pixel 7 Pro - Android 13.0
- Samsung Galaxy Tab S8 - Android 12.0

**Testing Checklist**:
- [ ] App installs successfully
- [ ] All dashboards load correctly
- [ ] Charts render properly
- [ ] Navigation works smoothly
- [ ] Pull-to-refresh works
- [ ] Push notifications work
- [ ] Offline mode works
- [ ] Dark mode works
- [ ] Accessibility features work
- [ ] No crashes or freezes

---

## 9️⃣ **DEPLOYMENT PLAN**

### **Build Configuration**

**Environment Configuration**:

```typescript
// app.config.ts
export default {
  expo: {
    name: 'OA Framework Mobile',
    slug: 'oa-framework-mobile',
    version: '1.0.0',
    orientation: 'portrait',
    icon: './assets/icon.png',
    userInterfaceStyle: 'automatic',
    splash: {
      image: './assets/splash.png',
      resizeMode: 'contain',
      backgroundColor: '#0B1437',
    },
    updates: {
      fallbackToCacheTimeout: 0,
    },
    assetBundlePatterns: ['**/*'],
    ios: {
      supportsTablet: true,
      bundleIdentifier: 'com.ezconsultancy.oaframework',
      buildNumber: '1',
      infoPlist: {
        NSCameraUsageDescription: 'This app does not use the camera.',
        NSMicrophoneUsageDescription: 'This app does not use the microphone.',
      },
    },
    android: {
      package: 'com.ezconsultancy.oaframework',
      versionCode: 1,
      adaptiveIcon: {
        foregroundImage: './assets/adaptive-icon.png',
        backgroundColor: '#0B1437',
      },
      permissions: [
        'INTERNET',
        'ACCESS_NETWORK_STATE',
        'RECEIVE_BOOT_COMPLETED',
      ],
    },
    extra: {
      apiBaseUrl: process.env.API_BASE_URL || 'http://localhost:3000/api',
      eas: {
        projectId: 'your-project-id',
      },
    },
  },
};
```

**Environment Variables**:

```bash
# .env.development
API_BASE_URL=http://localhost:3000/api
ENABLE_REDUX_DEVTOOLS=true
LOG_LEVEL=debug

# .env.staging
API_BASE_URL=https://staging.oa-framework.com/api
ENABLE_REDUX_DEVTOOLS=true
LOG_LEVEL=info

# .env.production
API_BASE_URL=https://api.oa-framework.com/api
ENABLE_REDUX_DEVTOOLS=false
LOG_LEVEL=error
```

### **APK/IPA Generation Process**

**Android APK Generation**:

**Step 1: Configure Signing**

Create `android/app/build.gradle` signing configuration:
```gradle
android {
    signingConfigs {
        release {
            if (project.hasProperty('MYAPP_RELEASE_STORE_FILE')) {
                storeFile file(MYAPP_RELEASE_STORE_FILE)
                storePassword MYAPP_RELEASE_STORE_PASSWORD
                keyAlias MYAPP_RELEASE_KEY_ALIAS
                keyPassword MYAPP_RELEASE_KEY_PASSWORD
            }
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}
```

**Step 2: Generate Keystore**
```bash
# Generate release keystore
keytool -genkeypair -v -storetype PKCS12 \
  -keystore oa-framework-release.keystore \
  -alias oa-framework-key \
  -keyalg RSA -keysize 2048 -validity 10000

# Store credentials in gradle.properties
echo "MYAPP_RELEASE_STORE_FILE=oa-framework-release.keystore" >> ~/.gradle/gradle.properties
echo "MYAPP_RELEASE_KEY_ALIAS=oa-framework-key" >> ~/.gradle/gradle.properties
echo "MYAPP_RELEASE_STORE_PASSWORD=your_store_password" >> ~/.gradle/gradle.properties
echo "MYAPP_RELEASE_KEY_PASSWORD=your_key_password" >> ~/.gradle/gradle.properties
```

**Step 3: Build APK**
```bash
# Using EAS Build
eas build --platform android --profile production

# Or using local build
cd android
./gradlew assembleRelease

# APK location: android/app/build/outputs/apk/release/app-release.apk
```

**Step 4: Build AAB (Android App Bundle)**
```bash
# Build AAB for Play Store
cd android
./gradlew bundleRelease

# AAB location: android/app/build/outputs/bundle/release/app-release.aab
```

**iOS IPA Generation**:

**Step 1: Configure Code Signing**

In Xcode:
1. Open `ios/OAFrameworkMobile.xcworkspace`
2. Select project → Signing & Capabilities
3. Select team and provisioning profile
4. Enable "Automatically manage signing" or use manual provisioning

**Step 2: Create Archive**
```bash
# Using EAS Build
eas build --platform ios --profile production

# Or using Xcode command line
xcodebuild -workspace ios/OAFrameworkMobile.xcworkspace \
  -scheme OAFrameworkMobile \
  -configuration Release \
  -archivePath build/OAFrameworkMobile.xcarchive \
  archive

# Export IPA
xcodebuild -exportArchive \
  -archivePath build/OAFrameworkMobile.xcarchive \
  -exportPath build \
  -exportOptionsPlist ExportOptions.plist
```

**Step 3: Create ExportOptions.plist**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>method</key>
    <string>app-store</string>
    <key>teamID</key>
    <string>YOUR_TEAM_ID</string>
    <key>uploadBitcode</key>
    <false/>
    <key>compileBitcode</key>
    <false/>
    <key>uploadSymbols</key>
    <true/>
</dict>
</plist>
```

**Build Artifacts**:

| Platform | File Type | Location | Size (Est.) | Purpose |
|----------|-----------|----------|-------------|---------|
| **Android** | APK | `android/app/build/outputs/apk/release/` | ~30MB | Direct installation |
| **Android** | AAB | `android/app/build/outputs/bundle/release/` | ~25MB | Play Store submission |
| **iOS** | IPA | `build/OAFrameworkMobile.ipa` | ~50MB | App Store submission |
| **iOS** | dSYM | `build/OAFrameworkMobile.app.dSYM.zip` | ~5MB | Crash symbolication |

---

### **Build Process**

**EAS Build Configuration** (`eas.json`):

```json
{
  "cli": {
    "version": ">= 5.0.0"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal",
      "ios": {
        "simulator": true
      }
    },
    "preview": {
      "distribution": "internal",
      "ios": {
        "simulator": false
      }
    },
    "production": {
      "distribution": "store",
      "autoIncrement": true
    }
  },
  "submit": {
    "production": {
      "ios": {
        "appleId": "<EMAIL>",
        "ascAppId": "**********",
        "appleTeamId": "ABCDE12345"
      },
      "android": {
        "serviceAccountKeyPath": "./google-play-service-account.json",
        "track": "production"
      }
    }
  }
}
```

**Build Commands**:

```bash
# Development build (for testing)
eas build --profile development --platform all

# Preview build (for internal testing - generates APK/IPA)
eas build --profile preview --platform all

# Production build (for app stores - generates signed APK/AAB/IPA)
eas build --profile production --platform all

# Android-only APK build
eas build --profile production --platform android

# iOS-only IPA build
eas build --profile production --platform ios
```

---

### **APK/IPA Testing & Distribution**

**Internal Testing (Before App Store Submission)**:

**Android APK Testing**:
```bash
# Install APK on connected device
adb install android/app/build/outputs/apk/release/app-release.apk

# Or use EAS Build download link
# Download APK from EAS Build dashboard
# Transfer to device and install

# Test checklist:
# - App launches successfully
# - All dashboards load
# - API connections work
# - Push notifications work
# - Offline mode works
# - No crashes or errors
```

**iOS IPA Testing (TestFlight)**:
```bash
# Upload to TestFlight for internal testing
eas submit --platform ios --profile preview

# Or use Xcode
# 1. Open Xcode
# 2. Window → Organizer
# 3. Select archive
# 4. Distribute App → TestFlight
# 5. Upload to App Store Connect

# Invite internal testers via App Store Connect
# Test on real devices before production release
```

**Distribution Methods**:

| Method | Platform | Use Case | Setup Time |
|--------|----------|----------|------------|
| **Direct APK** | Android | Internal testing, demos | 5 minutes |
| **TestFlight** | iOS | Internal/external testing | 1 hour |
| **Firebase App Distribution** | Both | Team distribution | 30 minutes |
| **Google Play Internal Testing** | Android | Pre-release testing | 1 hour |
| **App Store TestFlight** | iOS | Beta testing | 1 hour |

**Firebase App Distribution Setup** (Recommended for internal testing):

```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login to Firebase
firebase login

# Initialize Firebase App Distribution
firebase init appdistribution

# Distribute Android APK
firebase appdistribution:distribute \
  android/app/build/outputs/apk/release/app-release.apk \
  --app YOUR_ANDROID_APP_ID \
  --groups "internal-testers" \
  --release-notes "M0 Mobile App - Internal Testing Build"

# Distribute iOS IPA
firebase appdistribution:distribute \
  build/OAFrameworkMobile.ipa \
  --app YOUR_IOS_APP_ID \
  --groups "internal-testers" \
  --release-notes "M0 Mobile App - Internal Testing Build"
```

**APK/IPA Verification Checklist**:

Before submitting to app stores:
- [ ] APK/IPA installs successfully on test devices
- [ ] App launches within 2 seconds
- [ ] All 4 dashboards load correctly
- [ ] API connections work (production backend)
- [ ] Charts render properly
- [ ] Push notifications work
- [ ] Offline mode functions correctly
- [ ] Dark mode works
- [ ] No crashes during 30-minute test session
- [ ] Memory usage <150MB
- [ ] Battery drain <5% per hour
- [ ] App size within limits (APK <30MB, IPA <50MB)
- [ ] All permissions work correctly
- [ ] Deep linking works
- [ ] App icon displays correctly
- [ ] Splash screen displays correctly

### **App Store Submission**

**iOS App Store**:

**Requirements**:
- Apple Developer Account ($99/year)
- App Store Connect access
- App icons (1024x1024 px)
- Screenshots (various sizes)
- App description and keywords
- Privacy policy URL
- Support URL

**Submission Steps**:
1. Create app in App Store Connect
2. Upload build via EAS Submit or Transporter
3. Fill in app metadata
4. Submit for review
5. Wait for approval (1-3 days)

**App Store Metadata**:
```
App Name: OA Framework Mobile
Subtitle: M0 Milestone Monitoring
Category: Developer Tools
Keywords: monitoring, dashboard, framework, OA, governance
Description: [See App Store Description section below]
```

**Google Play Store**:

**Requirements**:
- Google Play Developer Account ($25 one-time)
- Google Play Console access
- App icons (512x512 px)
- Screenshots (various sizes)
- App description
- Privacy policy URL
- Content rating questionnaire

**Submission Steps**:
1. Create app in Google Play Console
2. Upload AAB via EAS Submit or manual upload
3. Fill in store listing
4. Complete content rating
5. Submit for review
6. Wait for approval (1-7 days)

**Play Store Metadata**:
```
App Name: OA Framework Mobile
Short Description: M0 Milestone Monitoring Dashboard
Category: Tools
Tags: monitoring, dashboard, developer tools
Description: [See Play Store Description section below]
```

### **App Store Descriptions**

**App Store / Play Store Description**:

```
OA Framework Mobile - M0 Milestone Monitoring

Monitor your OA Framework M0 milestone components in real-time from your mobile device.

KEY FEATURES:
• 4 Specialized Dashboards: Security, Governance, Tracking, Integration
• Real-Time Monitoring: Live data from 136 operational M0 components
• Push Notifications: Instant alerts for critical system events
• Offline Mode: Access cached data when offline
• Dark Mode: Comfortable viewing in any lighting condition
• Cross-Platform: Available on iOS and Android

DASHBOARDS:
🛡️ Security Dashboard
- Memory usage monitoring
- Buffer utilization tracking
- Threat level indicators
- Component health status

📊 Governance Dashboard
- Compliance score tracking
- Rule engine monitoring
- Framework status overview
- Violation management

📈 Tracking Dashboard
- Session analytics
- Component health monitoring
- Event timeline
- Progress tracking

🔗 Integration Console
- Integration status monitoring
- Dependency visualization
- Cross-component testing
- System health overview

TECHNICAL EXCELLENCE:
- Enterprise-grade security
- TypeScript strict mode
- WCAG 2.1 AA accessibility
- Material Design UI
- Optimized performance

Perfect for DevOps teams, system administrators, and developers who need mobile access to OA Framework monitoring capabilities.

REQUIREMENTS:
- iOS 13.0+ or Android 8.0+
- Internet connection for real-time data
- OA Framework backend access

SUPPORT:
For support, visit: https://oa-framework.com/support
Privacy Policy: https://oa-framework.com/privacy
```

### **Over-the-Air (OTA) Updates**

**EAS Update Configuration**:

```bash
# Publish OTA update
eas update --branch production --message "Bug fixes and performance improvements"

# Publish to specific channel
eas update --channel production --message "New features"
```

**Update Strategy**:
- **Critical Fixes**: Immediate OTA update
- **Minor Updates**: Weekly OTA updates
- **Major Updates**: App store submission

### **Monitoring & Analytics**

**Tools**:
- **Sentry**: Error tracking and crash reporting
- **Firebase Analytics**: User behavior analytics
- **Firebase Performance**: Performance monitoring
- **App Store Connect**: iOS analytics
- **Google Play Console**: Android analytics

**Metrics to Track**:
- Daily Active Users (DAU)
- Monthly Active Users (MAU)
- Session duration
- Screen views
- Crash-free rate
- API response times
- User retention rate

---

## 🔟 **SUCCESS METRICS**

### **Quantitative Metrics**

| Metric | Target | Measurement Method | Frequency |
|--------|--------|-------------------|-----------|
| **App Store Rating** | 4.5+ stars | App Store / Play Store reviews | Weekly |
| **Crash-Free Rate** | >99.5% | Sentry crash reporting | Daily |
| **API Success Rate** | >99% | API monitoring | Real-time |
| **App Launch Time** | <2 seconds | Performance monitoring | Daily |
| **Screen Load Time** | <500ms | Performance monitoring | Daily |
| **Code Coverage** | >80% | Jest coverage reports | Per commit |
| **User Retention (Day 7)** | >60% | Firebase Analytics | Weekly |
| **User Retention (Day 30)** | >40% | Firebase Analytics | Monthly |
| **Daily Active Users** | 100+ | Firebase Analytics | Daily |
| **Session Duration** | >5 minutes | Firebase Analytics | Daily |

### **Qualitative Metrics**

| Metric | Target | Measurement Method | Frequency |
|--------|--------|-------------------|-----------|
| **User Satisfaction** | 4.5+ / 5.0 | In-app surveys | Monthly |
| **Feature Usefulness** | 4.0+ / 5.0 | User feedback | Monthly |
| **UI/UX Quality** | 4.5+ / 5.0 | User feedback | Monthly |
| **Performance Perception** | "Fast" or "Very Fast" | User surveys | Monthly |
| **Ease of Use** | "Easy" or "Very Easy" | User surveys | Monthly |

### **Business Metrics**

| Metric | Target | Measurement Method | Frequency |
|--------|--------|-------------------|-----------|
| **App Downloads** | 1,000+ in first month | App Store / Play Store | Weekly |
| **Active Installations** | 500+ | App Store / Play Store | Weekly |
| **User Growth Rate** | 20% month-over-month | Analytics | Monthly |
| **Feature Adoption** | >70% use all 4 dashboards | Analytics | Monthly |
| **Push Notification Opt-in** | >60% | Analytics | Weekly |

### **Technical Metrics**

| Metric | Target | Measurement Method | Frequency |
|--------|--------|-------------------|-----------|
| **Build Success Rate** | >95% | CI/CD pipeline | Per build |
| **Test Pass Rate** | 100% | Jest test results | Per commit |
| **TypeScript Errors** | 0 | TypeScript compiler | Per commit |
| **ESLint Warnings** | <10 | ESLint reports | Per commit |
| **Bundle Size (iOS)** | <50MB | Build artifacts | Per release |
| **Bundle Size (Android)** | <30MB | Build artifacts | Per release |
| **Memory Usage** | <150MB | Performance monitoring | Daily |
| **Battery Drain** | <5% per hour | Device testing | Weekly |

### **Milestone Success Criteria**

**Phase 1 Success** (Project Setup):
- ✅ Project builds successfully on iOS and Android
- ✅ Navigation works between all screens
- ✅ Redux store configured and functional
- ✅ API client connects to backend
- ✅ Tests run with >80% coverage

**Phase 2 Success** (Security Dashboard):
- ✅ Security dashboard displays real data
- ✅ Charts render correctly
- ✅ Operations execute successfully
- ✅ Push notifications work
- ✅ Offline mode functional

**Phase 3 Success** (Governance Dashboard):
- ✅ Governance dashboard displays real data
- ✅ Compliance score displays correctly
- ✅ Violations management works
- ✅ Operations execute successfully

**Phase 4 Success** (Tracking Dashboard):
- ✅ Tracking dashboard displays real data
- ✅ Session analytics chart works
- ✅ Event timeline displays correctly
- ✅ Operations execute successfully

**Phase 5 Success** (Integration Console):
- ✅ Integration console displays real data
- ✅ Dependency tree renders correctly
- ✅ Cross-component testing works
- ✅ Test results display properly

**Phase 6 Success** (Deployment):
- ✅ App submitted to App Store and Play Store
- ✅ All tests passing with >80% coverage
- ✅ Documentation complete
- ✅ Performance targets met
- ✅ Zero critical bugs

### **Final Success Criteria**

**Production Readiness Checklist**:
- [ ] All 4 dashboards functional
- [ ] 136 M0 components integrated
- [ ] Real-time data updates working
- [ ] Push notifications operational
- [ ] Offline mode functional
- [ ] Dark mode implemented
- [ ] Accessibility compliance (WCAG 2.1 AA)
- [ ] Performance targets met
- [ ] Test coverage >80%
- [ ] Zero critical bugs
- [ ] Documentation complete
- [ ] App store assets ready
- [ ] Privacy policy published
- [ ] Support infrastructure ready

**Launch Criteria**:
- [ ] App Store approval received
- [ ] Play Store approval received
- [ ] Backend API stable and scalable
- [ ] Monitoring and analytics configured
- [ ] Support team trained
- [ ] Marketing materials ready
- [ ] User onboarding flow tested
- [ ] Rollback plan documented

---

## 📚 **APPENDIX**

### **A. Technology Justification**

**React Native vs. Native Development**:
- ✅ **Code Sharing**: 90%+ code shared between iOS and Android
- ✅ **Development Speed**: 50% faster than native development
- ✅ **Team Efficiency**: Single codebase, single team
- ✅ **TypeScript Support**: Type safety across platforms
- ✅ **Large Ecosystem**: Extensive library support
- ❌ **Performance**: Slightly slower than native (acceptable for dashboard app)

**Expo vs. React Native CLI**:
- ✅ **Simplified Setup**: No Xcode/Android Studio configuration
- ✅ **OTA Updates**: Push updates without app store review
- ✅ **Build Service**: Cloud builds (EAS Build)
- ✅ **Developer Experience**: Faster iteration cycles
- ❌ **Bundle Size**: Slightly larger than bare React Native

**Redux Toolkit vs. Context API**:
- ✅ **Scalability**: Better for complex state management
- ✅ **DevTools**: Excellent debugging experience
- ✅ **RTK Query**: Built-in API caching and data fetching
- ✅ **Performance**: Optimized re-renders
- ❌ **Learning Curve**: More complex than Context API

### **B. Risk Mitigation**

| Risk | Impact | Probability | Mitigation Strategy |
|------|--------|-------------|---------------------|
| **Backend API Changes** | High | Medium | Version API endpoints, maintain backward compatibility |
| **Platform Updates** | Medium | High | Regular dependency updates, test on beta OS versions |
| **Performance Issues** | High | Low | Performance monitoring, profiling, optimization |
| **App Store Rejection** | High | Low | Follow guidelines strictly, pre-submission review |
| **Security Vulnerabilities** | High | Low | Regular security audits, dependency scanning |
| **Network Connectivity** | Medium | High | Offline mode, retry logic, error handling |
| **Device Fragmentation** | Medium | Medium | Test on wide range of devices, responsive design |
| **User Adoption** | Medium | Medium | User onboarding, documentation, support |

### **C. Future Enhancements**

**Post-Launch Features** (Future milestones):
- M0.1 enhanced features integration
- M0.2 database optimization features
- Biometric authentication
- Widget support (iOS 14+, Android 12+)
- Apple Watch companion app
- Wear OS companion app
- Tablet-optimized layouts
- Landscape mode optimization
- Multi-language support
- Custom dashboard layouts
- Advanced filtering and search
- Export data functionality
- Offline-first architecture
- GraphQL API integration

### **D. References**

**Documentation**:
- React Native: https://reactnative.dev/
- Expo: https://docs.expo.dev/
- React Navigation: https://reactnavigation.org/
- Redux Toolkit: https://redux-toolkit.js.org/
- React Native Paper: https://callstack.github.io/react-native-paper/
- Victory Native: https://formidable.com/open-source/victory/

**OA Framework Documentation**:
- M0 Real Dashboard: `demos/m0-real-dashboard/docs/`
- M0 Component Analysis: `demos/m0-real-dashboard/plan/m0-component-analysis.md`
- API Endpoints: `demos/m0-real-dashboard/plan/api-endpoints-spec.md`
- Dashboard Architecture: `demos/m0-real-dashboard/plan/dashboard-architecture.md`

**Design Guidelines**:
- Material Design: https://m3.material.io/
- Human Interface Guidelines: https://developer.apple.com/design/human-interface-guidelines/
- WCAG 2.1: https://www.w3.org/WAI/WCAG21/quickref/

---

## ✅ **APPROVAL & SIGN-OFF**

**Document Status**: ✅ COMPLETE - Ready for Review
**Created By**: AI Assistant
**Created Date**: 2026-01-12
**Version**: 1.0

**Approval Required From**:
- [ ] President & CEO, E.Z. Consultancy
- [ ] Lead Software Engineer
- [ ] Mobile Development Team Lead
- [ ] QA Team Lead
- [ ] DevOps Team Lead

**Next Steps**:
1. Review and approve implementation plan
2. Allocate development resources
3. Set up development environment
4. Begin Phase 1: Project Setup & Foundation
5. Schedule weekly progress reviews

---

**END OF DOCUMENT**

