# APK/IPA Generation Guide - M0 Mobile App

**Document**: APK/IPA Generation Guide  
**Authority**: President & CEO, <PERSON><PERSON><PERSON><PERSON> Consultancy  
**Created**: 2026-01-12  
**Milestone**: M0 (Milestone 0)  
**Status**: PLANNING PHASE

---

## 📱 **OVERVIEW**

This guide provides step-by-step instructions for generating production-ready APK (Android) and IPA (iOS) files for the M0 Mobile Application.

---

## 🤖 **ANDROID APK GENERATION**

### **Method 1: EAS Build (Recommended)**

**Prerequisites**:
- Expo account
- EAS CLI installed: `npm install -g eas-cli`
- Configured `eas.json`

**Steps**:

```bash
# 1. <PERSON>gin to EAS
eas login

# 2. Configure project
eas build:configure

# 3. Build production APK
eas build --platform android --profile production

# 4. Download APK from EAS dashboard
# Visit: https://expo.dev/accounts/[your-account]/projects/oa-framework-mobile/builds

# APK will be available for download after build completes (~10-15 minutes)
```

**Build Output**:
- File: `oa-framework-mobile-[version].apk`
- Size: ~30MB
- Location: EAS Build dashboard download link

---

### **Method 2: Local Gradle Build**

**Prerequisites**:
- Android Studio installed
- Android SDK configured
- Java JDK 11+

**Step 1: Generate Keystore**

```bash
# Navigate to android/app directory
cd android/app

# Generate release keystore
keytool -genkeypair -v -storetype PKCS12 \
  -keystore oa-framework-release.keystore \
  -alias oa-framework-key \
  -keyalg RSA \
  -keysize 2048 \
  -validity 10000

# Enter keystore password when prompted
# Enter key password when prompted
# Fill in certificate information
```

**Step 2: Configure Signing**

Create/edit `android/gradle.properties`:

```properties
MYAPP_RELEASE_STORE_FILE=oa-framework-release.keystore
MYAPP_RELEASE_KEY_ALIAS=oa-framework-key
MYAPP_RELEASE_STORE_PASSWORD=your_keystore_password
MYAPP_RELEASE_KEY_PASSWORD=your_key_password
```

**Step 3: Build APK**

```bash
# Navigate to android directory
cd android

# Clean previous builds
./gradlew clean

# Build release APK
./gradlew assembleRelease

# APK location:
# android/app/build/outputs/apk/release/app-release.apk
```

**Step 4: Verify APK**

```bash
# Check APK info
aapt dump badging android/app/build/outputs/apk/release/app-release.apk

# Install on connected device
adb install android/app/build/outputs/apk/release/app-release.apk

# Or install via file manager on device
```

---

### **Method 3: Android App Bundle (AAB) for Play Store**

```bash
# Build AAB instead of APK
cd android
./gradlew bundleRelease

# AAB location:
# android/app/build/outputs/bundle/release/app-release.aab

# Size: ~25MB (smaller than APK)
# Use this for Google Play Store submission
```

---

## 🍎 **iOS IPA GENERATION**

### **Method 1: EAS Build (Recommended)**

**Prerequisites**:
- Expo account
- Apple Developer Account ($99/year)
- EAS CLI installed

**Steps**:

```bash
# 1. Login to EAS
eas login

# 2. Configure iOS credentials
eas credentials

# 3. Build production IPA
eas build --platform ios --profile production

# 4. Download IPA from EAS dashboard
# Visit: https://expo.dev/accounts/[your-account]/projects/oa-framework-mobile/builds

# IPA will be available for download after build completes (~15-20 minutes)
```

**Build Output**:
- File: `oa-framework-mobile-[version].ipa`
- Size: ~50MB
- Location: EAS Build dashboard download link

---

### **Method 2: Xcode Build (macOS only)**

**Prerequisites**:
- macOS with Xcode 14+
- Apple Developer Account
- Valid provisioning profile

**Step 1: Open Project in Xcode**

```bash
# Navigate to iOS directory
cd ios

# Install pods
pod install

# Open workspace
open OAFrameworkMobile.xcworkspace
```

**Step 2: Configure Signing**

1. Select project in Xcode navigator
2. Select target "OAFrameworkMobile"
3. Go to "Signing & Capabilities" tab
4. Select your Team
5. Choose provisioning profile (or enable "Automatically manage signing")

**Step 3: Create Archive**

1. In Xcode menu: Product → Scheme → Edit Scheme
2. Set Build Configuration to "Release"
3. Product → Archive
4. Wait for archive to complete (~5-10 minutes)

**Step 4: Export IPA**

1. Window → Organizer
2. Select your archive
3. Click "Distribute App"
4. Choose distribution method:
   - **App Store Connect**: For App Store submission
   - **Ad Hoc**: For internal testing (up to 100 devices)
   - **Enterprise**: For enterprise distribution
   - **Development**: For development testing
5. Follow wizard to export IPA
6. IPA will be saved to chosen location

---

### **Method 3: Xcode Command Line**

```bash
# Create archive
xcodebuild -workspace ios/OAFrameworkMobile.xcworkspace \
  -scheme OAFrameworkMobile \
  -configuration Release \
  -archivePath build/OAFrameworkMobile.xcarchive \
  archive

# Export IPA
xcodebuild -exportArchive \
  -archivePath build/OAFrameworkMobile.xcarchive \
  -exportPath build \
  -exportOptionsPlist ExportOptions.plist

# IPA location: build/OAFrameworkMobile.ipa
```

**ExportOptions.plist**:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>method</key>
    <string>app-store</string>
    <key>teamID</key>
    <string>YOUR_TEAM_ID</string>
</dict>
</plist>
```

---

## 📦 **BUILD ARTIFACTS SUMMARY**

| Platform | File Type | Method | Size | Purpose |
|----------|-----------|--------|------|---------|
| Android | APK | EAS Build | ~30MB | Direct install, testing |
| Android | APK | Gradle | ~30MB | Direct install, testing |
| Android | AAB | Gradle | ~25MB | Play Store submission |
| iOS | IPA | EAS Build | ~50MB | App Store, TestFlight |
| iOS | IPA | Xcode | ~50MB | App Store, TestFlight |

---

## ✅ **VERIFICATION CHECKLIST**

### **Before Distribution**

- [ ] APK/IPA builds successfully without errors
- [ ] File size within expected range
- [ ] Version number correct in build
- [ ] App installs on test device
- [ ] App launches successfully
- [ ] All dashboards load
- [ ] API connections work
- [ ] No crashes during testing
- [ ] App icon displays correctly
- [ ] Splash screen works

### **Security Checks**

- [ ] APK/IPA signed with production certificate
- [ ] No debug symbols in production build
- [ ] ProGuard/R8 enabled (Android)
- [ ] Code obfuscation enabled
- [ ] API keys secured (not hardcoded)
- [ ] HTTPS enforced for all API calls

---

## 🚀 **DISTRIBUTION**

### **Internal Testing**

**Android APK**:
```bash
# Direct installation
adb install app-release.apk

# Or use Firebase App Distribution
firebase appdistribution:distribute app-release.apk \
  --app YOUR_ANDROID_APP_ID \
  --groups "internal-testers"
```

**iOS IPA**:
```bash
# Upload to TestFlight
eas submit --platform ios --profile preview

# Or use Xcode Organizer → Distribute App → TestFlight
```

### **Production Release**

**Google Play Store** (Android):
```bash
# Submit AAB
eas submit --platform android

# Or upload manually to Play Console
# https://play.google.com/console
```

**App Store** (iOS):
```bash
# Submit IPA
eas submit --platform ios

# Or use Xcode Organizer → Distribute App → App Store Connect
```

---

**END OF GUIDE**

