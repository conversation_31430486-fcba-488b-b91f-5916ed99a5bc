{"name": "oa-framework-mobile-demo", "version": "1.0.0", "description": "OA Framework Mobile Demo - Showcasing M0, M0.1, M0.2, M0.3 Milestones", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit", "build:android": "eas build --platform android --profile production", "build:ios": "eas build --platform ios --profile production", "build:all": "eas build --platform all --profile production"}, "dependencies": {"expo": "^51.0.0", "expo-router": "^3.4.0", "expo-splash-screen": "^0.27.0", "expo-status-bar": "^1.12.0", "react": "^18.2.0", "react-native": "^0.74.0", "react-native-paper": "^5.11.0", "react-native-safe-area-context": "^4.8.0", "react-native-screens": "^3.29.0", "@react-navigation/native": "^6.1.0", "@react-navigation/stack": "^6.3.0", "@react-navigation/bottom-tabs": "^6.5.0", "@reduxjs/toolkit": "^1.9.7", "react-redux": "^8.1.3", "axios": "^1.6.2", "victory-native": "^36.9.0", "react-native-svg": "^14.1.0", "react-native-gesture-handler": "^2.14.0", "react-native-reanimated": "^3.6.0", "@react-native-async-storage/async-storage": "^1.21.0", "react-native-mmkv": "^2.11.0", "date-fns": "^2.30.0", "lodash": "^4.17.21"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-native": "^0.73.0", "@types/jest": "^29.5.0", "@types/lodash": "^4.14.200", "typescript": "^5.3.0", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "@testing-library/react-native": "^12.4.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-native": "^4.1.0", "prettier": "^3.1.0", "expo-cli": "^6.3.0"}, "private": true, "author": "E.Z. Consultancy", "license": "PROPRIETARY"}