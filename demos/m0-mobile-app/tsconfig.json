{"compilerOptions": {"strict": true, "target": "ES2020", "lib": ["ES2020"], "jsx": "react-native", "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@screens/*": ["src/screens/*"], "@components/*": ["src/components/*"], "@services/*": ["src/services/*"], "@store/*": ["src/store/*"], "@types/*": ["src/types/*"], "@theme/*": ["src/theme/*"], "@utils/*": ["src/utils/*"], "@hooks/*": ["src/hooks/*"]}, "types": ["jest", "@testing-library/react-native"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx"]}