/**
 * ============================================================================
 * OA Framework Mobile Demo - Theme Configuration
 * Purpose: Centralized theme export for entire application
 * ============================================================================
 */

import { colors } from './colors';
import { typography } from './typography';
import { spacing } from './spacing';

export const theme = {
  colors,
  typography,
  spacing,
} as const;

export type Theme = typeof theme;

// Re-export individual theme modules
export { colors } from './colors';
export { typography } from './typography';
export { spacing } from './spacing';

// Export types
export type { ColorKey } from './colors';
export type { TypographyKey } from './typography';
export type { SpacingKey } from './spacing';
