/**
 * ============================================================================
 * OA Framework Mobile Demo - Color Theme
 * Purpose: Centralized color palette for OA Framework branding
 * ============================================================================
 */

export const colors = {
  // Primary Colors
  primary: '#0075FF',        // Electric Blue
  primaryLight: '#4DA3FF',
  primaryDark: '#0052B3',

  // Secondary Colors
  secondary: '#7928CA',      // Purple
  secondaryLight: '#A855F7',
  secondaryDark: '#5B1FA3',

  // Status Colors
  success: '#01B574',        // Cyan
  successLight: '#4ECDC4',
  successDark: '#008B5C',

  warning: '#F49342',        // Orange
  warningLight: '#FFB84D',
  warningDark: '#D97706',

  error: '#E31A89',          // Pink
  errorLight: '#F472B6',
  errorDark: '#BE185D',

  // Neutral Colors
  background: '#0B1437',     // Dark Navy
  card: '#060B28',           // Card Background
  surface: '#1A2847',        // Surface
  border: '#2D3E5F',         // Border

  // Text Colors
  text: '#FFFFFF',           // White
  textSecondary: 'rgba(255,255,255,0.7)',
  textTertiary: 'rgba(255,255,255,0.5)',
  textDisabled: 'rgba(255,255,255,0.3)',

  // Semantic Colors
  info: '#0075FF',
  success_light: '#E8F5E9',
  warning_light: '#FFF3E0',
  error_light: '#FFEBEE',

  // Overlay
  overlay: 'rgba(0,0,0,0.5)',
  overlayLight: 'rgba(0,0,0,0.3)',
  overlayDark: 'rgba(0,0,0,0.7)',
} as const;

export type ColorKey = keyof typeof colors;
