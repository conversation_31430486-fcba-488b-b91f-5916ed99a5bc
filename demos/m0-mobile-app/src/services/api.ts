/**
 * ============================================================================
 * OA Framework Mobile Demo - API Client Service
 * Purpose: Centralized HTTP client for API communication
 * ============================================================================
 */

import axios, { AxiosInstance, AxiosError } from 'axios';

/**
 * API Configuration
 */
const API_BASE_URL = 'http://localhost:3000/api';
const API_TIMEOUT = 10000;

/**
 * Create Axios instance with default configuration
 */
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * Request interceptor for adding auth tokens or other headers
 */
apiClient.interceptors.request.use(
  (config) => {
    // Add any request interceptor logic here
    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);

/**
 * Response interceptor for handling errors
 */
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error: AxiosError) => {
    // Handle common error scenarios
    if (error.response?.status === 401) {
      // Handle unauthorized
      console.error('Unauthorized access');
    } else if (error.response?.status === 404) {
      // Handle not found
      console.error('Resource not found');
    } else if (error.response?.status === 500) {
      // Handle server error
      console.error('Server error');
    }
    return Promise.reject(error);
  }
);

export default apiClient;
