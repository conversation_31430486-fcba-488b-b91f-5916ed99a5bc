/**
 * ============================================================================
 * OA Framework Mobile Demo - API Endpoints Configuration
 * Purpose: Centralized endpoint definitions for all API calls
 * ============================================================================
 */

/**
 * API Endpoints organized by milestone
 */
export const endpoints = {
  // M0 Foundation endpoints
  m0: {
    components: '/m0-components',
    security: '/m0-security',
    governance: '/m0-governance',
    tracking: '/m0-tracking',
  },

  // M0.1 Enterprise endpoints
  m01: {
    analytics: '/m0.1/analytics',
    mlPredictions: '/m0.1/ml-predictions',
    sessionTracking: '/m0.1/session-tracking',
  },

  // M0.2 Performance endpoints
  m02: {
    performance: '/m0.2/performance',
    caching: '/m0.2/caching',
    notifications: '/m0.2/notifications',
  },

  // M0.3 Logging endpoints
  m03: {
    auditLogs: '/m0.3/audit-logs',
    compliance: '/m0.3/compliance',
    configuration: '/m0.3/configuration',
  },
} as const;

/**
 * Type for endpoint keys
 */
export type EndpointKey = keyof typeof endpoints;
export type M0EndpointKey = keyof typeof endpoints.m0;
export type M01EndpointKey = keyof typeof endpoints.m01;
export type M02EndpointKey = keyof typeof endpoints.m02;
export type M03EndpointKey = keyof typeof endpoints.m03;
