/**
 * ============================================================================
 * OA Framework Mobile Demo - Redux Store Configuration
 * Purpose: Centralized Redux store setup with RTK Query
 * ============================================================================
 */

import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { apiSlice } from './api/apiSlice';
import { uiReducer } from './slices/uiSlice';
import { appReducer } from './slices/appSlice';

/**
 * Configure Redux store with RTK Query and custom slices
 */
export const store = configureStore({
  reducer: {
    // RTK Query API slice
    [apiSlice.reducerPath]: apiSlice.reducer,

    // Custom slices
    ui: uiReducer,
    app: appReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['api/executeQuery/fulfilled', 'api/executeQuery/rejected'],
      },
    }).concat(apiSlice.middleware),
});

// Setup listeners for RTK Query
setupListeners(store.dispatch);

// Export types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Export store
export default store;
