/**
 * ============================================================================
 * OA Framework Mobile Demo - RTK Query API Slice
 * Purpose: Centralized API configuration with RTK Query
 * ============================================================================
 */

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

const API_BASE_URL = 'http://localhost:3000/api';

/**
 * Create RTK Query API slice for data fetching
 */
export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl: API_BASE_URL,
    prepareHeaders: (headers) => {
      headers.set('Content-Type', 'application/json');
      return headers;
    },
  }),
  endpoints: (builder) => ({
    // M0 Foundation endpoints
    getM0Components: builder.query({
      query: () => '/m0-components',
    }),
    getM0Security: builder.query({
      query: () => '/m0-security',
    }),
    getM0Governance: builder.query({
      query: () => '/m0-governance',
    }),
    getM0Tracking: builder.query({
      query: () => '/m0-tracking',
    }),

    // M0.1 Enterprise endpoints
    getM01Analytics: builder.query({
      query: () => '/m0.1/analytics',
    }),
    getM01MLPredictions: builder.query({
      query: () => '/m0.1/ml-predictions',
    }),
    getM01SessionTracking: builder.query({
      query: () => '/m0.1/session-tracking',
    }),

    // M0.2 Performance endpoints
    getM02Performance: builder.query({
      query: () => '/m0.2/performance',
    }),
    getM02Caching: builder.query({
      query: () => '/m0.2/caching',
    }),
    getM02Notifications: builder.query({
      query: () => '/m0.2/notifications',
    }),

    // M0.3 Logging endpoints
    getM03AuditLogs: builder.query({
      query: () => '/m0.3/audit-logs',
    }),
    getM03Compliance: builder.query({
      query: () => '/m0.3/compliance',
    }),
    getM03Configuration: builder.query({
      query: () => '/m0.3/configuration',
    }),
  }),
});

// Export hooks for each endpoint
export const {
  useGetM0ComponentsQuery,
  useGetM0SecurityQuery,
  useGetM0GovernanceQuery,
  useGetM0TrackingQuery,
  useGetM01AnalyticsQuery,
  useGetM01MLPredictionsQuery,
  useGetM01SessionTrackingQuery,
  useGetM02PerformanceQuery,
  useGetM02CachingQuery,
  useGetM02NotificationsQuery,
  useGetM03AuditLogsQuery,
  useGetM03ComplianceQuery,
  useGetM03ConfigurationQuery,
} = apiSlice;
