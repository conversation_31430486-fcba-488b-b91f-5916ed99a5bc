/**
 * ============================================================================
 * OA Framework Mobile Demo - App State Slice
 * Purpose: Manage application-wide state
 * ============================================================================
 */

import { createSlice, PayloadAction, SliceCaseReducers } from '@reduxjs/toolkit';

export interface AppState {
  apiBaseUrl: string;
  isConnected: boolean;
  lastSyncTime: number | null;
}

const initialState: AppState = {
  apiBaseUrl: 'http://localhost:3000/api',
  isConnected: false,
  lastSyncTime: null,
};

export const appSlice = createSlice<AppState, SliceCaseReducers<AppState>, 'app'>({
  name: 'app',
  initialState,
  reducers: {
    setApiBaseUrl: (state, action: PayloadAction<string>) => {
      state.apiBaseUrl = action.payload;
    },
    setConnected: (state, action: PayloadAction<boolean>) => {
      state.isConnected = action.payload;
    },
    setLastSyncTime: (state, action: PayloadAction<number>) => {
      state.lastSyncTime = action.payload;
    },
  },
});

export const { setApiBaseUrl, setConnected, setLastSyncTime } = appSlice.actions;
export const appReducer = appSlice.reducer;
