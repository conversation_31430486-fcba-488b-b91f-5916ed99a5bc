/**
 * ============================================================================
 * OA Framework Mobile Demo - UI State Slice
 * Purpose: Manage UI state (navigation, modals, loading states)
 * ============================================================================
 */

import { createSlice, PayloadAction, SliceCaseReducers } from '@reduxjs/toolkit';

export interface UIState {
  isLoading: boolean;
  activeTab: 'home' | 'm0' | 'm01' | 'm02' | 'm03' | 'interactions';
  isRefreshing: boolean;
  error: string | null;
}

const initialState: UIState = {
  isLoading: false,
  activeTab: 'home',
  isRefreshing: false,
  error: null,
};

export const uiSlice = createSlice<UIState, SliceCaseReducers<UIState>, 'ui'>({
  name: 'ui',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setActiveTab: (state, action: PayloadAction<UIState['activeTab']>) => {
      state.activeTab = action.payload;
    },
    setRefreshing: (state, action: PayloadAction<boolean>) => {
      state.isRefreshing = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const { setLoading, setActiveTab, setRefreshing, setError, clearError } = uiSlice.actions;
export const uiReducer = uiSlice.reducer;
