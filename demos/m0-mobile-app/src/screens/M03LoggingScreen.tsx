/**
 * ============================================================================
 * OA Framework Mobile Demo - M0.3 Logging Screen
 * Purpose: Display M0.3 Logging infrastructure data
 * ============================================================================
 */

import React from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Text } from 'react-native-paper';
import { colors, spacing } from '../theme';

export default function M03LoggingScreen(): React.ReactElement {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.title}>M0.3 Logging</Text>
            <Text style={styles.metric}>18 Tasks Complete</Text>
            <Text style={styles.description}>
              Configurable audit logging infrastructure
            </Text>
          </Card.Content>
        </Card>

        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.subtitle}>Audit Logs</Text>
            <Text style={styles.placeholder}>
              Real-time audit logs will display here
            </Text>
          </Card.Content>
        </Card>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    padding: spacing.lg,
  },
  card: {
    marginBottom: spacing.lg,
    backgroundColor: colors.card,
    borderColor: colors.border,
    borderWidth: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.text,
    marginBottom: spacing.md,
  },
  subtitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.md,
  },
  metric: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.warning,
    marginBottom: spacing.sm,
  },
  description: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  placeholder: {
    fontSize: 14,
    color: colors.textTertiary,
    fontStyle: 'italic',
  },
});
