/**
 * ============================================================================
 * OA Framework Mobile Demo - M0 Dashboard Screen
 * Purpose: Display M0 Foundation milestone data
 * ============================================================================
 */

import React from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Text, ActivityIndicator } from 'react-native-paper';
import { colors, spacing } from '../theme';
import { useGetM0ComponentsQuery } from '../store/api/apiSlice';

/**
 * M0 Dashboard Screen Component
 * Displays M0 foundation metrics and component status
 */
export default function M0DashboardScreen(): React.ReactElement {
  const { data, isLoading, error } = useGetM0ComponentsQuery({});

  if (isLoading) {
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.title}>M0 Foundation</Text>
            <Text style={styles.metric}>184 Components</Text>
            <Text style={styles.description}>
              Robust governance and tracking infrastructure
            </Text>
          </Card.Content>
        </Card>

        {error && (
          <Card style={[styles.card, styles.errorCard]}>
            <Card.Content>
              <Text style={styles.errorText}>Error loading data</Text>
            </Card.Content>
          </Card>
        )}

        {!error && (
          <Card style={styles.card}>
            <Card.Content>
              <Text style={styles.subtitle}>Component Status</Text>
              <Text style={styles.placeholder}>
                Real-time component metrics will display here
              </Text>
            </Card.Content>
          </Card>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  content: {
    padding: spacing.lg,
  },
  card: {
    marginBottom: spacing.lg,
    backgroundColor: colors.card,
    borderColor: colors.border,
    borderWidth: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.text,
    marginBottom: spacing.md,
  },
  subtitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.md,
  },
  metric: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.primary,
    marginBottom: spacing.sm,
  },
  description: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  placeholder: {
    fontSize: 14,
    color: colors.textTertiary,
    fontStyle: 'italic',
  },
  errorCard: {
    borderColor: colors.error,
  },
  errorText: {
    color: colors.error,
    fontSize: 14,
  },
});
