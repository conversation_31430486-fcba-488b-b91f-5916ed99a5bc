/**
 * ============================================================================
 * OA Framework Mobile Demo - Home Screen
 * Purpose: Landing screen with milestone overview and navigation
 * ============================================================================
 */

import React from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Card, Button, Text } from 'react-native-paper';
import { colors, spacing } from '../theme';

interface HomeScreenProps {
  navigation: any;
}

/**
 * Home Screen Component
 * Displays milestone overview cards and navigation options
 */
export default function HomeScreen({ navigation }: HomeScreenProps): React.ReactElement {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>OA Framework</Text>
        <Text style={styles.subtitle}>Mobile Demo</Text>
      </View>

      <View style={styles.content}>
        {/* M0 Foundation Card */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.cardTitle}>M0 Foundation</Text>
            <Text style={styles.cardSubtitle}>184 Components</Text>
            <Text style={styles.cardDescription}>
              Robust governance & tracking infrastructure
            </Text>
          </Card.Content>
          <Card.Actions>
            <Button
              mode="contained"
              onPress={() => navigation.navigate('M0Dashboard')}
              style={styles.button}
            >
              View Details
            </Button>
          </Card.Actions>
        </Card>

        {/* M0.1 Enterprise Card */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.cardTitle}>M0.1 Enterprise</Text>
            <Text style={styles.cardSubtitle}>45 Tasks Complete</Text>
            <Text style={styles.cardDescription}>
              Advanced analytics & ML capabilities
            </Text>
          </Card.Content>
          <Card.Actions>
            <Button
              mode="contained"
              onPress={() => navigation.navigate('M01Analytics')}
              style={styles.button}
            >
              View Analytics
            </Button>
          </Card.Actions>
        </Card>

        {/* M0.2 Performance Card */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.cardTitle}>M0.2 Performance</Text>
            <Text style={styles.cardSubtitle}>6 Tasks Complete</Text>
            <Text style={styles.cardDescription}>
              Optimization & notification services
            </Text>
          </Card.Content>
          <Card.Actions>
            <Button
              mode="contained"
              onPress={() => navigation.navigate('M02Performance')}
              style={styles.button}
            >
              View Metrics
            </Button>
          </Card.Actions>
        </Card>

        {/* M0.3 Logging Card */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.cardTitle}>M0.3 Logging</Text>
            <Text style={styles.cardSubtitle}>18 Tasks Complete</Text>
            <Text style={styles.cardDescription}>
              Configurable audit logging infrastructure
            </Text>
          </Card.Content>
          <Card.Actions>
            <Button
              mode="contained"
              onPress={() => navigation.navigate('M03Logging')}
              style={styles.button}
            >
              View Logs
            </Button>
          </Card.Actions>
        </Card>

        {/* Interactions Demo Card */}
        <Card style={[styles.card, styles.interactionsCard]}>
          <Card.Content>
            <Text style={styles.cardTitle}>Milestone Interactions</Text>
            <Text style={styles.cardDescription}>
              Visualize how milestones work together
            </Text>
          </Card.Content>
          <Card.Actions>
            <Button
              mode="contained"
              onPress={() => navigation.navigate('InteractionsDemo')}
              style={styles.button}
            >
              View Interactions
            </Button>
          </Card.Actions>
        </Card>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.xl,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: colors.primary,
    marginBottom: spacing.sm,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
  },
  content: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.xl,
  },
  card: {
    marginBottom: spacing.lg,
    backgroundColor: colors.card,
    borderColor: colors.border,
    borderWidth: 1,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.sm,
  },
  cardSubtitle: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  cardDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  button: {
    marginTop: spacing.md,
  },
  interactionsCard: {
    borderColor: colors.secondary,
    borderWidth: 2,
  },
});
