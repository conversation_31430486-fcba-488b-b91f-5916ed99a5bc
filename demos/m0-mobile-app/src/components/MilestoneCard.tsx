/**
 * ============================================================================
 * OA Framework Mobile Demo - Milestone Card Component
 * Purpose: Reusable card component for displaying milestone information
 * ============================================================================
 */

import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Card, Text } from 'react-native-paper';
import { colors, spacing } from '../theme';

interface MilestoneCardProps {
  title: string;
  subtitle: string;
  description: string;
  icon?: string;
  status?: 'active' | 'completed' | 'pending';
}

export default function MilestoneCard({
  title,
  subtitle,
  description,
  status = 'completed',
}: MilestoneCardProps): React.ReactElement {
  const statusColor = status === 'completed' ? colors.success : colors.warning;

  return (
    <Card style={styles.card}>
      <Card.Content>
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <Text style={styles.title}>{title}</Text>
            <View style={[styles.statusBadge, { backgroundColor: statusColor }]}>
              <Text style={styles.statusText}>
                {status === 'completed' ? '✓' : '○'}
              </Text>
            </View>
          </View>
        </View>
        <Text style={styles.subtitle}>{subtitle}</Text>
        <Text style={styles.description}>{description}</Text>
      </Card.Content>
    </Card>
  );
}

const styles = StyleSheet.create({
  card: {
    marginBottom: spacing.lg,
    backgroundColor: colors.card,
    borderColor: colors.border,
    borderWidth: 1,
  },
  header: {
    marginBottom: spacing.md,
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    flex: 1,
  },
  statusBadge: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusText: {
    color: colors.text,
    fontWeight: '700',
    fontSize: 14,
  },
  subtitle: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  description: {
    fontSize: 13,
    color: colors.textSecondary,
    lineHeight: 20,
  },
});
