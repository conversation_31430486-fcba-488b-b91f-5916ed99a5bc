/**
 * ============================================================================
 * OA Framework Mobile Demo - Status Indicator Component
 * Purpose: Visual status indicator for system health and states
 * ============================================================================
 */

import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';
import { colors, spacing } from '../theme';

interface StatusIndicatorProps {
  status: 'healthy' | 'warning' | 'error' | 'loading';
  label: string;
  size?: 'small' | 'medium' | 'large';
}

export default function StatusIndicator({
  status,
  label,
  size = 'medium',
}: StatusIndicatorProps): React.ReactElement {
  const getStatusColor = () => {
    switch (status) {
      case 'healthy':
        return colors.success;
      case 'warning':
        return colors.warning;
      case 'error':
        return colors.error;
      case 'loading':
        return colors.primary;
      default:
        return colors.textSecondary;
    }
  };

  const getSize = () => {
    switch (size) {
      case 'small':
        return 12;
      case 'large':
        return 20;
      default:
        return 16;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'healthy':
        return '●';
      case 'warning':
        return '◐';
      case 'error':
        return '○';
      case 'loading':
        return '◌';
      default:
        return '●';
    }
  };

  const statusSize = getSize();

  return (
    <View style={styles.container}>
      <Text
        style={[
          styles.indicator,
          {
            color: getStatusColor(),
            fontSize: statusSize,
          },
        ]}
      >
        {getStatusText()}
      </Text>
      <Text style={styles.label}>{label}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  indicator: {
    marginRight: spacing.md,
    fontWeight: '700',
  },
  label: {
    fontSize: 14,
    color: colors.text,
    flex: 1,
  },
});
