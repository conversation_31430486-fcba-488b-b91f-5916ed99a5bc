/**
 * ============================================================================
 * OA Framework Mobile Demo - Metric Display Component
 * Purpose: Display key metrics with visual indicators
 * ============================================================================
 */

import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Card, Text } from 'react-native-paper';
import { colors, spacing } from '../theme';

interface MetricDisplayProps {
  label: string;
  value: string | number;
  unit?: string;
  trend?: 'up' | 'down' | 'stable';
  color?: string;
}

export default function MetricDisplay({
  label,
  value,
  unit,
  trend = 'stable',
  color = colors.primary,
}: MetricDisplayProps): React.ReactElement {
  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return '↑';
      case 'down':
        return '↓';
      default:
        return '→';
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'up':
        return colors.success;
      case 'down':
        return colors.error;
      default:
        return colors.textSecondary;
    }
  };

  return (
    <Card style={styles.card}>
      <Card.Content>
        <Text style={styles.label}>{label}</Text>
        <View style={styles.valueContainer}>
          <Text style={[styles.value, { color }]}>
            {value}
            {unit && <Text style={styles.unit}>{unit}</Text>}
          </Text>
          <Text style={[styles.trend, { color: getTrendColor() }]}>
            {getTrendIcon()}
          </Text>
        </View>
      </Card.Content>
    </Card>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: colors.card,
    borderColor: colors.border,
    borderWidth: 1,
    marginBottom: spacing.md,
  },
  label: {
    fontSize: 12,
    color: colors.textSecondary,
    marginBottom: spacing.sm,
    textTransform: 'uppercase',
    fontWeight: '600',
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    justifyContent: 'space-between',
  },
  value: {
    fontSize: 24,
    fontWeight: '700',
  },
  unit: {
    fontSize: 14,
    fontWeight: '400',
    marginLeft: spacing.sm,
  },
  trend: {
    fontSize: 18,
    fontWeight: '700',
  },
});
