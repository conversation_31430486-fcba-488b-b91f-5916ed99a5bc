/**
 * ============================================================================
 * OA Framework Mobile Demo - Root Application Component
 * Purpose: Main app entry point with Redux provider and navigation
 * ============================================================================
 */

import React from 'react';
import { Provider } from 'react-redux';
import { PaperProvider } from 'react-native-paper';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';

import store from './store';
import { theme, colors } from './theme';
import HomeScreen from './screens/HomeScreen';
import M0DashboardScreen from './screens/M0DashboardScreen';
import M01AnalyticsScreen from './screens/M01AnalyticsScreen';
import M02PerformanceScreen from './screens/M02PerformanceScreen';
import M03LoggingScreen from './screens/M03LoggingScreen';
import InteractionsDemoScreen from './screens/InteractionsDemoScreen';

const Stack = createNativeStackNavigator();

/**
 * Root App Component
 * Provides Redux store, theme, and navigation
 */
export default function App(): React.ReactElement {
  return (
    <Provider store={store}>
      <SafeAreaProvider>
        <PaperProvider
          theme={{
            colors: {
              primary: colors.primary,
              secondary: colors.secondary,
              background: colors.background,
              surface: colors.card,
              error: colors.error,
              onBackground: colors.text,
              onSurface: colors.text,
            },
          }}
        >
          <NavigationContainer>
            <Stack.Navigator
              screenOptions={{
                headerStyle: {
                  backgroundColor: colors.card,
                },
                headerTintColor: colors.text,
                headerTitleStyle: {
                  fontWeight: '700',
                  fontSize: 18,
                },
              }}
            >
              <Stack.Screen
                name="Home"
                component={HomeScreen}
                options={{
                  title: 'OA Framework Mobile Demo',
                  headerShown: true,
                }}
              />
              <Stack.Screen
                name="M0Dashboard"
                component={M0DashboardScreen}
                options={{
                  title: 'M0 Foundation',
                }}
              />
              <Stack.Screen
                name="M01Analytics"
                component={M01AnalyticsScreen}
                options={{
                  title: 'M0.1 Enterprise',
                }}
              />
              <Stack.Screen
                name="M02Performance"
                component={M02PerformanceScreen}
                options={{
                  title: 'M0.2 Performance',
                }}
              />
              <Stack.Screen
                name="M03Logging"
                component={M03LoggingScreen}
                options={{
                  title: 'M0.3 Logging',
                }}
              />
              <Stack.Screen
                name="InteractionsDemo"
                component={InteractionsDemoScreen}
                options={{
                  title: 'Milestone Interactions',
                }}
              />
            </Stack.Navigator>
          </NavigationContainer>
          <StatusBar style="light" backgroundColor={colors.background} />
        </PaperProvider>
      </SafeAreaProvider>
    </Provider>
  );
}
