// ============================================================================
// DATABASE INITIALIZER TESTS
// M1-CORE-DB-04 | Test Suite for DatabaseInitializer
// ============================================================================

import { DatabaseInitializer } from '../database-initializer';
import type { IDatabaseService } from '../interfaces/IDatabaseService';
import type { IAuthorityEnforcementEngine } from '../../../governance/runtime/compliance/interfaces/IAuthorityEnforcementEngine';
import { AuditLoggingConfigurationService } from '../../../logging/configuration/AuditLoggingConfigurationService';
import type {
  TInitializerConfig,
  TDatabaseHealth,
  TPerformanceMetrics,
} from '../types/index';
import { exec } from 'child_process';

// Mock exec
jest.mock('child_process', () => ({
  exec: jest.fn(),
}));

// Mock AuditLoggingConfigurationService
jest.mock('../../../logging/configuration/AuditLoggingConfigurationService');

describe('DatabaseInitializer', () => {
  let initializer: DatabaseInitializer;
  let mockDatabaseService: jest.Mocked<IDatabaseService>;
  let mockAuthorityEngine: jest.Mocked<IAuthorityEnforcementEngine>;
  let mockAuditLogger: jest.Mocked<AuditLoggingConfigurationService>;
  let config: Partial<TInitializerConfig>;

  beforeEach(() => {
    // Create mocks
    mockDatabaseService = {
      getConnectionHealth: jest.fn(),
      getQueryPerformanceMetrics: jest.fn(),
      executeRawQuery: jest.fn(),
      executeQuery: jest.fn(),
      find: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      beginTransaction: jest.fn(),
      commit: jest.fn(),
      rollback: jest.fn(),
      initialize: jest.fn(),
      shutdown: jest.fn(),
      isReady: jest.fn(),
    } as any;

    mockAuthorityEngine = {
      validateAuthority: jest.fn(),
      enforceAuthorityCompliance: jest.fn(),
      initialize: jest.fn(),
      shutdown: jest.fn(),
    } as any;

    mockAuditLogger = {
      shouldLog: jest.fn().mockResolvedValue(true),
      logEvent: jest.fn(),
      initialize: jest.fn(),
      shutdown: jest.fn(),
      isInitialized: jest.fn().mockReturnValue(true),
    } as any;

    config = {
      autoInitialize: false,
      runMigrations: true,
      loadSeedData: false,
      validateSchema: true,
      migrationTimeout: 10000,
      seedDataTimeout: 5000,
      retryOnFailure: false,
      maxRetries: 3,
      retryDelay: 1000,
      forceReset: false,
    };

    initializer = new DatabaseInitializer(
      config,
      mockDatabaseService,
      mockAuthorityEngine,
      mockAuditLogger
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  // ============================================================================
  // INITIALIZATION TESTS
  // ============================================================================

  describe('Constructor', () => {
    it('should create instance with default configuration', () => {
      const defaultInitializer = new DatabaseInitializer(
        {},
        mockDatabaseService,
        mockAuthorityEngine,
        mockAuditLogger
      );
      expect(defaultInitializer).toBeInstanceOf(DatabaseInitializer);
    });

    it('should merge provided config with defaults', () => {
      const customConfig = { autoInitialize: true, runMigrations: false };
      const customInitializer = new DatabaseInitializer(
        customConfig,
        mockDatabaseService,
        mockAuthorityEngine,
        mockAuditLogger
      );
      expect(customInitializer).toBeInstanceOf(DatabaseInitializer);
    });

    it('should initialize state as NOT_STARTED', () => {
      const state = initializer.getInitializationState();
      expect(state.status).toBe('NOT_STARTED');
      expect(state.connectionVerified).toBe(false);
      expect(state.migrationsExecuted).toBe(false);
      expect(state.schemaValidated).toBe(false);
      expect(state.seedDataLoaded).toBe(false);
    });
  });

  describe('Service Lifecycle', () => {
    it('should implement getServiceName', () => {
      const serviceName = (initializer as any).getServiceName();
      expect(serviceName).toBe('DatabaseInitializer');
    });

    it('should implement getServiceVersion', () => {
      const serviceVersion = (initializer as any).getServiceVersion();
      expect(serviceVersion).toBe('1.0.0');
    });

    it('should initialize without errors', async () => {
      await expect(initializer.initialize()).resolves.not.toThrow();
    });

    it('should shutdown without errors', async () => {
      await initializer.initialize();
      await expect(initializer.shutdown()).resolves.not.toThrow();
    });
  });

  // ============================================================================
  // CONNECTION VERIFICATION TESTS
  // ============================================================================

  describe('verifyDatabaseConnection', () => {
    it('should return true when connection is healthy', async () => {
      mockDatabaseService.getConnectionHealth.mockResolvedValueOnce({
        isHealthy: true,
        connectionPoolSize: 10,
        activeConnections: 5,
        idleConnections: 5,
        lastCheckTime: new Date(),
        responseTimeMs: 10,
      });

      const result = await initializer.verifyDatabaseConnection();
      expect(result).toBe(true);
      expect(mockDatabaseService.getConnectionHealth).toHaveBeenCalledTimes(1);
    });

    it('should return false when connection is unhealthy', async () => {
      mockDatabaseService.getConnectionHealth.mockResolvedValueOnce({
        isHealthy: false,
        connectionPoolSize: 0,
        activeConnections: 0,
        idleConnections: 0,
        lastCheckTime: new Date(),
        responseTimeMs: 0,
        error: 'Connection failed',
      });

      const result = await initializer.verifyDatabaseConnection();
      expect(result).toBe(false);
    });

    it('should return false on connection error', async () => {
      mockDatabaseService.getConnectionHealth.mockRejectedValueOnce(
        new Error('Database unavailable')
      );

      const result = await initializer.verifyDatabaseConnection();
      expect(result).toBe(false);
    });
  });

  // ============================================================================
  // MIGRATION TESTS
  // ============================================================================

  describe('runMigrations', () => {
    it('should execute migrations successfully', async () => {
      const mockExec = exec as jest.MockedFunction<typeof exec>;
      (mockExec as any).mockImplementation(
        (_cmd: string, _opts: any, callback: Function) => {
          callback(null, { stdout: 'Migrations applied', stderr: '' });
        }
      );

      const result = await initializer.runMigrations();

      expect(result.status).toBe('SUCCESS');
      expect(result.appliedCount).toBe(1);
      expect(result.failedCount).toBe(0);
      expect(result.migrations).toHaveLength(1);
    });

    it('should handle migration failures', async () => {
      const mockExec = exec as jest.MockedFunction<typeof exec>;
      (mockExec as any).mockImplementation(
        (_cmd: string, _opts: any, callback: Function) => {
          callback(null, { stdout: '', stderr: 'Migration error' });
        }
      );

      const result = await initializer.runMigrations();

      expect(result.status).toBe('FAILED');
      expect(result.appliedCount).toBe(0);
      expect(result.failedCount).toBe(1);
      expect(result.errors).toContain('Migration error');
    });

    it('should handle exec errors', async () => {
      const mockExec = exec as jest.MockedFunction<typeof exec>;
      (mockExec as any).mockImplementation(
        (_cmd: string, _opts: any, callback: Function) => {
          callback(new Error('Command failed'), null);
        }
      );

      const result = await initializer.runMigrations();

      expect(result.status).toBe('FAILED');
      expect(result.failedCount).toBe(1);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  // ============================================================================
  // SCHEMA VALIDATION TESTS
  // ============================================================================

  describe('validateSchema', () => {
    it('should return true when schema is valid', async () => {
      mockDatabaseService.executeRawQuery.mockResolvedValueOnce([1]);

      const result = await initializer.validateSchema();
      expect(result).toBe(true);
      expect(mockDatabaseService.executeRawQuery).toHaveBeenCalledWith('SELECT 1');
    });

    it('should return false when schema validation fails', async () => {
      mockDatabaseService.executeRawQuery.mockRejectedValueOnce(
        new Error('Schema error')
      );

      const result = await initializer.validateSchema();
      expect(result).toBe(false);
    });
  });

  // ============================================================================
  // SEED DATA TESTS
  // ============================================================================

  describe('loadSeedData', () => {
    it('should load seed data successfully', async () => {
      await expect(initializer.loadSeedData()).resolves.not.toThrow();
    });

    it('should handle seed data loading errors', async () => {
      // Force error by mocking internal log operation
      const originalLog = (initializer as any)._logInternalOperation;
      (initializer as any)._logInternalOperation = jest
        .fn()
        .mockRejectedValueOnce(new Error('Log error'));

      await expect(initializer.loadSeedData()).rejects.toThrow();

      (initializer as any)._logInternalOperation = originalLog;
    });
  });

  describe('loadOAConfigurationData', () => {
    it('should load OA configuration successfully', async () => {
      await expect(initializer.loadOAConfigurationData()).resolves.not.toThrow();
    });

    it('should handle OA configuration loading errors', async () => {
      const originalLog = (initializer as any)._logInternalOperation;
      (initializer as any)._logInternalOperation = jest
        .fn()
        .mockRejectedValueOnce(new Error('Log error'));

      await expect(initializer.loadOAConfigurationData()).rejects.toThrow();

      (initializer as any)._logInternalOperation = originalLog;
    });
  });

  // ============================================================================
  // DATABASE STATE VALIDATION TESTS
  // ============================================================================

  describe('checkDatabaseState', () => {
    it('should return valid state when connection and schema are good', async () => {
      mockDatabaseService.getConnectionHealth.mockResolvedValueOnce({
        isHealthy: true,
        connectionPoolSize: 10,
        activeConnections: 5,
        idleConnections: 5,
        lastCheckTime: new Date(),
        responseTimeMs: 10,
      });

      mockDatabaseService.executeRawQuery.mockResolvedValueOnce([1]);

      const state = await initializer.checkDatabaseState();

      expect(state.status).toBe('VALID');
      expect(state.isReady).toBe(true);
      expect(state.checks).toHaveLength(2);
      expect(state.issues).toHaveLength(0);
    });

    it('should return invalid state when connection fails', async () => {
      mockDatabaseService.getConnectionHealth.mockResolvedValueOnce({
        isHealthy: false,
        connectionPoolSize: 0,
        activeConnections: 0,
        idleConnections: 0,
        lastCheckTime: new Date(),
        responseTimeMs: 0,
      });

      mockDatabaseService.executeRawQuery.mockResolvedValueOnce([1]);

      const state = await initializer.checkDatabaseState();

      expect(state.status).toBe('INVALID');
      expect(state.isReady).toBe(false);
      expect(state.issues).toContain('Database connection failed');
    });

    it('should return invalid state when schema validation fails', async () => {
      mockDatabaseService.getConnectionHealth.mockResolvedValueOnce({
        isHealthy: true,
        connectionPoolSize: 10,
        activeConnections: 5,
        idleConnections: 5,
        lastCheckTime: new Date(),
        responseTimeMs: 10,
      });

      mockDatabaseService.executeRawQuery.mockRejectedValueOnce(
        new Error('Schema error')
      );

      const state = await initializer.checkDatabaseState();

      expect(state.status).toBe('INVALID');
      expect(state.isReady).toBe(false);
      expect(state.issues).toContain('Schema validation failed');
    });

    it('should handle exceptions gracefully', async () => {
      mockDatabaseService.getConnectionHealth.mockRejectedValueOnce(
        new Error('Connection error')
      );

      const state = await initializer.checkDatabaseState();

      expect(state.status).toBe('INVALID');
      expect(state.isReady).toBe(false);
      expect(state.issues.length).toBeGreaterThan(0);
    });
  });

  // ============================================================================
  // INITIALIZATION TESTS
  // ============================================================================

  describe('initializeDatabase', () => {
    beforeEach(() => {
      mockDatabaseService.getConnectionHealth.mockResolvedValue({
        isHealthy: true,
        connectionPoolSize: 10,
        activeConnections: 5,
        idleConnections: 5,
        lastCheckTime: new Date(),
        responseTimeMs: 10,
      });

      mockDatabaseService.executeRawQuery.mockResolvedValue([1]);

      const mockExec = exec as jest.MockedFunction<typeof exec>;
      (mockExec as any).mockImplementation(
        (_cmd: string, _opts: any, callback: Function) => {
          callback(null, { stdout: 'Migrations applied', stderr: '' });
        }
      );
    });

    it('should initialize database successfully', async () => {
      await initializer.initializeDatabase();

      const state = initializer.getInitializationState();
      expect(state.status).toBe('COMPLETE');
      expect(state.connectionVerified).toBe(true);
      expect(state.migrationsExecuted).toBe(true);
      expect(state.schemaValidated).toBe(true);
      expect(state.oaConfigurationLoaded).toBe(true);
    });

    it('should skip migrations when disabled', async () => {
      const noMigrationConfig = { ...config, runMigrations: false };
      const noMigrationInitializer = new DatabaseInitializer(
        noMigrationConfig,
        mockDatabaseService,
        mockAuthorityEngine,
        mockAuditLogger
      );

      await noMigrationInitializer.initializeDatabase();

      const state = noMigrationInitializer.getInitializationState();
      expect(state.migrationsExecuted).toBe(false);
    });

    it('should skip schema validation when disabled', async () => {
      const noValidationConfig = { ...config, validateSchema: false };
      const noValidationInitializer = new DatabaseInitializer(
        noValidationConfig,
        mockDatabaseService,
        mockAuthorityEngine,
        mockAuditLogger
      );

      await noValidationInitializer.initializeDatabase();

      const state = noValidationInitializer.getInitializationState();
      expect(state.schemaValidated).toBe(false);
    });

    it('should load seed data when enabled', async () => {
      const seedDataConfig = { ...config, loadSeedData: true };
      const seedDataInitializer = new DatabaseInitializer(
        seedDataConfig,
        mockDatabaseService,
        mockAuthorityEngine,
        mockAuditLogger
      );

      await seedDataInitializer.initializeDatabase();

      const state = seedDataInitializer.getInitializationState();
      expect(state.seedDataLoaded).toBe(true);
    });

    it('should handle initialization failures', async () => {
      mockDatabaseService.getConnectionHealth.mockResolvedValueOnce({
        isHealthy: false,
        connectionPoolSize: 0,
        activeConnections: 0,
        idleConnections: 0,
        lastCheckTime: new Date(),
        responseTimeMs: 0,
      });

      await expect(initializer.initializeDatabase()).rejects.toThrow();

      const state = initializer.getInitializationState();
      expect(state.status).toBe('FAILED');
      expect(state.lastError).toBeTruthy();
    });

    it('should calculate duration correctly', async () => {
      await initializer.initializeDatabase();

      const state = initializer.getInitializationState();
      expect(state.durationMs).toBeGreaterThanOrEqual(0);
      expect(state.startTime).toBeTruthy();
      expect(state.completionTime).toBeTruthy();
    });
  });

  // ============================================================================
  // INITIALIZATION STATUS REPORTING TESTS
  // ============================================================================

  describe('reportInitializationStatus', () => {
    it('should report status without errors', async () => {
      await expect(
        initializer.reportInitializationStatus()
      ).resolves.not.toThrow();
    });

    it('should handle reporting errors gracefully', async () => {
      const originalLog = (initializer as any)._logInternalOperation;
      (initializer as any)._logInternalOperation = jest
        .fn()
        .mockRejectedValueOnce(new Error('Logging error'));

      await expect(
        initializer.reportInitializationStatus()
      ).resolves.not.toThrow();

      (initializer as any)._logInternalOperation = originalLog;
    });
  });

  // ============================================================================
  // ISETUPSERVICE INTERFACE TESTS
  // ============================================================================

  describe('ISetupService Interface', () => {
    it('should implement setup method', async () => {
      await expect(initializer.setup()).resolves.not.toThrow();
    });

    it('should implement teardown method', async () => {
      await initializer.setup();
      await expect(initializer.teardown()).resolves.not.toThrow();
    });

    it('should implement isServiceReady method', () => {
      expect(initializer.isServiceReady()).toBe(false);
    });

    it('should return true when service is ready', async () => {
      mockDatabaseService.getConnectionHealth.mockResolvedValue({
        isHealthy: true,
        connectionPoolSize: 10,
        activeConnections: 5,
        idleConnections: 5,
        lastCheckTime: new Date(),
        responseTimeMs: 10,
      });

      mockDatabaseService.executeRawQuery.mockResolvedValue([1]);

      const mockExec = exec as jest.MockedFunction<typeof exec>;
      (mockExec as any).mockImplementation(
        (_cmd: string, _opts: any, callback: Function) => {
          callback(null, { stdout: 'Migrations applied', stderr: '' });
        }
      );

      await initializer.initializeDatabase();
      expect(initializer.isServiceReady()).toBe(true);
    });

    it('should implement reset method', async () => {
      await initializer.reset();
      const state = initializer.getInitializationState();
      expect(state.status).toBe('NOT_STARTED');
    });

    it('should implement getServiceStatus method', () => {
      const status = initializer.getServiceStatus();
      expect(status).toBe('NOT_STARTED');
    });
  });

  // ============================================================================
  // INITIALIZATION STATE TESTS
  // ============================================================================

  describe('getInitializationState', () => {
    it('should return a copy of initialization state', () => {
      const state1 = initializer.getInitializationState();
      const state2 = initializer.getInitializationState();

      expect(state1).toEqual(state2);
      expect(state1).not.toBe(state2); // Different object references
    });

    it('should track steps completed during initialization', async () => {
      mockDatabaseService.getConnectionHealth.mockResolvedValue({
        isHealthy: true,
        connectionPoolSize: 10,
        activeConnections: 5,
        idleConnections: 5,
        lastCheckTime: new Date(),
        responseTimeMs: 10,
      });

      mockDatabaseService.executeRawQuery.mockResolvedValue([1]);

      const mockExec = exec as jest.MockedFunction<typeof exec>;
      (mockExec as any).mockImplementation(
        (_cmd: string, _opts: any, callback: Function) => {
          callback(null, { stdout: 'Migrations applied', stderr: '' });
        }
      );

      await initializer.initializeDatabase();

      const state = initializer.getInitializationState();
      expect(state.stepsCompleted).toContain('connection_verified');
      expect(state.stepsCompleted).toContain('migrations_executed');
      expect(state.stepsCompleted).toContain('schema_validated');
      expect(state.stepsCompleted).toContain('oa_configuration_loaded');
    });
  });

  // ============================================================================
  // ABSTRACT METHOD IMPLEMENTATION TESTS
  // ============================================================================

  describe('Abstract Method Implementations', () => {
    it('should implement doTrack', async () => {
      const trackData = {
        trackingId: 'test123',
        componentId: 'database-initializer',
        operationType: 'initialization',
        timestamp: new Date(),
        data: {},
      };

      await expect(
        (initializer as any).doTrack(trackData)
      ).resolves.not.toThrow();
    });

    it('should implement doValidate', async () => {
      mockDatabaseService.getConnectionHealth.mockResolvedValue({
        isHealthy: true,
        connectionPoolSize: 10,
        activeConnections: 5,
        idleConnections: 5,
        lastCheckTime: new Date(),
        responseTimeMs: 10,
      });

      mockDatabaseService.executeRawQuery.mockResolvedValue([1]);

      const result = await (initializer as any).doValidate();

      expect(result).toHaveProperty('validationId');
      expect(result).toHaveProperty('status');
      expect(result).toHaveProperty('isValid');
    });
  });

  // ============================================================================
  // EDGE CASES AND BRANCH COVERAGE TESTS
  // ============================================================================

  describe('Edge Cases and Branch Coverage', () => {
    it('should auto-initialize when config.autoInitialize is true', async () => {
      const autoConfig = { autoInitialize: true };
      const autoInitializer = new DatabaseInitializer(
        autoConfig,
        mockDatabaseService,
        mockAuthorityEngine,
        mockAuditLogger
      );

      mockDatabaseService.getConnectionHealth.mockResolvedValue({
        isHealthy: true,
        connectionPoolSize: 10,
        activeConnections: 5,
        idleConnections: 5,
        lastCheckTime: new Date(),
        responseTimeMs: 10,
      });

      mockDatabaseService.executeRawQuery.mockResolvedValue([1]);

      const mockExec = exec as jest.MockedFunction<typeof exec>;
      (mockExec as any).mockImplementation(
        (_cmd: string, _opts: any, callback: Function) => {
          callback(null, { stdout: 'Migrations applied', stderr: '' });
        }
      );

      await autoInitializer.initialize();

      const state = autoInitializer.getInitializationState();
      expect(state.status).toBe('COMPLETE');
    });

    it('should handle non-Error exceptions in checkDatabaseState', async () => {
      mockDatabaseService.getConnectionHealth.mockRejectedValueOnce({
        code: 'ERR_DB',
        details: 'Database error',
      });

      const state = await initializer.checkDatabaseState();

      expect(state.status).toBe('INVALID');
      expect(state.isReady).toBe(false);
      expect(state.issues.length).toBeGreaterThan(0);
    });

    it('should handle logging errors in _logInternalOperation', async () => {
      const originalShouldLog = mockAuditLogger.shouldLog;
      mockAuditLogger.shouldLog = jest
        .fn()
        .mockRejectedValueOnce(new Error('Logging error'));

      // This should not throw even if logging fails
      await expect(
        (initializer as any)._logInternalOperation(
          'test.category',
          { test: 'data' },
          'info'
        )
      ).resolves.not.toThrow();

      mockAuditLogger.shouldLog = originalShouldLog;
    });

    it('should handle errors when non-Error objects are thrown during initialization', async () => {
      mockDatabaseService.getConnectionHealth.mockRejectedValueOnce(
        'String error message'
      );

      await expect(initializer.initializeDatabase()).rejects.toThrow();

      const state = initializer.getInitializationState();
      expect(state.status).toBe('FAILED');
      expect(state.lastError).toContain('Database connection failed');
    });

    it('should handle non-Error thrown in checkDatabaseState catch block', async () => {
      // Override verifyDatabaseConnection to throw synchronously
      const originalVerify = initializer.verifyDatabaseConnection;
      initializer.verifyDatabaseConnection = jest.fn().mockImplementation(() => {
        throw {code: 'ERR_CUSTOM', msg: 'Custom error'};
      });

      const state = await initializer.checkDatabaseState();

      expect(state.status).toBe('INVALID');
      expect(state.isReady).toBe(false);
      expect(state.issues.length).toBeGreaterThan(0);

      // Restore
      initializer.verifyDatabaseConnection = originalVerify;
    });

    it('should test all nullable config values with defaults', () => {
      const minimalConfig = {};
      const minimalInitializer = new DatabaseInitializer(
        minimalConfig,
        mockDatabaseService,
        mockAuthorityEngine,
        mockAuditLogger
      );

      const state = minimalInitializer.getInitializationState();
      expect(state.status).toBe('NOT_STARTED');
    });

    it('should handle migration errors correctly', async () => {
      const mockExec = exec as jest.MockedFunction<typeof exec>;
      (mockExec as any).mockImplementation(
        (_cmd: string, _opts: any, callback: Function) => {
          callback(new Error('Timeout exceeded'), null);
        }
      );

      const result = await initializer.runMigrations();

      expect(result.status).toBe('FAILED');
      expect(result.errors).toContain('Timeout exceeded');
    });

    it('should handle schema validation returning false', async () => {
      mockDatabaseService.executeRawQuery.mockRejectedValueOnce(
        new Error('Table not found')
      );

      const isValid = await initializer.validateSchema();
      expect(isValid).toBe(false);
    });

    it('should complete initialization with all steps when fully enabled', async () => {
      const fullConfig = {
        autoInitialize: false,
        runMigrations: true,
        loadSeedData: true,
        validateSchema: true,
      };

      const fullInitializer = new DatabaseInitializer(
        fullConfig,
        mockDatabaseService,
        mockAuthorityEngine,
        mockAuditLogger
      );

      mockDatabaseService.getConnectionHealth.mockResolvedValue({
        isHealthy: true,
        connectionPoolSize: 10,
        activeConnections: 5,
        idleConnections: 5,
        lastCheckTime: new Date(),
        responseTimeMs: 10,
      });

      mockDatabaseService.executeRawQuery.mockResolvedValue([1]);

      const mockExec = exec as jest.MockedFunction<typeof exec>;
      (mockExec as any).mockImplementation(
        (_cmd: string, _opts: any, callback: Function) => {
          callback(null, { stdout: 'Migrations applied', stderr: '' });
        }
      );

      await fullInitializer.initializeDatabase();

      const state = fullInitializer.getInitializationState();
      expect(state.stepsCompleted).toContain('seed_data_loaded');
      expect(state.seedDataLoaded).toBe(true);
    });
  });
});
