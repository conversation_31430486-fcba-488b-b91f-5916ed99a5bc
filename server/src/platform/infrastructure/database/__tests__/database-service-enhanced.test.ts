// ============================================================================
// TEST SUITE: DatabaseServiceEnhanced
// M1-CORE-DB-02 | platform-database-service-enhanced
// ============================================================================

import { DatabaseServiceEnhanced } from '../database-service-enhanced';
import { AuditLoggingConfigurationService } from '../../logging/configuration/AuditLoggingConfigurationService';
import type { TDatabaseServiceConfig } from '../types';

// ============================================================================
// TEST SETUP
// ============================================================================

describe('DatabaseServiceEnhanced', () => {
  let dbService: DatabaseServiceEnhanced;
  let loggingService: AuditLoggingConfigurationService;

  beforeEach(() => {
    // Create mock logging service
    loggingService = {
      isInitialized: () => true,
      initialize: jest.fn().mockResolvedValue(undefined),
      shouldLog: jest.fn().mockResolvedValue(true),
      resolveConfiguration: jest.fn().mockResolvedValue({
        enabled: true,
        logLevel: 'info',
      }),
    } as any;

    // Create database configuration
    const config: TDatabaseServiceConfig = {
      databaseUrl: process.env.DATABASE_CONFIG_URL || 'postgresql://testuser:testuser#19@localhost:5432/oa_config_db',
      maxQueryTimeout: 10000,
      connectionTimeout: 5000,
      enableLogging: true,
      enableMetrics: true,
      enableMonitoring: true,
      poolSize: 20,
    };

    // Instantiate service
    dbService = new DatabaseServiceEnhanced(config, loggingService);
  });

  afterEach(async () => {
    if (dbService.isReady()) {
      await dbService.shutdown();
    }
  });

  // ============================================================================
  // INITIALIZATION TESTS
  // ============================================================================

  describe('Initialization', () => {
    it('should create instance with configuration', () => {
      expect(dbService).toBeDefined();
      expect(dbService.getServiceName()).toBe('Database Service Enhanced');
      expect(dbService.getServiceVersion()).toBe('1.0.0');
    });

    it('should initialize with valid configuration', async () => {
      await dbService.initialize();
      expect(dbService.isReady()).toBe(true);
    });

    it('should not be ready before initialization', () => {
      expect(dbService.isReady()).toBe(false);
    });

    it('should have ResilientTimer initialized', () => {
      expect((dbService as any)._resilientTimer).toBeDefined();
    });

    it('should have ResilientMetricsCollector initialized', () => {
      expect((dbService as any)._metricsCollector).toBeDefined();
    });

    it('should initialize logging service if not initialized', async () => {
      // Create new service with uninitialized logging service
      const uninitializedLogging = {
        isInitialized: jest.fn().mockReturnValue(false),
        initialize: jest.fn().mockResolvedValue(undefined),
        shouldLog: jest.fn().mockResolvedValue(true),
        resolveConfiguration: jest.fn().mockResolvedValue({
          enabled: true,
          logLevel: 'info',
        }),
      } as any;

      const config: TDatabaseServiceConfig = {
        databaseUrl: process.env.DATABASE_CONFIG_URL || 'postgresql://testuser:testuser#19@localhost:5432/oa_config_db',
        maxQueryTimeout: 10000,
        connectionTimeout: 5000,
        enableLogging: true,
        enableMetrics: true,
        enableMonitoring: true,
        poolSize: 20,
      };

      const newService = new DatabaseServiceEnhanced(config, uninitializedLogging);
      await newService.initialize();

      expect(uninitializedLogging.initialize).toHaveBeenCalled();

      await newService.shutdown();
    });
  });

  // ============================================================================
  // QUERY EXECUTION TESTS
  // ============================================================================

  describe('Query Execution', () => {
    beforeEach(async () => {
      await dbService.initialize();
    });

    it('should execute parameterized query', async () => {
      const query = 'SELECT 1 as result';
      const result = await dbService.executeQuery<{ result: number }>(query);
      expect(result).toBeDefined();
    });

    it('should execute raw SQL query', async () => {
      const sql = 'SELECT 1 as test';
      const result = await dbService.executeRawQuery<{ test: number }>(sql);
      expect(result).toBeDefined();
    });

    it('should sanitize queries in logs', async () => {
      const query = "SELECT * FROM users WHERE password = 'secret123'";
      try {
        await dbService.executeQuery(query);
      } catch (e) {
        // Expected to fail, but sanitization should occur
      }
      expect(loggingService.shouldLog).toHaveBeenCalled();
    });

    it('should record timing for executeQuery', async () => {
      const metricsCollector = (dbService as any)._metricsCollector;
      const recordTimingSpy = jest.spyOn(metricsCollector, 'recordTiming');

      try {
        await dbService.executeQuery('SELECT 1');
      } catch (e) {
        // Expected to fail in test environment
      }

      // Verify timing was attempted (might fail if timing not available)
      expect(recordTimingSpy).toHaveBeenCalledWith(
        'executeQuery',
        expect.objectContaining({
          duration: expect.any(Number),
        })
      );
    });
  });

  // ============================================================================
  // TRANSACTION TESTS
  // ============================================================================

  describe('Transaction Management', () => {
    beforeEach(async () => {
      await dbService.initialize();
    });

    it('should begin transaction', async () => {
      const transaction = await dbService.beginTransaction();
      expect(transaction).toBeDefined();
      expect(transaction.id).toMatch(/^db_tx_/);
      expect(transaction.startTime).toBeInstanceOf(Date);
    });

    it('should commit transaction', async () => {
      const transaction = await dbService.beginTransaction();
      await dbService.commit(transaction);
      expect(transaction.isCommitted).toBe(true);
    });

    it('should rollback transaction', async () => {
      const transaction = await dbService.beginTransaction();
      await dbService.rollback(transaction);
      expect(transaction.isRolledBack).toBe(true);
    });

    it('should generate unique transaction IDs', async () => {
      const tx1 = await dbService.beginTransaction();
      const tx2 = await dbService.beginTransaction();
      expect(tx1.id).not.toBe(tx2.id);
    });
  });

  // ============================================================================
  // HEALTH MONITORING TESTS
  // ============================================================================

  describe('Health Monitoring', () => {
    beforeEach(async () => {
      await dbService.initialize();
    });

    it('should get connection health', async () => {
      const health = await dbService.getConnectionHealth();
      expect(health).toBeDefined();
      expect(health).toHaveProperty('isHealthy');
      expect(health).toHaveProperty('lastCheckTime');
      expect(health).toHaveProperty('responseTimeMs');
    });

    it('should get query performance metrics', async () => {
      const metrics = await dbService.getQueryPerformanceMetrics();
      expect(metrics).toBeDefined();
      expect(metrics).toHaveProperty('averageQueryTimeMs');
      expect(metrics).toHaveProperty('totalQueries');
      expect(metrics).toHaveProperty('slowQueries');
      expect(metrics).toHaveProperty('metrics');
    });

    it('should get health status', async () => {
      const status = await dbService.getHealthStatus();
      expect(status).toBeDefined();
      expect(status).toHaveProperty('healthy');
      expect(status).toHaveProperty('message');
      expect(status).toHaveProperty('timestamp');
    });
  });

  // ============================================================================
  // LIFECYCLE TESTS
  // ============================================================================

  describe('Lifecycle Management', () => {
    it('should initialize and shutdown properly', async () => {
      expect(dbService.isReady()).toBe(false);

      await dbService.initialize();
      expect(dbService.isReady()).toBe(true);

      await dbService.shutdown();
      expect(dbService.isShuttingDown()).toBe(true);
    });

    it('should handle multiple shutdown calls', async () => {
      await dbService.initialize();
      await dbService.shutdown();

      // Second shutdown should not error
      await expect(dbService.shutdown()).resolves.not.toThrow();
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('M0.3 Logging Integration', () => {
    beforeEach(async () => {
      await dbService.initialize();
    });

    it('should call logging service for operations', async () => {
      try {
        await dbService.executeQuery('SELECT 1');
      } catch (e) {
        // Expected in test
      }
      expect(loggingService.shouldLog).toHaveBeenCalled();
    });

    it('should respect logging configuration', async () => {
      const logSpy = jest.spyOn(loggingService, 'shouldLog');
      logSpy.mockResolvedValueOnce(false);

      try {
        await dbService.executeQuery('SELECT 1');
      } catch (e) {
        // Expected
      }

      // Logging service should have been checked
      expect(logSpy).toHaveBeenCalled();
    });
  });

  // ============================================================================
  // ERROR HANDLING TESTS
  // ============================================================================

  describe('Error Handling', () => {
    beforeEach(async () => {
      await dbService.initialize();
    });

    it('should handle invalid queries', async () => {
      const invalidQuery = 'INVALID SQL SYNTAX @#$';
      await expect(dbService.executeQuery(invalidQuery)).rejects.toThrow();
    });

    it('should log errors with M0.3 integration', async () => {
      try {
        await dbService.executeQuery('INVALID');
      } catch (e) {
        // Expected
      }
      // Error logging should have been attempted
      expect(loggingService.shouldLog).toHaveBeenCalled();
    });
  });

  // ============================================================================
  // MEMORY SAFETY TESTS
  // ============================================================================

  describe('Memory Safety (Rule 04)', () => {
    it('should extend MemorySafeResourceManager', () => {
      const proto = Object.getPrototypeOf(dbService);
      expect(proto.constructor.name).toBe('DatabaseServiceEnhanced');
    });

    it('should have resource limits configured', () => {
      const limits = (dbService as any)._limits;
      expect(limits).toBeDefined();
      expect(limits.maxIntervals).toBeGreaterThan(0);
      expect(limits.maxTimeouts).toBeGreaterThan(0);
      expect(limits.maxCacheSize).toBeGreaterThan(0);
    });
  });

  // ============================================================================
  // PERFORMANCE TESTS
  // ============================================================================

  describe('Performance', () => {
    beforeEach(async () => {
      await dbService.initialize();
    });

    it('should execute queries within timing budget', async () => {
      const startTime = Date.now();
      try {
        await dbService.executeQuery('SELECT 1');
      } catch (e) {
        // Expected in test environment
      }
      const duration = Date.now() - startTime;

      // Should complete within reasonable timeout
      expect(duration).toBeLessThan(60000); // 60 second timeout
    });

    it('should generate operation IDs with timestamp prefix', async () => {
      const tx = await dbService.beginTransaction();
      expect(tx.id).toMatch(/^db_tx_\d+_/);
    });
  });

  // ============================================================================
  // CRUD OPERATIONS TESTS (for coverage)
  // ============================================================================

  describe('CRUD Operations with Actual Models', () => {
    beforeEach(async () => {
      await dbService.initialize();
    });

    it('should find records from a model', async () => {
      const result = await dbService.find('oAConfiguration', {
        isActive: true,
      });
      expect(Array.isArray(result)).toBe(true);
    });

    it('should find unique record from a model', async () => {
      // First create a test record
      const created = await dbService.create('oAConfiguration', {
        key: 'test-key-unique',
        value: 'test-value',
        configType: 'test',
        createdBy: 'test-suite',
        isActive: true,
      });

      // Then find it
      const result = await dbService.findUnique('oAConfiguration', {
        key: 'test-key-unique',
      });
      expect(result).toBeDefined();

      // Cleanup
      if (result) {
        await dbService.delete('oAConfiguration', {
          key: 'test-key-unique',
        });
      }
    });

    it('should create a record', async () => {
      const created = await dbService.create('oAConfiguration', {
        key: 'test-key-create',
        value: 'test-value-create',
        configType: 'test',
        createdBy: 'test-suite',
        isActive: true,
      });

      expect(created).toBeDefined();
      expect((created as any).key).toBe('test-key-create');

      // Cleanup
      await dbService.delete('oAConfiguration', {
        key: 'test-key-create',
      });
    });

    it('should update a record', async () => {
      // Create record first
      await dbService.create('oAConfiguration', {
        key: 'test-key-update',
        value: 'original-value',
        configType: 'test',
        createdBy: 'test-suite',
        isActive: true,
      });

      // Update it
      const updated = await dbService.update(
        'oAConfiguration',
        { key: 'test-key-update' },
        { value: 'updated-value' }
      );

      expect(updated).toBeDefined();
      expect((updated as any).value).toBe('updated-value');

      // Cleanup
      await dbService.delete('oAConfiguration', {
        key: 'test-key-update',
      });
    });

    it('should delete a record', async () => {
      // Create record first
      await dbService.create('oAConfiguration', {
        key: 'test-key-delete',
        value: 'value-to-delete',
        configType: 'test',
        createdBy: 'test-suite',
        isActive: true,
      });

      // Delete it
      const deleted = await dbService.delete('oAConfiguration', {
        key: 'test-key-delete',
      });

      expect(deleted).toBeDefined();
      expect((deleted as any).key).toBe('test-key-delete');

      // Verify it's gone
      const found = await dbService.findUnique('oAConfiguration', {
        key: 'test-key-delete',
      });
      expect(found).toBeNull();
    });

    it('should handle find with no results', async () => {
      const result = await dbService.find('oAConfiguration', {
        key: 'non-existent-key-12345',
      });
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(0);
    });

    it('should handle findUnique with no result', async () => {
      const result = await dbService.findUnique('oAConfiguration', {
        key: 'non-existent-unique-key-67890',
      });
      expect(result).toBeNull();
    });
  });

  // ============================================================================
  // ERROR PATH COVERAGE
  // ============================================================================

  describe('Error Path Coverage', () => {
    beforeEach(async () => {
      await dbService.initialize();
    });

    it('should handle create with invalid data', async () => {
      await expect(
        dbService.create('oAConfiguration', {
          // Missing required fields
          key: 'invalid-test',
        })
      ).rejects.toThrow();
    });

    it('should handle update on non-existent record', async () => {
      await expect(
        dbService.update(
          'oAConfiguration',
          { key: 'non-existent-update-key' },
          { value: 'new-value' }
        )
      ).rejects.toThrow();
    });

    it('should handle delete on non-existent record', async () => {
      await expect(
        dbService.delete('oAConfiguration', {
          key: 'non-existent-delete-key',
        })
      ).rejects.toThrow();
    });

    it('should handle logging service errors gracefully', async () => {
      const logSpy = jest.spyOn(loggingService, 'shouldLog');
      logSpy.mockRejectedValueOnce(new Error('Logging service error'));

      // Should not throw even if logging fails
      await expect(dbService.executeQuery('SELECT 1')).resolves.toBeDefined();
    });

    it('should handle executeRawQuery errors', async () => {
      await expect(
        dbService.executeRawQuery('INVALID SQL SYNTAX @#$%^')
      ).rejects.toThrow();
    });

    it('should handle find errors with invalid model', async () => {
      await expect(
        dbService.find('NonExistentModel', { id: 1 })
      ).rejects.toThrow();
    });

    it('should handle findUnique errors with invalid model', async () => {
      await expect(
        dbService.findUnique('NonExistentModel', { id: 1 })
      ).rejects.toThrow();
    });

    it('should handle create errors with invalid model', async () => {
      await expect(
        dbService.create('NonExistentModel', { data: 'test' })
      ).rejects.toThrow();
    });

    it('should handle update errors with invalid model', async () => {
      await expect(
        dbService.update('NonExistentModel', { id: 1 }, { data: 'test' })
      ).rejects.toThrow();
    });

    it('should handle delete errors with invalid model', async () => {
      await expect(
        dbService.delete('NonExistentModel', { id: 1 })
      ).rejects.toThrow();
    });

    it('should handle transaction commit errors', async () => {
      const tx = await dbService.beginTransaction();

      // Mock _logOperation to throw error during commit
      const logSpy = jest.spyOn(dbService as any, '_logOperation');
      logSpy.mockRejectedValueOnce(new Error('Commit logging failed'));

      await expect(dbService.commit(tx)).rejects.toThrow('Commit logging failed');

      // Restore
      logSpy.mockRestore();
    });

    it('should handle transaction rollback errors', async () => {
      const tx = await dbService.beginTransaction();

      // Mock _logOperation to throw error during rollback
      const logSpy = jest.spyOn(dbService as any, '_logOperation');
      logSpy.mockRejectedValueOnce(new Error('Rollback logging failed'));

      await expect(dbService.rollback(tx)).rejects.toThrow('Rollback logging failed');

      // Restore
      logSpy.mockRestore();
    });

    it('should handle transaction begin errors', async () => {
      // Mock _logOperation to throw error during begin
      const logSpy = jest.spyOn(dbService as any, '_logOperation');
      logSpy.mockRejectedValueOnce(new Error('Begin transaction logging failed'));

      await expect(dbService.beginTransaction()).rejects.toThrow('Begin transaction logging failed');

      // Restore
      logSpy.mockRestore();
    });

    it('should handle getConnectionHealth errors', async () => {
      const prismaClient = (dbService as any)._prismaClient;
      const originalQueryRaw = prismaClient.$queryRaw;

      prismaClient.$queryRaw = jest.fn().mockRejectedValue(
        new Error('Connection failed')
      );

      const health = await dbService.getConnectionHealth();
      expect(health.isHealthy).toBe(false);
      expect(health.error).toBeDefined();

      // Restore
      prismaClient.$queryRaw = originalQueryRaw;
    });
  });

  // ============================================================================
  // SHUTDOWN ERROR HANDLING
  // ============================================================================

  describe('Shutdown Error Handling', () => {
    it('should handle shutdown with Prisma disconnect error', async () => {
      await dbService.initialize();

      // Mock Prisma disconnect to throw error
      const prismaClient = (dbService as any)._prismaClient;
      const originalDisconnect = prismaClient.$disconnect;
      prismaClient.$disconnect = jest.fn().mockRejectedValue(
        new Error('Disconnect failed')
      );

      await expect(dbService.shutdown()).rejects.toThrow('Disconnect failed');

      // Restore original disconnect
      prismaClient.$disconnect = originalDisconnect;
    });

    it('should handle shutdown with non-Error exception', async () => {
      await dbService.initialize();

      // Mock Prisma disconnect to throw non-Error
      const prismaClient = (dbService as any)._prismaClient;
      const originalDisconnect = prismaClient.$disconnect;
      prismaClient.$disconnect = jest.fn().mockRejectedValue('String error');

      await expect(dbService.shutdown()).rejects.toThrow();

      // Restore original disconnect
      prismaClient.$disconnect = originalDisconnect;
    });
  });

  // ============================================================================
  // BRANCH COVERAGE COMPLETION
  // ============================================================================

  describe('Branch Coverage for Ternary Operators', () => {
    beforeEach(async () => {
      await dbService.initialize();
    });

    it('should handle executeQuery with params', async () => {
      const result = await dbService.executeQuery('SELECT $1 as value', { value: 42 });
      expect(result).toBeDefined();
    });

    it('should handle executeQuery without params', async () => {
      const result = await dbService.executeQuery('SELECT 1 as value');
      expect(result).toBeDefined();
    });

    it('should handle executeRawQuery with params', async () => {
      const result = await dbService.executeRawQuery('SELECT $1::int as value', [123]);
      expect(result).toBeDefined();
    });

    it('should handle executeRawQuery without params', async () => {
      const result = await dbService.executeRawQuery('SELECT 2 as value');
      expect(result).toBeDefined();
    });

    it('should return healthy message when database is healthy', async () => {
      const status = await dbService.getHealthStatus();
      expect(status.healthy).toBe(true);
      expect(status.message).toBe('Database service is healthy');
    });

    it('should return unhealthy message when database is unhealthy', async () => {
      // Mock getConnectionHealth to return unhealthy
      const originalHealth = dbService.getConnectionHealth.bind(dbService);
      dbService.getConnectionHealth = jest.fn().mockResolvedValue({
        isHealthy: false,
        error: 'Connection timeout',
        lastCheckTime: new Date(),
        connectionPoolSize: 0,
        activeConnections: 0,
        idleConnections: 0,
        responseTimeMs: 0,
      });

      const status = await dbService.getHealthStatus();
      expect(status.healthy).toBe(false);
      expect(status.message).toContain('unhealthy');
      expect(status.message).toContain('Connection timeout');

      // Restore
      dbService.getConnectionHealth = originalHealth;
    });

    it('should return unhealthy message when error is undefined', async () => {
      // Mock getConnectionHealth to return unhealthy without error message
      const originalHealth = dbService.getConnectionHealth.bind(dbService);
      dbService.getConnectionHealth = jest.fn().mockResolvedValue({
        isHealthy: false,
        lastCheckTime: new Date(),
        connectionPoolSize: 0,
        activeConnections: 0,
        idleConnections: 0,
        responseTimeMs: 0,
      });

      const status = await dbService.getHealthStatus();
      expect(status.healthy).toBe(false);
      expect(status.message).toContain('Unknown error');

      // Restore
      dbService.getConnectionHealth = originalHealth;
    });

    it('should handle errors thrown as non-Error objects in executeQuery', async () => {
      const prismaClient = (dbService as any)._prismaClient;
      const original = prismaClient.$queryRawUnsafe;
      prismaClient.$queryRawUnsafe = jest.fn().mockRejectedValue('String error');

      await expect(dbService.executeQuery('SELECT 1')).rejects.toBe('String error');

      prismaClient.$queryRawUnsafe = original;
    });

    it('should handle errors thrown as non-Error objects in executeRawQuery', async () => {
      const prismaClient = (dbService as any)._prismaClient;
      const original = prismaClient.$queryRawUnsafe;
      prismaClient.$queryRawUnsafe = jest.fn().mockRejectedValue({ code: 'ERROR' });

      await expect(dbService.executeRawQuery('SELECT 1')).rejects.toEqual({ code: 'ERROR' });

      prismaClient.$queryRawUnsafe = original;
    });

    it('should handle errors thrown as non-Error objects in find', async () => {
      const prismaClient = (dbService as any)._prismaClient;
      const originalModel = prismaClient.oAConfiguration;
      prismaClient.oAConfiguration = {
        findMany: jest.fn().mockRejectedValue(123),
      };

      await expect(dbService.find('oAConfiguration')).rejects.toBe(123);

      prismaClient.oAConfiguration = originalModel;
    });

    it('should handle errors thrown as non-Error objects in findUnique', async () => {
      const prismaClient = (dbService as any)._prismaClient;
      const originalModel = prismaClient.oAConfiguration;
      prismaClient.oAConfiguration = {
        findUnique: jest.fn().mockRejectedValue(null),
      };

      await expect(dbService.findUnique('oAConfiguration', { key: 'test' })).rejects.toBe(null);

      prismaClient.oAConfiguration = originalModel;
    });

    it('should handle errors thrown as non-Error objects in create', async () => {
      const prismaClient = (dbService as any)._prismaClient;
      const originalModel = prismaClient.oAConfiguration;
      prismaClient.oAConfiguration = {
        create: jest.fn().mockRejectedValue(false),
      };

      await expect(dbService.create('oAConfiguration', {})).rejects.toBe(false);

      prismaClient.oAConfiguration = originalModel;
    });

    it('should handle errors thrown as non-Error objects in update', async () => {
      const prismaClient = (dbService as any)._prismaClient;
      const originalModel = prismaClient.oAConfiguration;
      prismaClient.oAConfiguration = {
        update: jest.fn().mockRejectedValue([]),
      };

      await expect(dbService.update('oAConfiguration', {}, {})).rejects.toEqual([]);

      prismaClient.oAConfiguration = originalModel;
    });

    it('should handle errors thrown as non-Error objects in delete', async () => {
      const prismaClient = (dbService as any)._prismaClient;
      const originalModel = prismaClient.oAConfiguration;
      prismaClient.oAConfiguration = {
        delete: jest.fn().mockRejectedValue(undefined),
      };

      await expect(dbService.delete('oAConfiguration', {})).rejects.toBe(undefined);

      prismaClient.oAConfiguration = originalModel;
    });

    it('should handle errors thrown as non-Error objects in beginTransaction', async () => {
      const logSpy = jest.spyOn(dbService as any, '_logOperation');
      logSpy.mockRejectedValueOnce({ message: 'Object error' });

      await expect(dbService.beginTransaction()).rejects.toEqual({ message: 'Object error' });

      logSpy.mockRestore();
    });

    it('should handle errors thrown as non-Error objects in commit', async () => {
      const tx = await dbService.beginTransaction();
      const logSpy = jest.spyOn(dbService as any, '_logOperation');
      logSpy.mockRejectedValueOnce(123);

      await expect(dbService.commit(tx)).rejects.toBe(123);

      logSpy.mockRestore();
    });

    it('should handle errors thrown as non-Error objects in rollback', async () => {
      const tx = await dbService.beginTransaction();
      const logSpy = jest.spyOn(dbService as any, '_logOperation');
      logSpy.mockRejectedValueOnce('rollback error');

      await expect(dbService.rollback(tx)).rejects.toBe('rollback error');

      logSpy.mockRestore();
    });

    it('should use poolSize from config if available', async () => {
      const health = await dbService.getConnectionHealth();
      expect(health.connectionPoolSize).toBe(20); // From config
    });

    it('should use default poolSize if not in config', async () => {
      // Create service with config without poolSize
      const configWithoutPoolSize: TDatabaseServiceConfig = {
        databaseUrl: process.env.DATABASE_CONFIG_URL || 'postgresql://testuser:testuser#19@localhost:5432/oa_config_db',
        maxQueryTimeout: 10000,
        connectionTimeout: 5000,
        enableLogging: true,
        enableMetrics: true,
        enableMonitoring: true,
      };

      const newService = new DatabaseServiceEnhanced(configWithoutPoolSize, loggingService);
      await newService.initialize();

      const health = await newService.getConnectionHealth();
      expect(health.connectionPoolSize).toBe(20); // Default value

      await newService.shutdown();
    });

    it('should use poolSize from config in error case', async () => {
      const prismaClient = (dbService as any)._prismaClient;
      const original$queryRaw = prismaClient.$queryRaw;

      prismaClient.$queryRaw = jest.fn().mockRejectedValue(new Error('Query failed'));

      const health = await dbService.getConnectionHealth();
      expect(health.isHealthy).toBe(false);
      expect(health.connectionPoolSize).toBe(20); // From config

      prismaClient.$queryRaw = original$queryRaw;
    });

    it('should use default poolSize in error case when not in config', async () => {
      // Create service with config without poolSize
      const configWithoutPoolSize: TDatabaseServiceConfig = {
        databaseUrl: process.env.DATABASE_CONFIG_URL || 'postgresql://testuser:testuser#19@localhost:5432/oa_config_db',
        maxQueryTimeout: 10000,
        connectionTimeout: 5000,
        enableLogging: true,
        enableMetrics: true,
        enableMonitoring: true,
      };

      const newService = new DatabaseServiceEnhanced(configWithoutPoolSize, loggingService);
      await newService.initialize();

      const prismaClient = (newService as any)._prismaClient;
      const original$queryRaw = prismaClient.$queryRaw;

      prismaClient.$queryRaw = jest.fn().mockRejectedValue(new Error('Query failed'));

      const health = await newService.getConnectionHealth();
      expect(health.isHealthy).toBe(false);
      expect(health.connectionPoolSize).toBe(20); // Default value

      prismaClient.$queryRaw = original$queryRaw;
      await newService.shutdown();
    });

    it('should handle non-Error exceptions in getConnectionHealth', async () => {
      const prismaClient = (dbService as any)._prismaClient;
      const original$queryRaw = prismaClient.$queryRaw;

      prismaClient.$queryRaw = jest.fn().mockRejectedValue('string error');

      const health = await dbService.getConnectionHealth();
      expect(health.isHealthy).toBe(false);
      expect(health.error).toBe('string error');

      prismaClient.$queryRaw = original$queryRaw;
    });

    it('should test findUnique when result is null', async () => {
      // Already covered by 'should handle findUnique with no result' test
      const result = await dbService.findUnique('oAConfiguration', {
        key: 'definitely-non-existent-key-99999',
      });
      expect(result).toBeNull();
    });

    it('should test findUnique when result is not null', async () => {
      // First create a record
      const created = await dbService.create('oAConfiguration', {
        key: 'test-key-for-findUnique-not-null',
        value: 'test-value',
        configType: 'test',
        createdBy: 'test-suite',
        isActive: true,
      });

      // Then find it (result will be not null)
      const result = await dbService.findUnique('oAConfiguration', {
        key: 'test-key-for-findUnique-not-null',
      });
      expect(result).not.toBeNull();

      // Cleanup
      if (result) {
        await dbService.delete('oAConfiguration', {
          key: 'test-key-for-findUnique-not-null',
        });
      }
    });

    it('should call _logOperation with all log levels', async () => {
      // Test trace level
      await (dbService as any)._logOperation('test.category', { test: 'data' }, 'trace');

      // Test debug level (already covered by existing tests)
      await (dbService as any)._logOperation('test.category', { test: 'data' }, 'debug');

      // Test warn level
      await (dbService as any)._logOperation('test.category', { test: 'data' }, 'warn');

      // Test critical level
      await (dbService as any)._logOperation('test.category', { test: 'data' }, 'critical');

      // Test default (no level specified - should use 'info')
      await (dbService as any)._logOperation('test.category', { test: 'data' });

      expect(true).toBe(true); // Just verifying no errors
    });

    it('should record correct count in find result logging', async () => {
      // Create multiple records to test recordCount
      await dbService.create('oAConfiguration', {
        key: 'test-find-count-1',
        value: 'value-1',
        configType: 'test-count',
        createdBy: 'test-suite',
        isActive: true,
      });

      await dbService.create('oAConfiguration', {
        key: 'test-find-count-2',
        value: 'value-2',
        configType: 'test-count',
        createdBy: 'test-suite',
        isActive: true,
      });

      const results = await dbService.find('oAConfiguration', {
        configType: 'test-count',
      });

      expect(results.length).toBeGreaterThanOrEqual(2);

      // Cleanup
      await dbService.delete('oAConfiguration', { key: 'test-find-count-1' });
      await dbService.delete('oAConfiguration', { key: 'test-find-count-2' });
    });

    it('should test getQueryPerformanceMetrics when snapshot is empty/null', async () => {
      // Mock the metrics collector to return null/empty snapshot
      const originalCollector = (dbService as any)._metricsCollector;
      (dbService as any)._metricsCollector = {
        createSnapshot: jest.fn().mockReturnValue(null),
      };

      const metrics = await dbService.getQueryPerformanceMetrics();
      expect(metrics.averageQueryTimeMs).toBe(0);

      // Restore
      (dbService as any)._metricsCollector = originalCollector;
    });

    it('should test getQueryPerformanceMetrics when snapshot has no executeQuery', async () => {
      // Mock the metrics collector to return snapshot without executeQuery
      const originalCollector = (dbService as any)._metricsCollector;
      (dbService as any)._metricsCollector = {
        createSnapshot: jest.fn().mockReturnValue({
          someOtherMetric: { value: 100 }
        }),
      };

      const metrics = await dbService.getQueryPerformanceMetrics();
      expect(metrics.averageQueryTimeMs).toBe(0);

      // Restore
      (dbService as any)._metricsCollector = originalCollector;
    });

    it('should test isReady when PrismaClient is undefined', () => {
      // Create new service without initializing
      const uninitializedService = new DatabaseServiceEnhanced(
        {
          databaseUrl: 'postgresql://test',
          maxQueryTimeout: 10000,
          connectionTimeout: 5000,
          enableLogging: true,
          enableMetrics: true,
          enableMonitoring: true,
        },
        loggingService
      );

      expect(uninitializedService.isReady()).toBe(false);
    });

    it('should test isReady returns false when shutting down', async () => {
      await dbService.initialize();
      expect(dbService.isReady()).toBe(true);

      // Start shutdown (sets _isShuttingDown to true)
      const shutdownPromise = dbService.shutdown();

      // During shutdown, isReady should return false
      // (Note: This test is tricky because shutdown completes quickly)
      expect(dbService.isShuttingDown()).toBe(true);

      await shutdownPromise;
    });

    it('should handle getQueryPerformanceMetrics when snapshot is not an object', async () => {
      const originalCollector = (dbService as any)._metricsCollector;
      (dbService as any)._metricsCollector = {
        createSnapshot: jest.fn().mockReturnValue('string snapshot'),
      };

      const metrics = await dbService.getQueryPerformanceMetrics();
      expect(metrics.totalQueries).toBe(0); // Map will be empty

      // Restore
      (dbService as any)._metricsCollector = originalCollector;
    });
  });

  // ============================================================================
  // SURGICAL PRECISION: instanceof Error BRANCH COVERAGE
  // ============================================================================

  describe('🎯 SURGICAL PRECISION - instanceof Error Branch Coverage', () => {
    beforeEach(async () => {
      await dbService.initialize();
    });

    // ✅ Pattern: Test both Error objects AND non-Error objects for complete branch coverage
    // Reference: docs/lessons/testing-quick-reference.md - Error Type Differentiation Testing

    it('should handle non-Error objects in executeQuery catch block (Line 294)', async () => {
      const prismaClient = (dbService as any)._prismaClient;
      const original = prismaClient.$queryRawUnsafe;

      // Mock to throw non-Error object (string)
      prismaClient.$queryRawUnsafe = jest.fn().mockRejectedValue('String error in executeQuery');

      try {
        await expect(dbService.executeQuery('SELECT 1')).rejects.toBe('String error in executeQuery');
        // ✅ This covers the FALSE branch: String(error) instead of error.message
      } finally {
        prismaClient.$queryRawUnsafe = original;
      }
    });

    it('should handle non-Error objects in executeRawQuery catch block (Line 336)', async () => {
      const prismaClient = (dbService as any)._prismaClient;
      const original = prismaClient.$queryRawUnsafe;

      // Mock to throw non-Error object (number)
      prismaClient.$queryRawUnsafe = jest.fn().mockRejectedValue(404);

      try {
        await expect(dbService.executeRawQuery('SELECT 1')).rejects.toBe(404);
        // ✅ This covers the FALSE branch: String(error) instead of error.message
      } finally {
        prismaClient.$queryRawUnsafe = original;
      }
    });

    it('should handle non-Error objects in find catch block (Line 381)', async () => {
      const prismaClient = (dbService as any)._prismaClient;
      const originalModel = prismaClient.oAConfiguration;

      // Mock to throw non-Error object (object)
      prismaClient.oAConfiguration = {
        findMany: jest.fn().mockRejectedValue({ code: 'CUSTOM_ERROR', details: 'Find operation failed' }),
      };

      try {
        await expect(dbService.find('oAConfiguration')).rejects.toEqual({
          code: 'CUSTOM_ERROR',
          details: 'Find operation failed'
        });
        // ✅ This covers the FALSE branch: String(error) instead of error.message
      } finally {
        prismaClient.oAConfiguration = originalModel;
      }
    });

    it('should handle non-Error objects in findUnique catch block (Line 425)', async () => {
      const prismaClient = (dbService as any)._prismaClient;
      const originalModel = prismaClient.oAConfiguration;

      // Mock to throw non-Error object (boolean)
      prismaClient.oAConfiguration = {
        findUnique: jest.fn().mockRejectedValue(false),
      };

      try {
        await expect(dbService.findUnique('oAConfiguration', { key: 'test' })).rejects.toBe(false);
        // ✅ This covers the FALSE branch: String(error) instead of error.message
      } finally {
        prismaClient.oAConfiguration = originalModel;
      }
    });

    it('should handle non-Error objects in create catch block (Line 468)', async () => {
      const prismaClient = (dbService as any)._prismaClient;
      const originalModel = prismaClient.oAConfiguration;

      // Mock to throw non-Error object (null)
      prismaClient.oAConfiguration = {
        create: jest.fn().mockRejectedValue(null),
      };

      try {
        await expect(dbService.create('oAConfiguration', {})).rejects.toBe(null);
        // ✅ This covers the FALSE branch: String(error) instead of error.message
      } finally {
        prismaClient.oAConfiguration = originalModel;
      }
    });

    it('should handle non-Error objects in update catch block (Line 513)', async () => {
      const prismaClient = (dbService as any)._prismaClient;
      const originalModel = prismaClient.oAConfiguration;

      // Mock to throw non-Error object (array)
      prismaClient.oAConfiguration = {
        update: jest.fn().mockRejectedValue(['update', 'failed']),
      };

      try {
        await expect(dbService.update('oAConfiguration', {}, {})).rejects.toEqual(['update', 'failed']);
        // ✅ This covers the FALSE branch: String(error) instead of error.message
      } finally {
        prismaClient.oAConfiguration = originalModel;
      }
    });

    it('should handle non-Error objects in delete catch block (Line 556)', async () => {
      const prismaClient = (dbService as any)._prismaClient;
      const originalModel = prismaClient.oAConfiguration;

      // Mock to throw non-Error object (undefined)
      prismaClient.oAConfiguration = {
        delete: jest.fn().mockRejectedValue(undefined),
      };

      try {
        await expect(dbService.delete('oAConfiguration', {})).rejects.toBe(undefined);
        // ✅ This covers the FALSE branch: String(error) instead of error.message
      } finally {
        prismaClient.oAConfiguration = originalModel;
      }
    });

    it('should handle non-Error objects in getConnectionHealth catch block (Line 694)', async () => {
      const prismaClient = (dbService as any)._prismaClient;
      const original$queryRaw = prismaClient.$queryRaw;

      // Mock to throw non-Error object (symbol)
      const symbolError = Symbol('connection-error');
      prismaClient.$queryRaw = jest.fn().mockRejectedValue(symbolError);

      try {
        const health = await dbService.getConnectionHealth();
        expect(health.isHealthy).toBe(false);
        expect(health.error).toBe('Symbol(connection-error)'); // String(symbolError)
        // ✅ This covers the FALSE branch: String(error) instead of error.message
      } finally {
        prismaClient.$queryRaw = original$queryRaw;
      }
    });

    // ✅ Verification test: Ensure Error objects still work (TRUE branch coverage)
    it('should handle Error objects correctly in all catch blocks', async () => {
      const prismaClient = (dbService as any)._prismaClient;
      const original = prismaClient.$queryRawUnsafe;

      // Mock to throw actual Error object
      const testError = new Error('Test error message');
      prismaClient.$queryRawUnsafe = jest.fn().mockRejectedValue(testError);

      try {
        await expect(dbService.executeQuery('SELECT 1')).rejects.toThrow('Test error message');
        // ✅ This verifies the TRUE branch: error.message is used
      } finally {
        prismaClient.$queryRawUnsafe = original;
      }
    });
  });
});
