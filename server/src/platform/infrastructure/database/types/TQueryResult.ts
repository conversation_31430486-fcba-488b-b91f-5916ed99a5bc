// ============================================================================
// QUERY RESULT TYPE
// M1-CORE-DB-02 | platform-database-service-enhanced
// ============================================================================

/**
 * Generic query result wrapper with metadata
 */
export interface TQueryResult<T = any> {
  /** Query result data */
  data: T;

  /** Number of rows affected */
  rowCount: number;

  /** Query execution time in milliseconds */
  executionTimeMs: number;

  /** Whether the query was successful */
  success: boolean;

  /** Error message if query failed */
  error?: string;

  /** Operation ID for tracking */
  operationId?: string;

  /** Timestamp of query execution */
  timestamp: Date;
}

/**
 * Query result for create/update/delete operations
 */
export interface TMutationResult {
  /** Number of rows affected */
  count: number;

  /** Execution time in milliseconds */
  executionTimeMs: number;

  /** Operation was successful */
  success: boolean;

  /** Error if operation failed */
  error?: string;
}
