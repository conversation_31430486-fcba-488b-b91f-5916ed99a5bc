// ============================================================================
// INITIALIZATION STATE TYPE
// ============================================================================

/**
 * Initialization state status
 */
export type TInitializationStatus =
  | 'NOT_STARTED'
  | 'IN_PROGRESS'
  | 'VALIDATING'
  | 'MIGRATING'
  | 'SEEDING'
  | 'COMPLETE'
  | 'FAILED';

/**
 * State of database initialization process
 */
export type TInitializationState = {
  /**
   * Current initialization status
   */
  status: TInitializationStatus;

  /**
   * Initialization start time
   */
  startTime: Date | null;

  /**
   * Initialization completion time
   */
  completionTime: Date | null;

  /**
   * Connection verified successfully
   */
  connectionVerified: boolean;

  /**
   * Migrations executed successfully
   */
  migrationsExecuted: boolean;

  /**
   * Schema validated successfully
   */
  schemaValidated: boolean;

  /**
   * Seed data loaded successfully
   */
  seedDataLoaded: boolean;

  /**
   * OA configuration loaded successfully
   */
  oaConfigurationLoaded: boolean;

  /**
   * Retry attempts made
   */
  retryAttempts: number;

  /**
   * Last error encountered (if any)
   */
  lastError: string | null;

  /**
   * Initialization steps completed
   */
  stepsCompleted: string[];

  /**
   * Total duration in milliseconds
   */
  durationMs: number;
};
