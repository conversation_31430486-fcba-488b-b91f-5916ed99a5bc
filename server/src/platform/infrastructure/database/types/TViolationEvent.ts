// ============================================================================
// VIOLATION EVENT TYPE
// ============================================================================

/**
 * Violation severity levels
 */
export type TViolationSeverity = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';

/**
 * Violation types
 */
export type TViolationType =
  | 'UNAUTHORIZED_ACCESS'
  | 'POLICY_VIOLATION'
  | 'COMPLIANCE_BREACH'
  | 'AUTHORITY_MISMATCH'
  | 'DATA_INTEGRITY'
  | 'GOVERNANCE_FAILURE';

/**
 * Governance violation event
 */
export type TViolationEvent = {
  /**
   * Unique violation ID
   */
  violationId: string;

  /**
   * Violation type
   */
  type: TViolationType;

  /**
   * Violation severity
   */
  severity: TViolationSeverity;

  /**
   * Violation description
   */
  description: string;

  /**
   * Component where violation occurred
   */
  component: string;

  /**
   * Operation that caused violation
   */
  operation: string;

  /**
   * Actor who triggered violation
   */
  actor: string;

  /**
   * Detection timestamp
   */
  detectedAt: Date;

  /**
   * Policy violated
   */
  policyId?: string;

  /**
   * Expected authority level
   */
  expectedAuthority?: string;

  /**
   * Actual authority level
   */
  actualAuthority?: string;

  /**
   * Violation context
   */
  context: Record<string, unknown>;

  /**
   * Remediation actions taken
   */
  remediationActions: string[];

  /**
   * Violation resolved
   */
  resolved: boolean;

  /**
   * Resolution timestamp
   */
  resolvedAt?: Date;
};
