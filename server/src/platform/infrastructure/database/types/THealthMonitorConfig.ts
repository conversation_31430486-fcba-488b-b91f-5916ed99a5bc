// ============================================================================
// DATABASE HEALTH MONITOR CONFIGURATION TYPE
// ============================================================================

/**
 * Database health monitor configuration
 */
export type THealthMonitorConfig = {
  /** Monitoring enabled flag */
  enabled: boolean;

  /** Health check interval in milliseconds */
  checkInterval: number;

  /** Metrics collection interval in milliseconds */
  metricsInterval: number;

  /** Alert check interval in milliseconds */
  alertCheckInterval: number;

  /** Connection pool thresholds */
  connectionPoolThresholds: {
    /** Maximum connection pool utilization (0-1) */
    maxUtilization: number;

    /** Minimum idle connections */
    minIdleConnections: number;

    /** Maximum wait time for connection (ms) */
    maxWaitTime: number;
  };

  /** Query performance thresholds */
  queryPerformanceThresholds: {
    /** Slow query threshold (ms) */
    slowQueryThreshold: number;

    /** Maximum average query time (ms) */
    maxAverageQueryTime: number;

    /** Maximum query error rate (0-1) */
    maxErrorRate: number;
  };

  /** System resource thresholds */
  systemResourceThresholds: {
    /** Maximum CPU utilization (0-1) */
    maxCpuUtilization: number;

    /** Maximum memory utilization (0-1) */
    maxMemoryUtilization: number;

    /** Maximum disk utilization (0-1) */
    maxDiskUtilization: number;
  };

  /** Alert configuration */
  alertConfig: {
    /** Enable alerts */
    enabled: boolean;

    /** Alert cooldown period (ms) */
    cooldownPeriod: number;

    /** Maximum alerts per hour */
    maxAlertsPerHour: number;
  };

  /** Health event history size */
  maxHealthEventsHistory: number;

  /** Metrics retention period (ms) */
  metricsRetentionPeriod: number;
};
