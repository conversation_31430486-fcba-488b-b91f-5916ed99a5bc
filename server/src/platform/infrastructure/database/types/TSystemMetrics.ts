// ============================================================================
// SYSTEM METRICS TYPE
// ============================================================================

/**
 * System resource metrics for database health
 */
export type TSystemMetrics = {
  /** CPU utilization (0-1) */
  cpuUtilization: number;

  /** Memory utilization (0-1) */
  memoryUtilization: number;

  /** Total memory (bytes) */
  totalMemory: number;

  /** Used memory (bytes) */
  usedMemory: number;

  /** Free memory (bytes) */
  freeMemory: number;

  /** Disk utilization (0-1) */
  diskUtilization: number;

  /** Total disk space (bytes) */
  totalDiskSpace: number;

  /** Used disk space (bytes) */
  usedDiskSpace: number;

  /** Free disk space (bytes) */
  freeDiskSpace: number;

  /** System uptime (ms) */
  uptime: number;

  /** Load average (1, 5, 15 minutes) */
  loadAverage: number[];

  /** Last metric collection timestamp */
  timestamp: Date;

  /** System health status */
  isHealthy: boolean;

  /** Issues detected */
  issues: string[];
};
