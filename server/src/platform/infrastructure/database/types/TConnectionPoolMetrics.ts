// ============================================================================
// CONNECTION POOL METRICS TYPE
// ============================================================================

/**
 * Connection pool metrics from Prisma
 */
export type TConnectionPoolMetrics = {
  /** Total connection pool size */
  poolSize: number;

  /** Active connections count */
  activeConnections: number;

  /** Idle connections count */
  idleConnections: number;

  /** Connection pool utilization (0-1) */
  utilization: number;

  /** Average wait time for connection (ms) */
  averageWaitTime: number;

  /** Maximum wait time for connection (ms) */
  maxWaitTime: number;

  /** Connection timeout count */
  timeoutCount: number;

  /** Connections acquired count */
  acquiredCount: number;

  /** Connections released count */
  releasedCount: number;

  /** Last metric collection timestamp */
  timestamp: Date;

  /** Pool health status */
  isHealthy: boolean;

  /** Issues detected */
  issues: string[];
};
