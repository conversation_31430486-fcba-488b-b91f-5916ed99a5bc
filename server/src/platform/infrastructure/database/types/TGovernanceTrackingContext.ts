// ============================================================================
// GOVERNANCE TRACKING CONTEXT TYPE
// ============================================================================

/**
 * Context for database governance tracking
 */
export type TGovernanceTrackingContext = {
  /**
   * Unique tracking ID
   */
  trackingId: string;

  /**
   * Component being tracked
   */
  componentId: string;

  /**
   * Operation being performed
   */
  operation: string;

  /**
   * User or system performing operation
   */
  actor: string;

  /**
   * Authority level required
   */
  requiredAuthority: string;

  /**
   * Timestamp of operation
   */
  timestamp: Date;

  /**
   * Operation metadata
   */
  metadata: Record<string, unknown>;

  /**
   * Governance policy ID
   */
  policyId?: string;

  /**
   * Compliance check results
   */
  complianceResults?: TComplianceCheckResult[];
};

/**
 * Compliance check result
 */
export type TComplianceCheckResult = {
  /**
   * Check name
   */
  checkName: string;

  /**
   * Check passed
   */
  passed: boolean;

  /**
   * Check severity
   */
  severity: 'info' | 'warning' | 'error' | 'critical';

  /**
   * Check message
   */
  message: string;

  /**
   * Timestamp
   */
  timestamp: Date;
};
