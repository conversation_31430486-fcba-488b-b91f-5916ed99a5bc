// ============================================================================
// DATABASE SERVICE CONFIGURATION TYPE
// M1-CORE-DB-02 | platform-database-service-enhanced
// ============================================================================

/**
 * Configuration for DatabaseServiceEnhanced
 * Includes Prisma client options and service-specific settings
 */
export interface TDatabaseServiceConfig {
  /** Database URL for Prisma connection */
  databaseUrl: string;

  /** Maximum query timeout in milliseconds */
  maxQueryTimeout?: number;

  /** Connection timeout in milliseconds */
  connectionTimeout?: number;

  /** Enable query logging */
  enableLogging?: boolean;

  /** Log level for queries */
  logLevel?: 'trace' | 'debug' | 'info' | 'warn' | 'error' | 'critical';

  /** Pool size for database connections */
  poolSize?: number;

  /** Enable metrics collection */
  enableMetrics?: boolean;

  /** Enable performance monitoring */
  enableMonitoring?: boolean;

  /** Custom Prisma client options */
  prismaOptions?: Record<string, any>;
}
