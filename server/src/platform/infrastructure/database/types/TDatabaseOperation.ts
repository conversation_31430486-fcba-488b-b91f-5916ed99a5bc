// ============================================================================
// DATABASE OPERATION TYPE
// ============================================================================

/**
 * Database operation types
 */
export type TDatabaseOperationType =
  | 'QUERY'
  | 'INSERT'
  | 'UPDATE'
  | 'DELETE'
  | 'TRANSACTION'
  | 'MIGRATION'
  | 'SCHEMA_CHANGE'
  | 'HEALTH_CHECK';

/**
 * Database operation for governance tracking
 */
export type TDatabaseOperation = {
  /**
   * Unique operation ID
   */
  operationId: string;

  /**
   * Operation type
   */
  type: TDatabaseOperationType;

  /**
   * Operation description
   */
  description: string;

  /**
   * Table(s) affected
   */
  tables: string[];

  /**
   * Operation timestamp
   */
  timestamp: Date;

  /**
   * User/actor performing operation
   */
  actor: string;

  /**
   * Operation status
   */
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';

  /**
   * Duration in milliseconds
   */
  durationMs?: number;

  /**
   * Rows affected
   */
  rowsAffected?: number;

  /**
   * Error message (if failed)
   */
  error?: string;

  /**
   * Operation metadata
   */
  metadata: Record<string, unknown>;
};

/**
 * Operation context for authority validation
 */
export type TOperationContext = {
  /**
   * Operation details
   */
  operation: TDatabaseOperation;

  /**
   * Required authority level
   */
  requiredAuthority: string;

  /**
   * Actor's current authority
   */
  actorAuthority?: string;

  /**
   * Governance policy to apply
   */
  policyId?: string;

  /**
   * Additional context
   */
  context: Record<string, unknown>;
};
