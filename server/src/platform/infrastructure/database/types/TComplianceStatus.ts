// ============================================================================
// COMPLIANCE STATUS TYPE
// ============================================================================

/**
 * Compliance status levels
 */
export type TComplianceLevel =
  | 'COMPLIANT'
  | 'NON_COMPLIANT'
  | 'PARTIALLY_COMPLIANT'
  | 'UNDER_REVIEW'
  | 'UNKNOWN';

/**
 * Compliance status for database operations
 */
export type TComplianceStatus = {
  /**
   * Overall compliance level
   */
  level: TComplianceLevel;

  /**
   * Compliance score (0-100)
   */
  score: number;

  /**
   * Number of checks performed
   */
  totalChecks: number;

  /**
   * Number of checks passed
   */
  passedChecks: number;

  /**
   * Number of checks failed
   */
  failedChecks: number;

  /**
   * Compliance issues found
   */
  issues: TComplianceIssue[];

  /**
   * Recommendations for improvement
   */
  recommendations: string[];

  /**
   * Last check timestamp
   */
  lastCheckTime: Date;

  /**
   * Next check due
   */
  nextCheckDue?: Date;
};

/**
 * Compliance issue
 */
export type TComplianceIssue = {
  /**
   * Issue ID
   */
  id: string;

  /**
   * Issue severity
   */
  severity: 'info' | 'warning' | 'error' | 'critical';

  /**
   * Issue category
   */
  category: string;

  /**
   * Issue description
   */
  description: string;

  /**
   * Affected component
   */
  component: string;

  /**
   * Detection timestamp
   */
  detectedAt: Date;

  /**
   * Resolution status
   */
  resolved: boolean;

  /**
   * Resolution timestamp
   */
  resolvedAt?: Date;
};
