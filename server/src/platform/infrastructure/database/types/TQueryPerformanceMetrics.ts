// ============================================================================
// QUERY PERFORMANCE METRICS TYPE
// ============================================================================

/**
 * Query performance metrics from Prisma events
 */
export type TQueryPerformanceMetrics = {
  /** Total queries executed */
  totalQueries: number;

  /** Successful queries */
  successfulQueries: number;

  /** Failed queries */
  failedQueries: number;

  /** Query error rate (0-1) */
  errorRate: number;

  /** Average query execution time (ms) */
  averageQueryTime: number;

  /** Median query execution time (ms) */
  medianQueryTime: number;

  /** P95 query execution time (ms) */
  p95QueryTime: number;

  /** P99 query execution time (ms) */
  p99QueryTime: number;

  /** Slow queries count (above threshold) */
  slowQueriesCount: number;

  /** Slow queries percentage (0-1) */
  slowQueriesRate: number;

  /** Queries per second */
  queriesPerSecond: number;

  /** Last metric collection timestamp */
  timestamp: Date;

  /** Performance health status */
  isHealthy: boolean;

  /** Issues detected */
  issues: string[];
};
