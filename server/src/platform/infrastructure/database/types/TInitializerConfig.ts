// ============================================================================
// INITIALIZER CONFIGURATION TYPE
// ============================================================================

/**
 * Configuration for database initialization
 */
export type TInitializerConfig = {
  /**
   * Enable automatic database initialization on startup
   */
  autoInitialize: boolean;

  /**
   * Run Prisma migrations during initialization
   */
  runMigrations: boolean;

  /**
   * Load seed data during initialization
   */
  loadSeedData: boolean;

  /**
   * Validate schema compliance after initialization
   */
  validateSchema: boolean;

  /**
   * Migration timeout in milliseconds
   */
  migrationTimeout: number;

  /**
   * Seed data loading timeout in milliseconds
   */
  seedDataTimeout: number;

  /**
   * Retry failed initialization attempts
   */
  retryOnFailure: boolean;

  /**
   * Maximum retry attempts
   */
  maxRetries: number;

  /**
   * Delay between retry attempts in milliseconds
   */
  retryDelay: number;

  /**
   * Force reset database (CAUTION: destroys all data)
   */
  forceReset: boolean;
};
