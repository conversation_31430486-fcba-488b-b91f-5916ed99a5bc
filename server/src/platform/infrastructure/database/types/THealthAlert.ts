// ============================================================================
// HEALTH ALERT TYPE
// ============================================================================

/**
 * Health alert severity levels
 */
export type THealthAlertSeverity = 'critical' | 'high' | 'medium' | 'low' | 'info';

/**
 * Health alert type
 */
export type THealthAlert = {
  /** Unique alert ID */
  alertId: string;

  /** Alert severity */
  severity: THealthAlertSeverity;

  /** Alert category */
  category: 'connection' | 'performance' | 'resource' | 'error';

  /** Alert message */
  message: string;

  /** Alert metric name */
  metric: string;

  /** Current metric value */
  currentValue: number;

  /** Threshold value */
  threshold: number;

  /** Alert timestamp */
  timestamp: Date;

  /** Alert metadata */
  metadata: Record<string, unknown>;

  /** Recommended action */
  recommendedAction?: string;
};

/**
 * Alert thresholds configuration
 */
export type TAlertThresholds = {
  /** Connection pool thresholds */
  connectionPool: {
    maxUtilization: number;
    minIdleConnections: number;
    maxWaitTime: number;
  };

  /** Query performance thresholds */
  queryPerformance: {
    slowQueryThreshold: number;
    maxAverageQueryTime: number;
    maxErrorRate: number;
  };

  /** System resource thresholds */
  systemResources: {
    maxCpuUtilization: number;
    maxMemoryUtilization: number;
    maxDiskUtilization: number;
  };
};
