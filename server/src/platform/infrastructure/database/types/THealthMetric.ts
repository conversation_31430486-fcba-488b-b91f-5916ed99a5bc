// ============================================================================
// HEALTH METRIC TYPE
// ============================================================================

/**
 * Health metric data point
 */
export type THealthMetric = {
  /** Metric name */
  name: string;

  /** Metric value */
  value: number;

  /** Metric unit */
  unit: string;

  /** Metric timestamp */
  timestamp: Date;

  /** Metric threshold */
  threshold?: number;

  /** Is metric healthy */
  isHealthy: boolean;

  /** Metric metadata */
  metadata?: Record<string, unknown>;
};
