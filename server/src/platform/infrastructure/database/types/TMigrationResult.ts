// ============================================================================
// MIGRATION RESULT TYPE
// ============================================================================

/**
 * Migration execution status
 */
export type TMigrationStatus = 'SUCCESS' | 'FAILED' | 'PARTIAL' | 'SKIPPED';

/**
 * Individual migration record
 */
export type TMigrationRecord = {
  /**
   * Migration name
   */
  name: string;

  /**
   * Migration applied successfully
   */
  applied: boolean;

  /**
   * Migration timestamp
   */
  timestamp: Date;

  /**
   * Execution duration in milliseconds
   */
  durationMs: number;

  /**
   * Error message (if failed)
   */
  error: string | null;
};

/**
 * Result of migration execution
 */
export type TMigrationResult = {
  /**
   * Overall migration status
   */
  status: TMigrationStatus;

  /**
   * Total migrations applied
   */
  appliedCount: number;

  /**
   * Total migrations failed
   */
  failedCount: number;

  /**
   * Total migrations skipped
   */
  skippedCount: number;

  /**
   * Migration records
   */
  migrations: TMigrationRecord[];

  /**
   * Total execution duration in milliseconds
   */
  totalDurationMs: number;

  /**
   * Schema version after migrations
   */
  schemaVersion: string;

  /**
   * Errors encountered during migration
   */
  errors: string[];
};
