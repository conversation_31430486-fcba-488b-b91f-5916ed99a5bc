// ============================================================================
// TRANSACTION CONTEXT TYPE
// M1-CORE-DB-02 | platform-database-service-enhanced
// ============================================================================

/**
 * Transaction context for managing database transactions
 */
export interface TTransactionContext {
  /** Unique transaction identifier */
  id: string;

  /** Transaction start time */
  startTime: Date;

  /** Prisma transaction object for internal use */
  prismaTransaction?: any;

  /** Whether transaction is committed */
  isCommitted?: boolean;

  /** Whether transaction is rolled back */
  isRolledBack?: boolean;

  /** Transaction error if any */
  error?: Error;
}

/**
 * Transaction state for monitoring
 */
export interface TTransactionState {
  /** Transaction ID */
  transactionId: string;

  /** Current state: active, committed, or rolled-back */
  state: 'active' | 'committed' | 'rolled-back' | 'failed';

  /** Duration in milliseconds */
  durationMs: number;

  /** Number of operations in transaction */
  operationCount: number;

  /** Start time */
  startTime: Date;

  /** End time if completed */
  endTime?: Date;
}
