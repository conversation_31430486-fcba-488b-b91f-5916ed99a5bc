// ============================================================================
// DATABASE STATE VALIDATION TYPE
// ============================================================================

/**
 * Database state validation status
 */
export type TValidationStatus = 'VALID' | 'INVALID' | 'WARNING' | 'UNKNOWN';

/**
 * Individual validation check result
 */
export type TValidationCheck = {
  /**
   * Check name
   */
  name: string;

  /**
   * Check passed successfully
   */
  passed: boolean;

  /**
   * Check message
   */
  message: string;

  /**
   * Severity level
   */
  severity: 'info' | 'warning' | 'error' | 'critical';

  /**
   * Execution timestamp
   */
  timestamp: Date;
};

/**
 * Database state validation result
 */
export type TDatabaseStateValidation = {
  /**
   * Overall validation status
   */
  status: TValidationStatus;

  /**
   * Database is ready for operations
   */
  isReady: boolean;

  /**
   * Schema version
   */
  schemaVersion: string;

  /**
   * Validation checks performed
   */
  checks: TValidationCheck[];

  /**
   * Number of tables found
   */
  tableCount: number;

  /**
   * Database size in bytes
   */
  databaseSize: number;

  /**
   * Validation timestamp
   */
  timestamp: Date;

  /**
   * Issues found during validation
   */
  issues: string[];

  /**
   * Warnings found during validation
   */
  warnings: string[];
};
