// ============================================================================
// PERFORMANCE METRICS TYPE
// M1-CORE-DB-02 | platform-database-service-enhanced
// ============================================================================

/**
 * Query performance metrics collection
 */
export interface TPerformanceMetrics {
  /** Average query execution time in milliseconds */
  averageQueryTimeMs: number;

  /** Total number of queries executed */
  totalQueries: number;

  /** Number of slow queries (exceeding threshold) */
  slowQueries: number;

  /** Detailed metrics map */
  metrics: Map<string, any>;

  /** Percentile metrics */
  percentiles?: {
    p50: number;  // 50th percentile (median)
    p75: number;  // 75th percentile
    p90: number;  // 90th percentile
    p95: number;  // 95th percentile
    p99: number;  // 99th percentile
  };

  /** Min and max query times */
  range?: {
    min: number;
    max: number;
  };

  /** Collection timestamp */
  timestamp?: Date;
}

/**
 * Detailed operation metrics
 */
export interface TOperationMetrics {
  /** Operation name */
  operation: string;

  /** Total executions */
  executions: number;

  /** Total time spent (milliseconds) */
  totalTimeMs: number;

  /** Average execution time (milliseconds) */
  averageTimeMs: number;

  /** Minimum execution time (milliseconds) */
  minTimeMs: number;

  /** Maximum execution time (milliseconds) */
  maxTimeMs: number;

  /** Number of failures */
  failures: number;

  /** Success rate as percentage */
  successRatePercent: number;
}

/**
 * Timing information from ResilientTimer
 */
export interface TTimingInfo {
  /** Duration in milliseconds */
  duration: number;

  /** Whether timing is reliable */
  reliable: boolean;

  /** The timing method used */
  method: string;

  /** Timestamp */
  timestamp: Date;
}
