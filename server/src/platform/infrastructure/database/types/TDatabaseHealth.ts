// ============================================================================
// DATABASE HEALTH TYPE
// M1-CORE-DB-02 | platform-database-service-enhanced
// ============================================================================

/**
 * Database health status information
 */
export interface TDatabaseHealth {
  /** Whether database is healthy and responsive */
  isHealthy: boolean;

  /** Total connection pool size */
  connectionPoolSize: number;

  /** Number of active connections */
  activeConnections: number;

  /** Number of idle connections */
  idleConnections: number;

  /** Last health check time */
  lastCheckTime: Date;

  /** Health check response time in milliseconds */
  responseTimeMs: number;

  /** Error message if health check failed */
  error?: string;

  /** Additional health metrics */
  metrics?: Record<string, any>;
}

/**
 * Connection pool health details
 */
export interface TConnectionPoolHealth {
  /** Whether pool is accepting new connections */
  isAcceptingConnections: boolean;

  /** Percentage of pool utilization */
  utilizationPercent: number;

  /** Average connection lifetime in milliseconds */
  avgConnectionLifetimeMs: number;

  /** Number of failed connection attempts */
  failedAttempts: number;

  /** Last connection error */
  lastError?: Error;

  /** Pool statistics */
  stats: {
    totalCreated: number;
    totalClosed: number;
    totalTimedOut: number;
  };
}
