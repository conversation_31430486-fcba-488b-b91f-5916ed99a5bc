// ============================================================================
// GOVERNANCE POLICY TYPE
// ============================================================================

import type { TViolationEvent } from './TViolationEvent';

/**
 * Policy enforcement level
 */
export type TPolicyEnforcementLevel = 'ADVISORY' | 'MANDATORY' | 'CRITICAL';

/**
 * Governance policy for database operations
 */
export type TGovernancePolicy = {
  /**
   * Unique policy ID
   */
  policyId: string;

  /**
   * Policy name
   */
  name: string;

  /**
   * Policy description
   */
  description: string;

  /**
   * Enforcement level
   */
  enforcementLevel: TPolicyEnforcementLevel;

  /**
   * Policy rules
   */
  rules: TPolicyRule[];

  /**
   * Effective from date
   */
  effectiveFrom: Date;

  /**
   * Effective until date (optional)
   */
  effectiveUntil?: Date;

  /**
   * Policy enabled
   */
  enabled: boolean;

  /**
   * Policy metadata
   */
  metadata: Record<string, unknown>;
};

/**
 * Policy rule
 */
export type TPolicyRule = {
  /**
   * Rule ID
   */
  ruleId: string;

  /**
   * Rule description
   */
  description: string;

  /**
   * Rule condition (evaluated as expression)
   */
  condition: string;

  /**
   * Action to take if rule violated
   */
  action: 'BLOCK' | 'WARN' | 'LOG' | 'ALERT';

  /**
   * Rule priority (higher = more important)
   */
  priority: number;

  /**
   * Rule enabled
   */
  enabled: boolean;
};

/**
 * Compliance report
 */
export type TComplianceReport = {
  /**
   * Report ID
   */
  reportId: string;

  /**
   * Report generation timestamp
   */
  generatedAt: Date;

  /**
   * Reporting period start
   */
  periodStart: Date;

  /**
   * Reporting period end
   */
  periodEnd: Date;

  /**
   * Overall compliance status
   */
  overallStatus: 'COMPLIANT' | 'NON_COMPLIANT' | 'PARTIAL';

  /**
   * Compliance score (0-100)
   */
  complianceScore: number;

  /**
   * Total operations tracked
   */
  totalOperations: number;

  /**
   * Compliant operations
   */
  compliantOperations: number;

  /**
   * Non-compliant operations
   */
  nonCompliantOperations: number;

  /**
   * Violations detected
   */
  violations: TViolationEvent[];

  /**
   * Recommendations
   */
  recommendations: string[];

  /**
   * Report metadata
   */
  metadata: Record<string, unknown>;
};
