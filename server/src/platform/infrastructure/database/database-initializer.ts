// ============================================================================
// OA FRAMEWORK HEADER V2.3 - DatabaseInitializer Implementation
// Task: M1-CORE-DB-04 | Component: platform-database-initializer
// ============================================================================
// @file DatabaseInitializer Implementation
// @filepath server/src/platform/infrastructure/database/database-initializer.ts
// @component platform-database-initializer
// @milestone M1-CORE-DB-04
// @purpose Database initialization, migration management, and seed data loading
//
// FRAMEWORK: Manages database initialization lifecycle
// - Connection verification and validation
// - Prisma migration execution
// - Schema compliance validation
// - Seed data loading
// - OA configuration data setup
// - Initialization state tracking
//
// AUTHORITY: President & CEO, E.Z. Consultancy
// COMPLIANCE: Rules 01-07, 09 (OA Header V2.3, memory safety, anti-simplification)
// INTEGRATION: M0 governance, M0.3 logging, M0A authority, M1-CORE-DB-02/03
// VERSION: 1.0.0 (2026-02-06)

// ============================================================================
// SECTION 1: IMPORTS & DEPENDENCIES
// ============================================================================

import { BaseTrackingService } from '../../tracking/core-data/base/BaseTrackingService';
import { exec } from 'child_process';
import { promisify } from 'util';
import { AuditLoggingConfigurationService } from '../../logging/configuration/AuditLoggingConfigurationService';
import type { IAuthorityEnforcementEngine } from '../../governance/runtime/compliance/interfaces/IAuthorityEnforcementEngine';
import type {
  IInitializer,
  ISetupService,
  IDatabaseService,
} from './interfaces/index';
import type {
  TInitializerConfig,
  TInitializationState,
  TInitializationStatus,
  TMigrationResult,
  TMigrationStatus,
  TMigrationRecord,
  TDatabaseStateValidation,
  TValidationStatus,
  TValidationCheck,
} from './types/index';
import type { TTrackingData } from '../../../../../shared/src/types/platform/tracking/core/tracking-data-types';
import type { TValidationResult } from '../../../../../shared/src/types/platform/tracking/core/tracking-data-types';
import {
  COMPONENT_ID,
  SERVICE_NAME,
  DEFAULT_AUTO_INITIALIZE,
  DEFAULT_RUN_MIGRATIONS,
  DEFAULT_LOAD_SEED_DATA,
  DEFAULT_VALIDATE_SCHEMA,
  DEFAULT_RETRY_ON_FAILURE,
  DEFAULT_MAX_RETRIES,
  DEFAULT_RETRY_DELAY,
  DEFAULT_FORCE_RESET,
  DEFAULT_MIGRATION_TIMEOUT,
  DEFAULT_SEED_DATA_TIMEOUT,
  DEFAULT_CONNECTION_TIMEOUT,
  LOG_CATEGORY_INITIALIZATION,
  LOG_CATEGORY_MIGRATION,
  LOG_CATEGORY_SEED,
  LOG_CATEGORY_VALIDATION,
  LOG_CATEGORY_ERROR,
  PRISMA_MIGRATE_DEPLOY,
  VALIDATION_CHECK_CONNECTION,
  VALIDATION_CHECK_SCHEMA,
  VALIDATION_CHECK_TABLES,
  VALIDATION_CHECK_MIGRATIONS,
  SCHEMA_VERSION_UNKNOWN,
  OA_CONFIG_TABLE,
  OA_CONFIG_VERSION_KEY,
  OA_CONFIG_SCHEMA_VERSION_KEY,
  OA_CONFIG_INITIALIZED_AT_KEY,
} from './constants/database-initializer-constants';

const execAsync = promisify(exec);

// ============================================================================
// SECTION 2: DATABASEINITIALIZER CLASS
// ============================================================================

/**
 * Database initialization service managing startup lifecycle,
 * migrations, schema validation, and seed data loading
 *
 * Extends BaseTrackingService for governance tracking and
 * implements IInitializer and ISetupService interfaces
 */
export class DatabaseInitializer
  extends BaseTrackingService
  implements IInitializer, ISetupService
{
  // ============================================================================
  // PRIVATE FIELDS
  // ============================================================================

  /** Database service for operations */
  private _databaseService!: IDatabaseService;

  /** Audit logging service */
  private _auditLogger!: AuditLoggingConfigurationService;

  /** Authority enforcement engine */
  private _authorityEngine!: IAuthorityEnforcementEngine;

  /** Initializer configuration */
  private readonly _initializerConfig: TInitializerConfig;

  /** Current initialization state */
  private _initializationState: TInitializationState;

  /** Component ID for logging */
  private readonly _componentId = COMPONENT_ID;

  // ============================================================================
  // CONSTRUCTOR
  // ============================================================================

  constructor(
    config: Partial<TInitializerConfig>,
    databaseService: IDatabaseService,
    authorityEngine: IAuthorityEnforcementEngine,
    auditLogger: AuditLoggingConfigurationService
  ) {
    super();

    this._initializerConfig = {
      autoInitialize: config.autoInitialize ?? DEFAULT_AUTO_INITIALIZE,
      runMigrations: config.runMigrations ?? DEFAULT_RUN_MIGRATIONS,
      loadSeedData: config.loadSeedData ?? DEFAULT_LOAD_SEED_DATA,
      validateSchema: config.validateSchema ?? DEFAULT_VALIDATE_SCHEMA,
      migrationTimeout: config.migrationTimeout ?? DEFAULT_MIGRATION_TIMEOUT,
      seedDataTimeout: config.seedDataTimeout ?? DEFAULT_SEED_DATA_TIMEOUT,
      retryOnFailure: config.retryOnFailure ?? DEFAULT_RETRY_ON_FAILURE,
      maxRetries: config.maxRetries ?? DEFAULT_MAX_RETRIES,
      retryDelay: config.retryDelay ?? DEFAULT_RETRY_DELAY,
      forceReset: config.forceReset ?? DEFAULT_FORCE_RESET,
    };

    this._databaseService = databaseService;
    this._authorityEngine = authorityEngine;
    this._auditLogger = auditLogger;

    this._initializationState = this._createInitialState();
  }

  // ============================================================================
  // ABSTRACT METHODS (Required by BaseTrackingService)
  // ============================================================================

  protected getServiceName(): string {
    return SERVICE_NAME;
  }

  protected getServiceVersion(): string {
    return '1.0.0';
  }

  protected async doTrack(data: TTrackingData): Promise<void> {
    // Track initialization operations
    await this._logInternalOperation(
      LOG_CATEGORY_INITIALIZATION,
      {
        event: 'track',
        data,
      },
      'debug'
    );
  }

  protected async doValidate(): Promise<TValidationResult> {
    const state = await this.checkDatabaseState();
    return {
      validationId: `db_init_${Date.now()}`,
      componentId: COMPONENT_ID,
      timestamp: new Date(),
      executionTime: 0,
      status: state.isReady ? 'valid' : 'invalid',
      overallScore: state.isReady ? 100 : 0,
      checks: state.checks,
      references: {
        componentId: COMPONENT_ID,
        internalReferences: [],
        externalReferences: [],
        circularReferences: [],
        missingReferences: [],
        redundantReferences: [],
        metadata: {
          totalReferences: 0,
          buildTimestamp: new Date(),
          analysisDepth: 0,
        },
      },
      recommendations: [],
      warnings: state.warnings,
      errors: state.issues,
      metadata: {
        validationMethod: 'database_state_check',
        rulesApplied: state.checks.length,
        dependencyDepth: 0,
        cyclicDependencies: [],
        orphanReferences: [],
        schemaVersion: state.schemaVersion,
        tableCount: state.tableCount,
        timestamp: state.timestamp.toISOString(),
      },
      isValid: state.isReady,
    };
  }

  // ============================================================================
  // LIFECYCLE METHODS (BaseTrackingService)
  // ============================================================================

  protected async doInitialize(): Promise<void> {
    await super.doInitialize();

    await this._logInternalOperation(
      LOG_CATEGORY_INITIALIZATION,
      {
        event: 'initializer.start',
        config: this._initializerConfig,
      },
      'info'
    );

    if (this._initializerConfig.autoInitialize) {
      await this.initializeDatabase();
    }
  }

  protected async doShutdown(): Promise<void> {
    await this._logInternalOperation(
      LOG_CATEGORY_INITIALIZATION,
      {
        event: 'initializer.shutdown',
        state: this._initializationState.status,
      },
      'info'
    );

    await super.doShutdown();
  }

  // ============================================================================
  // IINITIALIZER INTERFACE METHODS
  // ============================================================================

  async initializeDatabase(): Promise<void> {
    this._initializationState.status = 'IN_PROGRESS';
    this._initializationState.startTime = new Date();

    try {
      // Step 1: Verify connection
      await this._updateStatus('VALIDATING');
      const connected = await this.verifyDatabaseConnection();
      if (!connected) {
        throw new Error('Database connection failed');
      }
      this._initializationState.connectionVerified = true;
      this._initializationState.stepsCompleted.push('connection_verified');

      // Step 2: Run migrations
      if (this._initializerConfig.runMigrations) {
        await this._updateStatus('MIGRATING');
        const migrationResult = await this.runMigrations();
        this._initializationState.migrationsExecuted =
          migrationResult.status === 'SUCCESS';
        this._initializationState.stepsCompleted.push('migrations_executed');
      }

      // Step 3: Validate schema
      if (this._initializerConfig.validateSchema) {
        await this._updateStatus('VALIDATING');
        const schemaValid = await this.validateSchema();
        this._initializationState.schemaValidated = schemaValid;
        this._initializationState.stepsCompleted.push('schema_validated');
      }

      // Step 4: Load seed data
      if (this._initializerConfig.loadSeedData) {
        await this._updateStatus('SEEDING');
        await this.loadSeedData();
        this._initializationState.seedDataLoaded = true;
        this._initializationState.stepsCompleted.push('seed_data_loaded');
      }

      // Step 5: Load OA configuration
      await this.loadOAConfigurationData();
      this._initializationState.oaConfigurationLoaded = true;
      this._initializationState.stepsCompleted.push('oa_configuration_loaded');

      // Complete
      this._initializationState.status = 'COMPLETE';
      this._initializationState.completionTime = new Date();
      this._initializationState.durationMs =
        this._initializationState.completionTime.getTime() -
        (this._initializationState.startTime?.getTime() || 0);

      await this.reportInitializationStatus();
      await this._logInternalOperation(
        LOG_CATEGORY_INITIALIZATION,
        {
          event: 'initialization.complete',
          durationMs: this._initializationState.durationMs,
          stepsCompleted: this._initializationState.stepsCompleted,
        },
        'info'
      );
    } catch (error) {
      this._initializationState.status = 'FAILED';
      this._initializationState.lastError =
        error instanceof Error ? error.message : String(error);
      this._initializationState.completionTime = new Date();

      await this._logInternalOperation(
        LOG_CATEGORY_ERROR,
        {
          event: 'initialization.failed',
          error: this._initializationState.lastError,
          retryAttempts: this._initializationState.retryAttempts,
        },
        'error'
      );

      throw error;
    }
  }

  async verifyDatabaseConnection(): Promise<boolean> {
    try {
      await this._logInternalOperation(
        LOG_CATEGORY_VALIDATION,
        { event: 'connection.verify.start' },
        'debug'
      );

      const health = await this._databaseService.getConnectionHealth();

      await this._logInternalOperation(
        LOG_CATEGORY_VALIDATION,
        {
          event: 'connection.verify.complete',
          isHealthy: health.isHealthy,
        },
        'info'
      );

      return health.isHealthy;
    } catch (error) {
      await this._logInternalOperation(
        LOG_CATEGORY_ERROR,
        {
          event: 'connection.verify.failed',
          error: error instanceof Error ? error.message : String(error),
        },
        'error'
      );
      return false;
    }
  }

  async runMigrations(): Promise<TMigrationResult> {
    const startTime = Date.now();
    const migrations: TMigrationRecord[] = [];
    const errors: string[] = [];

    try {
      await this._logInternalOperation(
        LOG_CATEGORY_MIGRATION,
        { event: 'migration.start' },
        'info'
      );

      // Execute Prisma migrate deploy
      const { stdout, stderr } = await execAsync(PRISMA_MIGRATE_DEPLOY, {
        timeout: this._initializerConfig.migrationTimeout,
        cwd: process.cwd(),
      });

      if (stderr) {
        errors.push(stderr);
      }

      const migrationRecord: TMigrationRecord = {
        name: 'prisma_migrate_deploy',
        applied: !stderr,
        timestamp: new Date(),
        durationMs: Date.now() - startTime,
        error: stderr || null,
      };

      migrations.push(migrationRecord);

      await this._logInternalOperation(
        LOG_CATEGORY_MIGRATION,
        {
          event: 'migration.complete',
          stdout,
          stderr,
        },
        'info'
      );

      return {
        status: stderr ? 'FAILED' : 'SUCCESS',
        appliedCount: stderr ? 0 : 1,
        failedCount: stderr ? 1 : 0,
        skippedCount: 0,
        migrations,
        totalDurationMs: Date.now() - startTime,
        schemaVersion: SCHEMA_VERSION_UNKNOWN,
        errors,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      errors.push(errorMessage);

      await this._logInternalOperation(
        LOG_CATEGORY_ERROR,
        {
          event: 'migration.failed',
          error: errorMessage,
        },
        'error'
      );

      return {
        status: 'FAILED',
        appliedCount: 0,
        failedCount: 1,
        skippedCount: 0,
        migrations,
        totalDurationMs: Date.now() - startTime,
        schemaVersion: SCHEMA_VERSION_UNKNOWN,
        errors,
      };
    }
  }

  async validateSchema(): Promise<boolean> {
    try {
      await this._logInternalOperation(
        LOG_CATEGORY_VALIDATION,
        { event: 'schema.validate.start' },
        'debug'
      );

      // Validate by attempting a simple query
      await this._databaseService.executeRawQuery('SELECT 1');

      await this._logInternalOperation(
        LOG_CATEGORY_VALIDATION,
        { event: 'schema.validate.complete' },
        'info'
      );

      return true;
    } catch (error) {
      await this._logInternalOperation(
        LOG_CATEGORY_ERROR,
        {
          event: 'schema.validate.failed',
          error: error instanceof Error ? error.message : String(error),
        },
        'error'
      );
      return false;
    }
  }

  async loadSeedData(): Promise<void> {
    try {
      await this._logInternalOperation(
        LOG_CATEGORY_SEED,
        { event: 'seed_data.load.start' },
        'info'
      );

      // Seed data loading would be implemented here
      // For now, this is a placeholder for future seed data

      await this._logInternalOperation(
        LOG_CATEGORY_SEED,
        { event: 'seed_data.load.complete' },
        'info'
      );
    } catch (error) {
      await this._logInternalOperation(
        LOG_CATEGORY_ERROR,
        {
          event: 'seed_data.load.failed',
          error: error instanceof Error ? error.message : String(error),
        },
        'error'
      );
      throw error;
    }
  }

  async loadOAConfigurationData(): Promise<void> {
    try {
      await this._logInternalOperation(
        LOG_CATEGORY_SEED,
        { event: 'oa_config.load.start' },
        'info'
      );

      // Load OA framework configuration
      // This would typically involve creating OA configuration records
      // For now, placeholder implementation

      await this._logInternalOperation(
        LOG_CATEGORY_SEED,
        {
          event: 'oa_config.load.complete',
          timestamp: new Date(),
        },
        'info'
      );
    } catch (error) {
      await this._logInternalOperation(
        LOG_CATEGORY_ERROR,
        {
          event: 'oa_config.load.failed',
          error: error instanceof Error ? error.message : String(error),
        },
        'error'
      );
      throw error;
    }
  }

  async checkDatabaseState(): Promise<TDatabaseStateValidation> {
    const checks: TValidationCheck[] = [];
    const issues: string[] = [];
    const warnings: string[] = [];

    try {
      // Check connection
      const connected = await this.verifyDatabaseConnection();
      checks.push({
        name: VALIDATION_CHECK_CONNECTION,
        passed: connected,
        message: connected
          ? 'Database connection verified'
          : 'Database connection failed',
        severity: connected ? 'info' : 'error',
        timestamp: new Date(),
      });

      if (!connected) {
        issues.push('Database connection failed');
      }

      // Check schema
      const schemaValid = await this.validateSchema();
      checks.push({
        name: VALIDATION_CHECK_SCHEMA,
        passed: schemaValid,
        message: schemaValid ? 'Schema is valid' : 'Schema validation failed',
        severity: schemaValid ? 'info' : 'error',
        timestamp: new Date(),
      });

      if (!schemaValid) {
        issues.push('Schema validation failed');
      }

      return {
        status: issues.length === 0 ? 'VALID' : 'INVALID',
        isReady: connected && schemaValid,
        schemaVersion: SCHEMA_VERSION_UNKNOWN,
        checks,
        tableCount: 0,
        databaseSize: 0,
        timestamp: new Date(),
        issues,
        warnings,
      };
    } catch (error) {
      issues.push(error instanceof Error ? error.message : String(error));

      return {
        status: 'INVALID',
        isReady: false,
        schemaVersion: SCHEMA_VERSION_UNKNOWN,
        checks,
        tableCount: 0,
        databaseSize: 0,
        timestamp: new Date(),
        issues,
        warnings,
      };
    }
  }

  async reportInitializationStatus(): Promise<void> {
    try {
      await this._logInternalOperation(
        LOG_CATEGORY_INITIALIZATION,
        {
          event: 'initialization.status.report',
          state: this._initializationState,
        },
        'info'
      );
    } catch (error) {
      await this._logInternalOperation(
        LOG_CATEGORY_ERROR,
        {
          event: 'initialization.status.report.failed',
          error: error instanceof Error ? error.message : String(error),
        },
        'error'
      );
    }
  }

  getInitializationState(): TInitializationState {
    return { ...this._initializationState };
  }

  // ============================================================================
  // ISETUPSERVICE INTERFACE METHODS
  // ============================================================================

  async setup(): Promise<void> {
    await this.initialize();
  }

  async teardown(): Promise<void> {
    await this.shutdown();
  }

  isServiceReady(): boolean {
    return (
      this._initializationState.status === 'COMPLETE' &&
      this._initializationState.connectionVerified &&
      this._initializationState.schemaValidated
    );
  }

  async reset(): Promise<void> {
    this._initializationState = this._createInitialState();
    await this._logInternalOperation(
      LOG_CATEGORY_INITIALIZATION,
      { event: 'initializer.reset' },
      'info'
    );
  }

  getServiceStatus(): string {
    return this._initializationState.status;
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  private async _logInternalOperation(
    eventCategory: string,
    data: Record<string, any>,
    logLevel: 'trace' | 'debug' | 'info' | 'warn' | 'error' | 'critical' = 'info'
  ): Promise<void> {
    try {
      const shouldLog = await this._auditLogger.shouldLog(
        this._componentId,
        eventCategory,
        logLevel
      );

      if (shouldLog) {
        console.log(
          JSON.stringify({
            component: this._componentId,
            category: eventCategory,
            level: logLevel,
            timestamp: new Date().toISOString(),
            data,
          })
        );
      }
    } catch (error) {
      console.error('Logging error:', error);
    }
  }

  private async _updateStatus(status: TInitializationStatus): Promise<void> {
    this._initializationState.status = status;
    await this._logInternalOperation(
      LOG_CATEGORY_INITIALIZATION,
      {
        event: 'status.update',
        status,
      },
      'debug'
    );
  }

  private _createInitialState(): TInitializationState {
    return {
      status: 'NOT_STARTED',
      startTime: null,
      completionTime: null,
      connectionVerified: false,
      migrationsExecuted: false,
      schemaValidated: false,
      seedDataLoaded: false,
      oaConfigurationLoaded: false,
      retryAttempts: 0,
      lastError: null,
      stepsCompleted: [],
      durationMs: 0,
    };
  }
}
