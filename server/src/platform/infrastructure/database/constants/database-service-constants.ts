// ============================================================================
// OA FRAMEWORK HEADER V2.3 - Database Service Constants
// Task: M1-CORE-DB-02 | Component: platform-database-service-enhanced
// ============================================================================
// @file Database Service Constants
// @filepath server/src/platform/infrastructure/database/constants/database-service-constants.ts
// @component platform-database-service-enhanced
// @milestone M1-CORE-DB-02
// @library Prisma Client v5.x (@prisma/client)
// @purpose Configuration constants for DatabaseServiceEnhanced with governance targets
//
// Performance targets, limits, and timeouts for enterprise database operations
//
// AUTHORITY: President & CEO, E.Z. Consultancy
// COMPLIANCE: Rules 01-07, 09 (OA Header V2.3, memory safety, anti-simplification)
// INTEGRATION: M0 governance, M0.3 logging, M00.2 gateway, M0A authority
// PERFORMANCE: Query target <10ms per Rule 03
// VERSION: 1.0.0 (2026-02-06)

// ============================================================================
// QUERY EXECUTION CONSTANTS
// ============================================================================

/** Maximum query execution timeout in milliseconds */
export const MAX_QUERY_TIMEOUT_MS = 10000;

/** Default database connection timeout */
export const DEFAULT_CONNECTION_TIMEOUT = 5000;

/** Query performance threshold for warning logs (milliseconds) */
export const QUERY_PERFORMANCE_THRESHOLD = 10;

/** Batch operation size for bulk operations */
export const BATCH_OPERATION_SIZE = 1000;

// ============================================================================
// COMPONENT IDENTIFICATION
// ============================================================================

/** Component ID for audit logging and governance tracking */
export const COMPONENT_ID = 'database-service-enhanced';

/** Service name for display and logging */
export const SERVICE_NAME = 'Database Service Enhanced';

// ============================================================================
// RESOURCE LIMITS
// ============================================================================

/** Maximum safe intervals (per MemorySafeResourceManager) */
export const MAX_SAFE_INTERVALS = 5;

/** Maximum safe timeouts (per MemorySafeResourceManager) */
export const MAX_SAFE_TIMEOUTS = 10;

/** Maximum cache size in bytes (10MB) */
export const MAX_CACHE_SIZE_BYTES = 10 * 1024 * 1024;

/** Maximum concurrent connections */
export const MAX_CONNECTIONS = 50;

/** Memory threshold in MB before warnings */
export const MEMORY_THRESHOLD_MB = 50;

/** Resource cleanup interval in milliseconds (1 minute) */
export const CLEANUP_INTERVAL_MS = 60000;

// ============================================================================
// TIMING CONFIGURATION
// ============================================================================

/** Maximum expected query duration (ms) for ResilientTimer */
export const RESILIENT_TIMER_MAX_DURATION = 10000;

/** Unreliable threshold for ResilientTimer */
export const RESILIENT_TIMER_UNRELIABLE_THRESHOLD = 3;

/** Baseline estimate for ResilientTimer (ms) */
export const RESILIENT_TIMER_BASELINE = 5;

/** Metrics age before expiration (5 minutes) */
export const METRICS_MAX_AGE_MS = 300000;

// ============================================================================
// OPERATION TYPE DEFAULTS
// ============================================================================

/** Default estimated duration for executeQuery (ms) */
export const ESTIMATE_EXECUTE_QUERY = 10;

/** Default estimated duration for executeRawQuery (ms) */
export const ESTIMATE_EXECUTE_RAW_QUERY = 10;

/** Default estimated duration for find (ms) */
export const ESTIMATE_FIND = 10;

/** Default estimated duration for findUnique (ms) */
export const ESTIMATE_FIND_UNIQUE = 5;

/** Default estimated duration for create (ms) */
export const ESTIMATE_CREATE = 10;

/** Default estimated duration for update (ms) */
export const ESTIMATE_UPDATE = 10;

/** Default estimated duration for delete (ms) */
export const ESTIMATE_DELETE = 10;

/** Default estimated duration for beginTransaction (ms) */
export const ESTIMATE_BEGIN_TRANSACTION = 5;

/** Default estimated duration for commit (ms) */
export const ESTIMATE_COMMIT = 5;

/** Default estimated duration for rollback (ms) */
export const ESTIMATE_ROLLBACK = 5;

// ============================================================================
// EVENT CATEGORIES FOR M0.3 LOGGING
// ============================================================================

/** Event category for database connections */
export const LOG_CATEGORY_CONNECTION = 'database.connection';

/** Event category for database queries */
export const LOG_CATEGORY_QUERY = 'database.query';

/** Event category for database transactions */
export const LOG_CATEGORY_TRANSACTION = 'database.transaction';

/** Event category for database errors */
export const LOG_CATEGORY_ERROR = 'database.error';

// ============================================================================
// PRISMA CLIENT OPTIONS
// ============================================================================

/** Prisma logging events to capture */
export const PRISMA_LOG_EVENTS = ['query', 'info', 'warn', 'error'] as const;

/** Prisma error format for display */
export const PRISMA_ERROR_FORMAT = 'pretty' as const;

// ============================================================================
// OPERATION ID PREFIXES
// ============================================================================

/** Prefix for database operation IDs */
export const OPERATION_ID_PREFIX = 'db_op_';

/** Prefix for transaction IDs */
export const TRANSACTION_ID_PREFIX = 'db_tx_';
