// ============================================================================
// DATABASE HEALTH MONITOR CONSTANTS
// ============================================================================

// ============================================================================
// COMPONENT IDENTIFICATION
// ============================================================================

export const COMPONENT_ID = 'database-health-monitor-enhanced';
export const SERVICE_NAME = 'DatabaseHealthMonitorEnhanced';

// ============================================================================
// MEMORY SAFETY CONFIGURATION
// ============================================================================

export const MAX_SAFE_INTERVALS = 5;
export const MAX_SAFE_TIMEOUTS = 10;
export const MAX_CACHE_SIZE_BYTES = 10 * 1024 * 1024; // 10MB
export const MAX_CONNECTIONS = 50;
export const MEMORY_THRESHOLD_MB = 50;
export const CLEANUP_INTERVAL_MS = 60000; // 1 minute

// ============================================================================
// RESILIENT TIMING CONFIGURATION
// ============================================================================

export const RESILIENT_TIMER_MAX_DURATION = 10000; // 10 seconds
export const RESILIENT_TIMER_UNRELIABLE_THRESHOLD = 3;
export const RESILIENT_TIMER_BASELINE = 5;

// ============================================================================
// METRICS COLLECTOR CONFIGURATION
// ============================================================================

export const METRICS_MAX_AGE_MS = 300000; // 5 minutes
export const ESTIMATE_CHECK_HEALTH = 5; // ms
export const ESTIMATE_GET_POOL_METRICS = 5; // ms
export const ESTIMATE_GET_QUERY_METRICS = 10; // ms
export const ESTIMATE_COLLECT_SYSTEM_METRICS = 10; // ms
export const ESTIMATE_CHECK_ALERTS = 5; // ms
export const ESTIMATE_TRIGGER_ALERT = 10; // ms
export const ESTIMATE_MONITOR_CONNECTION_POOL = 5; // ms
export const ESTIMATE_MONITOR_QUERY_PERFORMANCE = 5; // ms
export const ESTIMATE_REPORT_HEALTH_STATUS = 10; // ms

// ============================================================================
// LOGGING CATEGORIES
// ============================================================================

export const LOG_CATEGORY_HEALTH = 'database.health';
export const LOG_CATEGORY_MONITORING = 'database.monitoring';
export const LOG_CATEGORY_ALERT = 'database.alert';
export const LOG_CATEGORY_ERROR = 'database.error';

// ============================================================================
// HEALTH CHECK CONFIGURATION
// ============================================================================

export const DEFAULT_CHECK_INTERVAL = 30000; // 30 seconds
export const DEFAULT_METRICS_INTERVAL = 60000; // 1 minute
export const DEFAULT_ALERT_CHECK_INTERVAL = 10000; // 10 seconds

// ============================================================================
// CONNECTION POOL THRESHOLDS
// ============================================================================

export const DEFAULT_MAX_POOL_UTILIZATION = 0.8; // 80%
export const DEFAULT_MIN_IDLE_CONNECTIONS = 5;
export const DEFAULT_MAX_WAIT_TIME = 5000; // 5 seconds

// ============================================================================
// QUERY PERFORMANCE THRESHOLDS
// ============================================================================

export const DEFAULT_SLOW_QUERY_THRESHOLD = 1000; // 1 second
export const DEFAULT_MAX_AVERAGE_QUERY_TIME = 100; // 100ms
export const DEFAULT_MAX_ERROR_RATE = 0.05; // 5%

// ============================================================================
// SYSTEM RESOURCE THRESHOLDS
// ============================================================================

export const DEFAULT_MAX_CPU_UTILIZATION = 0.8; // 80%
export const DEFAULT_MAX_MEMORY_UTILIZATION = 0.85; // 85%
export const DEFAULT_MAX_DISK_UTILIZATION = 0.9; // 90%

// ============================================================================
// ALERT CONFIGURATION
// ============================================================================

export const DEFAULT_ALERT_COOLDOWN_PERIOD = 300000; // 5 minutes
export const DEFAULT_MAX_ALERTS_PER_HOUR = 10;

// ============================================================================
// HISTORY CONFIGURATION
// ============================================================================

export const DEFAULT_MAX_HEALTH_EVENTS_HISTORY = 1000;
export const DEFAULT_METRICS_RETENTION_PERIOD = 86400000; // 24 hours

// ============================================================================
// ID PREFIXES
// ============================================================================

export const ALERT_ID_PREFIX = 'health_alert_';
export const METRIC_ID_PREFIX = 'health_metric_';

// ============================================================================
// PERFORMANCE TARGETS
// ============================================================================

export const PERFORMANCE_TARGET_HEALTH_CHECK = 5; // ms
export const PERFORMANCE_TARGET_METRICS_COLLECTION = 10; // ms
export const PERFORMANCE_TARGET_ALERT_EVALUATION = 5; // ms
