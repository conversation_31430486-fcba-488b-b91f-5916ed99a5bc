// ============================================================================
// DATABASE INITIALIZER CONSTANTS
// ============================================================================

// ============================================================================
// COMPONENT IDENTIFICATION
// ============================================================================

export const COMPONENT_ID = 'database-initializer';
export const SERVICE_NAME = 'DatabaseInitializer';

// ============================================================================
// INITIALIZATION DEFAULTS
// ============================================================================

export const DEFAULT_AUTO_INITIALIZE = true;
export const DEFAULT_RUN_MIGRATIONS = true;
export const DEFAULT_LOAD_SEED_DATA = false;
export const DEFAULT_VALIDATE_SCHEMA = true;
export const DEFAULT_RETRY_ON_FAILURE = true;
export const DEFAULT_MAX_RETRIES = 3;
export const DEFAULT_RETRY_DELAY = 5000; // 5 seconds
export const DEFAULT_FORCE_RESET = false;

// ============================================================================
// TIMEOUT CONFIGURATION
// ============================================================================

export const DEFAULT_MIGRATION_TIMEOUT = 300000; // 5 minutes
export const DEFAULT_SEED_DATA_TIMEOUT = 60000; // 1 minute
export const DEFAULT_CONNECTION_TIMEOUT = 10000; // 10 seconds
export const DEFAULT_VALIDATION_TIMEOUT = 30000; // 30 seconds

// ============================================================================
// LOGGING CATEGORIES
// ============================================================================

export const LOG_CATEGORY_INITIALIZATION = 'database.initialization';
export const LOG_CATEGORY_MIGRATION = 'database.migration';
export const LOG_CATEGORY_SEED = 'database.seed';
export const LOG_CATEGORY_VALIDATION = 'database.validation';
export const LOG_CATEGORY_ERROR = 'database.error';

// ============================================================================
// PRISMA COMMANDS
// ============================================================================

export const PRISMA_MIGRATE_DEPLOY = 'prisma migrate deploy';
export const PRISMA_MIGRATE_STATUS = 'prisma migrate status';
export const PRISMA_DB_PUSH = 'prisma db push';
export const PRISMA_GENERATE = 'prisma generate';

// ============================================================================
// VALIDATION CHECKS
// ============================================================================

export const VALIDATION_CHECK_CONNECTION = 'connection';
export const VALIDATION_CHECK_SCHEMA = 'schema';
export const VALIDATION_CHECK_TABLES = 'tables';
export const VALIDATION_CHECK_MIGRATIONS = 'migrations';
export const VALIDATION_CHECK_SEED_DATA = 'seed_data';

// ============================================================================
// SCHEMA VERSION
// ============================================================================

export const SCHEMA_VERSION_UNKNOWN = 'unknown';
export const SCHEMA_VERSION_PREFIX = 'v';

// ============================================================================
// SEED DATA CONFIGURATION
// ============================================================================

export const SEED_DATA_BATCH_SIZE = 100;
export const SEED_DATA_MAX_RETRIES = 3;
export const SEED_DATA_RETRY_DELAY = 1000; // 1 second

// ============================================================================
// OA CONFIGURATION DATA
// ============================================================================

export const OA_CONFIG_TABLE = 'OAConfiguration';
export const OA_CONFIG_VERSION_KEY = 'oa_framework_version';
export const OA_CONFIG_SCHEMA_VERSION_KEY = 'oa_schema_version';
export const OA_CONFIG_INITIALIZED_AT_KEY = 'oa_initialized_at';
