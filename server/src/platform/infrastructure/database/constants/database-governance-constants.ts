// ============================================================================
// DATABASE GOVERNANCE TRACKER CONSTANTS
// ============================================================================

// ============================================================================
// COMPONENT IDENTIFICATION
// ============================================================================

export const COMPONENT_ID = 'database-governance-tracker';
export const SERVICE_NAME = 'DatabaseGovernanceTracker';

// ============================================================================
// LOGGING CATEGORIES
// ============================================================================

export const LOG_CATEGORY_GOVERNANCE = 'database.governance';
export const LOG_CATEGORY_COMPLIANCE = 'database.compliance';
export const LOG_CATEGORY_VIOLATION = 'database.violation';
export const LOG_CATEGORY_AUTHORITY = 'database.authority';
export const LOG_CATEGORY_POLICY = 'database.policy';
export const LOG_CATEGORY_ERROR = 'database.governance.error';

// ============================================================================
// COMPLIANCE THRESHOLDS
// ============================================================================

export const DEFAULT_COMPLIANCE_THRESHOLD = 95; // 95% compliance required
export const CRITICAL_VIOLATION_THRESHOLD = 5; // Max critical violations
export const HIGH_VIOLATION_THRESHOLD = 10; // Max high violations
export const COMPLIANCE_CHECK_INTERVAL = 300000; // 5 minutes

// ============================================================================
// AUTHORITY LEVELS
// ============================================================================

export const AUTHORITY_READ = 'READ';
export const AUTHORITY_WRITE = 'WRITE';
export const AUTHORITY_DELETE = 'DELETE';
export const AUTHORITY_ADMIN = 'ADMIN';
export const AUTHORITY_SCHEMA_CHANGE = 'SCHEMA_CHANGE';

// ============================================================================
// VIOLATION SEVERITY WEIGHTS
// ============================================================================

export const SEVERITY_WEIGHT_LOW = 1;
export const SEVERITY_WEIGHT_MEDIUM = 5;
export const SEVERITY_WEIGHT_HIGH = 10;
export const SEVERITY_WEIGHT_CRITICAL = 25;

// ============================================================================
// TRACKING CONFIGURATION
// ============================================================================

export const MAX_VIOLATION_HISTORY = 1000;
export const MAX_OPERATION_HISTORY = 5000;
export const VIOLATION_RETENTION_PERIOD = 2592000000; // 30 days in ms
export const OPERATION_RETENTION_PERIOD = 604800000; // 7 days in ms

// ============================================================================
// POLICY DEFAULTS
// ============================================================================

export const DEFAULT_POLICY_ENFORCEMENT_LEVEL = 'MANDATORY';
export const DEFAULT_POLICY_ENABLED = true;

// ============================================================================
// ID PREFIXES
// ============================================================================

export const VIOLATION_ID_PREFIX = 'viol_';
export const TRACKING_ID_PREFIX = 'track_';
export const REPORT_ID_PREFIX = 'report_';
export const POLICY_ID_PREFIX = 'policy_';
