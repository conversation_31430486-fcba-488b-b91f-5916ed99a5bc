// ============================================================================
// OA FRAMEWORK HEADER V2.3 - Prisma Schema Definition
// Task: M1-CORE-DB-01 | Component: platform-prisma-schema
// ============================================================================
// @file Prisma Schema Definition
// @filepath prisma/schema.prisma
// @component platform-prisma-schema
// @milestone M1-CORE-DB-01
// @library Prisma ORM v5.x (@prisma/client)
// @purpose Database schema for M1 infrastructure with OA configuration isolation
//
// FRAMEWORK: 24 enterprise-grade models supporting M1, M1A, M1B, M1C
// - OA Configuration: Framework isolation from business data
// - Audit & Governance: Compliance tracking for M0.3/M0A integration
// - Database Infrastructure: M1 operations
// - Configuration Management: Zod validation support
// - Security Foundation: Policies and encryption keys
// - Business Data Foundation: M1A/M1B/M1C support
//
// AUTHORITY: President & CEO, E.Z. Consultancy
// COMPLIANCE: Rules 01-07, 09 (OA Header V2.3, memory safety, anti-simplification)
// INTEGRATION: M0 governance, M0.3 logging, M00.2 gateway, M0A authority
// PERFORMANCE: Query target <10ms via 34 strategic indexes
// VERSION: 1.0.0 (2026-02-06)

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_CONFIG_URL")
}

generator client {
  provider = "prisma-client-js"
  output   = "../server/src/platform/infrastructure/database/.prisma/client"
}

// ============================================================================
// SECTION 1: OA CONFIGURATION DATABASE (Framework Isolation)
// ============================================================================

model OAConfiguration {
  id             String    @id @default(cuid())
  key            String    @unique
  value          String
  configType     String
  description    String?
  isEncrypted    Boolean   @default(false)
  version        Int       @default(1)
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  deletedAt      DateTime?
  createdBy      String
  lastModifiedBy String?
  isActive       Boolean   @default(true)

  @@index([key])
  @@index([createdAt, isActive])
  @@map("oa_configuration")
}

model OAConfigurationHistory {
  id            String   @id @default(cuid())
  configKey     String
  previousValue String
  newValue      String
  changeReason  String?
  changedBy     String
  changedAt     DateTime @default(now())
  version       Int      @default(1)

  @@index([configKey, changedAt])
  @@map("oa_configuration_history")
}

model OAGovernanceRules {
  id             String    @id @default(cuid())
  ruleName       String    @unique
  ruleCode       String
  ruleDefinition String
  severity       String
  isEnforced     Boolean   @default(true)
  authorityLevel String
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  deletedAt      DateTime?
  createdBy      String
  lastModifiedBy String?
  isActive       Boolean   @default(true)

  @@index([isEnforced, isActive])
  @@map("oa_governance_rules")
}

model OAComplianceStatus {
  id             String    @id @default(cuid())
  authorityName  String
  complianceArea String
  status         String
  lastCheckDate  DateTime
  nextCheckDate  DateTime
  details        String?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  deletedAt      DateTime?
  isActive       Boolean   @default(true)

  @@unique([authorityName, complianceArea])
  @@index([status, nextCheckDate])
  @@map("oa_compliance_status")
}

model OAServiceRegistry {
  id              String    @id @default(cuid())
  serviceName     String    @unique
  serviceType     String
  status          String
  endpointUrl     String?
  version         String
  lastHealthCheck DateTime?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  @@index([status, serviceType])
  @@map("oa_service_registry")
}

model OAHealthStatus {
  id            String   @id @default(cuid())
  serviceName   String
  isHealthy     Boolean
  uptime        Float
  lastCheckTime DateTime
  nextCheckTime DateTime
  details       String?
  timestamp     DateTime @default(now())

  @@index([serviceName, timestamp])
  @@index([isHealthy, lastCheckTime])
  @@map("oa_health_status")
}

model OAAccessControl {
  id         String    @id @default(cuid())
  userId     String
  role       String
  resource   String
  permission String
  grantedAt  DateTime  @default(now())
  grantedBy  String
  expiresAt  DateTime?
  isActive   Boolean   @default(true)

  @@unique([userId, resource, permission])
  @@index([userId, isActive])
  @@map("oa_access_control")
}

model OAAuthority {
  id             String   @id @default(cuid())
  authorityName  String   @unique
  authorityLevel Int
  description    String?
  permissions    String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  isActive       Boolean  @default(true)

  @@index([authorityLevel])
  @@map("oa_authority")
}

model OAMetrics {
  id          String   @id @default(cuid())
  metricName  String
  metricValue Float
  unit        String
  component   String
  tags        String?
  timestamp   DateTime @default(now())

  @@index([component, timestamp])
  @@index([metricName, timestamp])
  @@map("oa_metrics")
}

// ============================================================================
// SECTION 2: AUDIT & GOVERNANCE MODELS
// ============================================================================

model OAAuditLog {
  id              String   @id @default(cuid())
  action          String
  entity          String
  entityId        String?
  userId          String?
  component       String
  details         String
  status          String
  errorMessage    String?
  ipAddress       String?
  userAgent       String?
  timestamp       DateTime @default(now())
  executionTimeMs Int?

  @@index([userId, timestamp])
  @@index([entity, timestamp])
  @@index([status, timestamp])
  @@map("oa_audit_log")
}

model ChangeLog {
  id          String   @id @default(cuid())
  entity      String
  entityId    String
  action      String
  beforeValue String?
  afterValue  String?
  changedBy   String
  changedAt   DateTime @default(now())
  version     Int      @default(1)

  @@index([entity, entityId, changedAt])
  @@map("change_log")
}

model ComplianceCheck {
  id        String   @id @default(cuid())
  checkName String
  checkType String
  status    String
  details   String?
  checkedAt DateTime @default(now())
  checkedBy String?

  @@index([checkName, checkedAt])
  @@index([status])
  @@map("compliance_check")
}

model RiskAssessment {
  id              String   @id @default(cuid())
  assetName       String
  riskLevel       String
  riskDescription String
  mitigation      String?
  assessmentDate  DateTime @default(now())
  assessedBy      String?

  @@index([riskLevel, assessmentDate])
  @@map("risk_assessment")
}

// ============================================================================
// SECTION 3: DATABASE INFRASTRUCTURE
// ============================================================================

model DatabaseConnection {
  id             String    @id @default(cuid())
  name           String    @unique
  connectionUrl  String
  databaseType   String
  isActive       Boolean   @default(true)
  maxConnections Int       @default(20)
  idleTimeout    Int       @default(30000)
  testConnection Boolean   @default(false)
  lastTestedAt   DateTime?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  deletedAt      DateTime?

  @@index([isActive])
  @@index([databaseType])
  @@map("database_connection")
}

model DatabaseSchema {
  id          String    @id @default(cuid())
  version     String    @unique
  description String?
  appliedAt   DateTime?
  status      String
  checksum    String
  createdAt   DateTime  @default(now())

  @@map("database_schema")
}

model QueryLog {
  id              String   @id @default(cuid())
  query           String
  executionTimeMs Int
  rowsAffected    Int
  status          String
  errorMessage    String?
  executedBy      String?
  timestamp       DateTime @default(now())

  @@index([executionTimeMs])
  @@index([timestamp])
  @@map("query_log")
}

// ============================================================================
// SECTION 4: CONFIGURATION MANAGEMENT
// ============================================================================

model ConfigurationProvider {
  id           String   @id @default(cuid())
  providerName String   @unique
  providerType String
  priority     Int
  isEnabled    Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@index([priority, isEnabled])
  @@map("configuration_provider")
}

model ConfigurationValue {
  id          String    @id @default(cuid())
  key         String
  value       String
  valueType   String
  providerId  String
  isEncrypted Boolean   @default(false)
  version     Int       @default(1)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?

  @@unique([key, providerId])
  @@index([key])
  @@index([createdAt])
  @@map("configuration_value")
}

model ConfigurationSchema {
  id               String   @id @default(cuid())
  schemaName       String   @unique
  schemaDefinition String
  version          Int      @default(1)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  @@index([schemaName])
  @@map("configuration_schema")
}

// ============================================================================
// SECTION 5: SECURITY FOUNDATION
// ============================================================================

model SecurityPolicy {
  id         String   @id @default(cuid())
  policyName String   @unique
  policyType String
  definition String
  isEnforced Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@index([policyType, isEnforced])
  @@map("security_policy")
}

model EncryptionKey {
  id           String    @id @default(cuid())
  keyName      String    @unique
  keyMaterial  String
  algorithm    String
  keySize      Int
  rotationDate DateTime?
  nextRotation DateTime
  isActive     Boolean   @default(true)
  createdAt    DateTime  @default(now())

  @@index([isActive, nextRotation])
  @@map("encryption_key")
}

model SecurityEvent {
  id          String   @id @default(cuid())
  eventType   String
  userId      String?
  severity    String
  description String
  timestamp   DateTime @default(now())

  @@index([severity, timestamp])
  @@index([userId, timestamp])
  @@map("security_event")
}

// ============================================================================
// SECTION 6: BUSINESS DATA FOUNDATION
// ============================================================================

model FeatureFlag {
  id                String   @id @default(cuid())
  flagName          String   @unique
  isEnabled         Boolean  @default(false)
  rolloutPercentage Int      @default(0)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@index([isEnabled])
  @@map("feature_flag")
}

model AccessToken {
  id         String    @id @default(cuid())
  token      String    @unique
  userId     String
  tokenType  String
  expiresAt  DateTime
  issuedAt   DateTime  @default(now())
  lastUsedAt DateTime?
  isRevoked  Boolean   @default(false)

  @@index([userId, isRevoked])
  @@index([expiresAt])
  @@map("access_token")
}
