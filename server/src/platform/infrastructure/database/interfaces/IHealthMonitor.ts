// ============================================================================
// DATABASE HEALTH MONITOR INTERFACE
// ============================================================================

import type {
  TDatabaseHealth,
  TConnectionPoolMetrics,
  TQueryPerformanceMetrics,
  TSystemMetrics,
  THealthAlert,
} from '../types/index';

/**
 * Database health monitor interface
 */
export interface IHealthMonitor {
  /**
   * Check overall database health
   * @returns Database health status with connection and performance metrics
   */
  checkDatabaseHealth(): Promise<TDatabaseHealth>;

  /**
   * Get connection pool metrics
   * @returns Detailed connection pool metrics from Prisma
   */
  getConnectionPoolMetrics(): Promise<TConnectionPoolMetrics>;

  /**
   * Get query performance metrics
   * @returns Query execution performance metrics
   */
  getQueryPerformanceMetrics(): Promise<TQueryPerformanceMetrics>;

  /**
   * Collect system resource metrics
   * @returns System CPU, memory, and disk metrics
   */
  collectSystemMetrics(): Promise<TSystemMetrics>;

  /**
   * Check for health alerts based on thresholds
   * @returns Array of health alerts if any thresholds exceeded
   */
  checkHealthAlerts(): Promise<THealthAlert[]>;

  /**
   * Trigger a health alert with governance validation
   * @param alert Alert to trigger
   */
  triggerAlert(alert: THealthAlert): Promise<void>;
}
