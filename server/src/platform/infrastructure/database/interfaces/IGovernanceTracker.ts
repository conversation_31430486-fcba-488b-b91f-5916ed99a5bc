// ============================================================================
// GOVERNANCE TRACKER INTERFACE
// ============================================================================

import type {
  TDatabaseOperation,
  TOperationContext,
  TComplianceStatus,
  TViolationEvent,
  TGovernancePolicy,
} from '../types/index';

/**
 * Governance tracker interface for database operations
 */
export interface IGovernanceTracker {
  /**
   * Track a database operation for governance
   * @param operation - Database operation to track
   */
  trackDatabaseOperation(operation: TDatabaseOperation): Promise<void>;

  /**
   * Validate authority for an operation
   * @param context - Operation context for validation
   * @returns true if authorized, false otherwise
   */
  validateAuthority(context: TOperationContext): Promise<boolean>;

  /**
   * Check compliance for an operation
   * @param context - Operation context
   * @returns Compliance status
   */
  checkCompliance(context: TOperationContext): Promise<TComplianceStatus>;

  /**
   * Detect governance violations
   * @returns List of violations detected
   */
  detectViolations(): Promise<TViolationEvent[]>;

  /**
   * Enforce a governance policy
   * @param policy - Policy to enforce
   */
  enforcePolicy(policy: TGovernancePolicy): Promise<void>;
}
