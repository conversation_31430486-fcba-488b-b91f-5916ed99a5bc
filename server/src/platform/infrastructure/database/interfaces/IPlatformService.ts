// ============================================================================
// PLATFORM SERVICE INTERFACE
// M1-CORE-DB-02 | platform-database-service-enhanced
// ============================================================================

/**
 * Cross-cutting platform service interface
 * Implemented by all M1+ platform services
 */
export interface IPlatformService {
  /**
   * Get service name for identification
   */
  getServiceName(): string;

  /**
   * Get service version
   */
  getServiceVersion(): string;

  /**
   * Initialize service
   */
  initialize(): Promise<void>;

  /**
   * Shutdown service gracefully
   */
  shutdown(): Promise<void>;

  /**
   * Check if service is initialized and ready
   */
  isReady(): boolean;

  /**
   * Check if service is shutting down
   */
  isShuttingDown(): boolean;

  /**
   * Get service health status
   */
  getHealthStatus(): Promise<{
    healthy: boolean;
    message: string;
    timestamp: Date;
  }>;
}
