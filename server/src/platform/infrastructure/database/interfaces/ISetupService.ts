// ============================================================================
// SETUP SERVICE INTERFACE
// ============================================================================

/**
 * Service setup interface for lifecycle management
 */
export interface ISetupService {
  /**
   * Setup the service
   * @returns Promise that resolves when setup completes
   */
  setup(): Promise<void>;

  /**
   * Teardown the service
   * @returns Promise that resolves when teardown completes
   */
  teardown(): Promise<void>;

  /**
   * Check if service is ready
   * @returns true if service is ready for operations
   */
  isServiceReady(): boolean;

  /**
   * Reset the service to initial state
   * @returns Promise that resolves when reset completes
   */
  reset(): Promise<void>;

  /**
   * Get service status
   * @returns Service status string
   */
  getServiceStatus(): string;
}
