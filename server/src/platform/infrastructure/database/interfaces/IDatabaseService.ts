// ============================================================================
// DATABASE SERVICE INTERFACE
// M1-CORE-DB-02 | platform-database-service-enhanced
// ============================================================================

import type {
  TDatabaseServiceConfig,
  TQueryResult,
  TTransactionContext,
  TDatabaseHealth,
  TPerformanceMetrics,
} from '../types';

/**
 * Database service interface for Prisma wrapper operations
 */
export interface IDatabaseService {
  // ============================================================================
  // QUERY EXECUTION
  // ============================================================================

  /**
   * Execute a parameterized query
   */
  executeQuery<T = any>(
    query: string,
    params?: Record<string, unknown>
  ): Promise<T>;

  /**
   * Execute raw SQL query
   */
  executeRawQuery<T = any>(
    sql: string,
    values?: unknown[]
  ): Promise<T>;

  /**
   * Find multiple records
   */
  find<T = any>(
    model: string,
    where?: Record<string, unknown>
  ): Promise<T[]>;

  /**
   * Find single record
   */
  findUnique<T = any>(
    model: string,
    where: Record<string, unknown>
  ): Promise<T | null>;

  /**
   * Create new record
   */
  create<T = any>(
    model: string,
    data: Record<string, unknown>
  ): Promise<T>;

  /**
   * Update record
   */
  update<T = any>(
    model: string,
    where: Record<string, unknown>,
    data: Record<string, unknown>
  ): Promise<T>;

  /**
   * Delete record
   */
  delete<T = any>(
    model: string,
    where: Record<string, unknown>
  ): Promise<T>;

  // ============================================================================
  // TRANSACTION MANAGEMENT
  // ============================================================================

  /**
   * Begin a new database transaction
   */
  beginTransaction(): Promise<TTransactionContext>;

  /**
   * Commit a transaction
   */
  commit(transaction: TTransactionContext): Promise<void>;

  /**
   * Rollback a transaction
   */
  rollback(transaction: TTransactionContext): Promise<void>;

  // ============================================================================
  // HEALTH AND MONITORING
  // ============================================================================

  /**
   * Get database connection health status
   */
  getConnectionHealth(): Promise<TDatabaseHealth>;

  /**
   * Get query performance metrics
   */
  getQueryPerformanceMetrics(): Promise<TPerformanceMetrics>;

  // ============================================================================
  // LIFECYCLE
  // ============================================================================

  /**
   * Initialize the database service
   */
  initialize(): Promise<void>;

  /**
   * Shutdown the database service
   */
  shutdown(): Promise<void>;

  /**
   * Check if service is ready
   */
  isReady(): boolean;
}
