// ============================================================================
// MONITORING SERVICE INTERFACE
// ============================================================================

/**
 * Monitoring service interface for health monitoring lifecycle
 */
export interface IMonitoringService {
  /**
   * Start connection pool monitoring
   */
  monitorConnectionPool(): Promise<void>;

  /**
   * Start query performance monitoring
   */
  monitorQueryPerformance(): Promise<void>;

  /**
   * Report current health status to governance system
   */
  reportHealthStatus(): Promise<void>;

  /**
   * Get monitoring service status
   * @returns Service readiness status
   */
  isMonitoring(): boolean;
}
