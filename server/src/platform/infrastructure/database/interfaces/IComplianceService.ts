// ============================================================================
// COMPLIANCE SERVICE INTERFACE
// ============================================================================

import type { TComplianceReport, TComplianceStatus } from '../types/index';

/**
 * Compliance service interface for database governance
 */
export interface IComplianceService {
  /**
   * Report current compliance status
   * @returns Compliance report
   */
  reportComplianceStatus(): Promise<TComplianceReport>;

  /**
   * Get current compliance status
   * @returns Compliance status
   */
  getComplianceStatus(): Promise<TComplianceStatus>;

  /**
   * Initialize compliance monitoring
   */
  initializeCompliance(): Promise<void>;

  /**
   * Shutdown compliance monitoring
   */
  shutdownCompliance(): Promise<void>;
}
