// ============================================================================
// INITIALIZER INTERFACE
// ============================================================================

import type {
  TInitializationState,
  TMigrationResult,
  TDatabaseStateValidation,
} from '../types/index';

/**
 * Database initialization interface
 */
export interface IInitializer {
  /**
   * Initialize the database
   * @returns Promise that resolves when initialization completes
   */
  initializeDatabase(): Promise<void>;

  /**
   * Verify database connection
   * @returns Promise that resolves to true if connection is valid
   */
  verifyDatabaseConnection(): Promise<boolean>;

  /**
   * Run database migrations
   * @returns Promise that resolves to migration result
   */
  runMigrations(): Promise<TMigrationResult>;

  /**
   * Validate database schema
   * @returns Promise that resolves to true if schema is valid
   */
  validateSchema(): Promise<boolean>;

  /**
   * Load seed data into database
   * @returns Promise that resolves when seed data is loaded
   */
  loadSeedData(): Promise<void>;

  /**
   * Load OA framework configuration data
   * @returns Promise that resolves when OA configuration is loaded
   */
  loadOAConfigurationData(): Promise<void>;

  /**
   * Check current database state
   * @returns Promise that resolves to database state validation result
   */
  checkDatabaseState(): Promise<TDatabaseStateValidation>;

  /**
   * Report initialization status to governance system
   * @returns Promise that resolves when status is reported
   */
  reportInitializationStatus(): Promise<void>;

  /**
   * Get current initialization state
   * @returns Current initialization state
   */
  getInitializationState(): TInitializationState;
}
