# ============================================================================
# M1-CORE-DB-01: Prisma Environment Configuration
# OA Framework Database Configuration
# ============================================================================
# Authority: President & CEO, E.Z. Consultancy
# Purpose: Prisma ORM database connection and configuration
# Task ID: M1-CORE-DB-01
# Date: 2026-02-06
# ============================================================================

# ============================================================================
# PRIMARY DATABASE CONNECTION
# ============================================================================
# OA Configuration Database - Framework configuration and metadata
# This database is separate from business data (oa_business_db)
# Used exclusively for M1 infrastructure, governance, and audit tables
DATABASE_CONFIG_URL=postgresql://testuser:testuser%2319@localhost:5432/oaf_config_db

# ============================================================================
# BUSINESS DATABASE CONNECTION (Reference)
# ============================================================================
# Business Database - Application and business data
# Managed by application code, not by M1 infrastructure
# Defined separately from framework configuration
# DATABASE_BUSINESS_URL=postgresql://testuser:testuser%2319@localhost:5432/oa_business_db

# ============================================================================
# PRISMA CONFIGURATION
# ============================================================================

# Prisma Client generation
PRISMA_CLIENT_OUTPUT=../server/src/platform/infrastructure/database/.prisma/client

# ============================================================================
# CONNECTION POOL SETTINGS
# ============================================================================
# PostgreSQL connection pool configuration
# Managed by Prisma Client (recommended settings)

# Maximum number of connections in pool
DATABASE_POOL_SIZE=20

# Connection idle timeout (milliseconds)
DATABASE_IDLE_TIMEOUT=30000

# Connection timeout (milliseconds)
DATABASE_CONNECTION_TIMEOUT=5000

# ============================================================================
# PERFORMANCE REQUIREMENTS (Rule 03 Compliance)
# ============================================================================
# Query execution target: <10ms (with strategic indexes)
DATABASE_QUERY_TIMEOUT_MS=10000

# Health check interval (milliseconds)
DATABASE_HEALTH_CHECK_INTERVAL=30000

# ============================================================================
# NOTES
# ============================================================================
# 1. This file is used for Prisma configuration and deployment
# 2. Local development should use .env.local for sensitive values
# 3. Production deployments should use secure secret management
# 4. Password special character '#' is URL-encoded as %23
# 5. Both databases must exist before schema deployment
# 6. Run 'npx prisma migrate dev' to apply schema to database
