{"profileId": "gdpr-2016", "name": "General Data Protection Regulation (GDPR) Compliance", "standard": "GDPR", "enforcementMode": "strict", "requiredEvents": [{"eventId": "gdpr-data-access", "eventName": "Personal Data Access", "eventCategory": "security", "minimumLogLevel": "info", "retentionDays": 1095, "description": "Access to personal data (Article 15)", "metadata": {"article": "15", "requirement": "Right of access by the data subject"}}, {"eventId": "gdpr-data-deletion", "eventName": "Personal Data Deletion", "eventCategory": "business", "minimumLogLevel": "warn", "retentionDays": 1095, "description": "Deletion of personal data (Article 17)", "metadata": {"article": "17", "requirement": "Right to erasure ('right to be forgotten')"}}, {"eventId": "gdpr-consent-change", "eventName": "Consent Status Change", "eventCategory": "business", "minimumLogLevel": "info", "retentionDays": 1095, "description": "Changes to user consent status (Article 7)", "metadata": {"article": "7", "requirement": "Conditions for consent"}}, {"eventId": "gdpr-data-breach", "eventName": "Data Breach Event", "eventCategory": "security", "minimumLogLevel": "error", "retentionDays": 1095, "description": "Personal data breach notification (Article 33)", "metadata": {"article": "33", "requirement": "Notification of a personal data breach to the supervisory authority"}}], "retentionPolicy": {"policyId": "gdpr-retention-policy", "retentionDays": 1095, "archiveAfterDays": 180, "deleteAfterDays": 1095, "compressionEnabled": true, "encryptionRequired": true, "metadata": {"legalBasis": "GDPR Article 5(1)(e)", "description": "3-year retention for personal data processing records", "archiveLocation": "gdpr-compliant-storage"}}, "encryptionRequired": true, "auditTrailRequired": true, "metadata": {"version": "1.0.0", "effectiveDate": "2018-05-25", "regulatoryBody": "European Data Protection Board", "applicableJurisdictions": ["EU", "EEA"], "description": "GDPR compliance profile for personal data protection and privacy"}}