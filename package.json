{"name": "oa-framework", "version": "1.0.0", "description": "Open Architecture Framework - Enterprise-grade component tracking and governance system", "main": "server/src/index.ts", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:implementation-tracker": "jest tests/platform/tracking/core-data/ImplementationProgressTracker", "test:T-TSK-01": "jest tests/platform/tracking/core-data/", "test:T-TSK-02": "jest tests/platform/tracking/core-trackers/", "test:G-TSK-01": "jest tests/platform/governance/rule-management/core/", "test:G-TSK-02": "jest tests/platform/governance/rule-management/", "test:m0-components": "jest tests/platform/", "test:performance": "NODE_OPTIONS='--max-old-space-size=4096 --expose-gc' TEST_TYPE=performance jest --runInBand --testPathPattern=\"GovernanceTrackingSystem.performance.test.ts\" --verbose", "test:governance:isolated": "node --expose-gc --max-old-space-size=4096 node_modules/.bin/jest --testPathPattern='GovernanceTrackingSystem.*\\.test\\.ts$' --runInBand --no-cache --maxWorkers=1 --logHeapUsage --verbose", "test:governance:debug": "node --expose-gc --max-old-space-size=4096 node_modules/.bin/jest --testPathPattern='GovernanceTrackingSystem.*\\.test\\.ts$' --runInBand --no-cache --maxWorkers=1 --detectOpenHandles --forceExit --verbose", "test:unit": "jest --testPathPattern='GovernanceTrackingSystem\\.test\\.ts$' --runInBand --no-cache", "test:integration": "jest --testPathPattern='GovernanceTrackingSystem\\.integration\\.test\\.ts$' --runInBand --no-cache", "test:security": "jest --testPathPattern='GovernanceTrackingSystem\\.security\\.test\\.ts$' --runInBand --no-cache", "test:troubleshooting": "jest --testPathPattern='TroubleshootingGuideAutomation.*\\.test\\.ts$' --runInBand --no-cache", "test:troubleshooting:unit": "jest --testPathPattern='TroubleshootingGuideAutomation\\.test\\.ts$' --runInBand --no-cache", "test:troubleshooting:integration": "jest --testPathPattern='TroubleshootingGuideAutomation\\.integration\\.test\\.ts$' --runInBand --no-cache", "test:troubleshooting:performance": "NODE_OPTIONS='--max-old-space-size=4096 --expose-gc' jest --testPathPattern='TroubleshootingGuideAutomation\\.performance\\.test\\.ts$' --runInBand --no-cache --testTimeout=120000", "test:troubleshooting:coverage": "NODE_OPTIONS='--max-old-space-size=4096 --expose-gc' jest --testPathPattern='TroubleshootingGuideAutomation.*\\.test\\.ts$' --coverage --runInBand --no-cache", "test:m0-dashboard": "cd demos/m0-real-dashboard && npm test", "test:m0-dashboard:integration": "cd demos/m0-real-dashboard && npm run test:integration", "test:m0-dashboard:coverage": "cd demos/m0-real-dashboard && npm run test:coverage", "test:m0-dashboard:watch": "cd demos/m0-real-dashboard && npm run test:watch", "build": "npm run clean && tsc", "build:watch": "tsc --watch", "build:production": "npm run clean && npm run lint && tsc --project tsconfig.json", "clean": "<PERSON><PERSON><PERSON> dist", "clean:full": "node scripts/clean-build.js", "build:info": "node scripts/clean-build.js --info", "clean:build": "npm run clean && npm run build", "dev": "ts-node server/src/index.ts", "dev:watch": "ts-node --watch server/src/index.ts", "start": "node dist/server/src/index.js", "start:prod": "NODE_ENV=production node dist/server/src/index.js", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "header:validate": "node scripts/header-tools/validate-headers.js", "header:inject": "node scripts/header-tools/inject-header.js", "header:check": "node scripts/header-tools/check-compliance.js", "header:generate": "node scripts/header-tools/generate-header.js", "m01:watch": "node scripts/m01-automation/plan-watcher.js", "m01:check": "node scripts/m01-automation/plan-watcher.js check", "m01:test": "node scripts/m01-automation/test-plan-watcher.js", "postbuild": "echo '✅ Build completed successfully! Output in ./dist directory'"}, "devDependencies": {"@prisma/client": "^5.20.0", "@types/ejs": "^3.1.5", "@types/express": "^5.0.6", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/mustache": "^4.2.6", "@types/node": "^18.15.0", "@types/supertest": "^6.0.3", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^5.57.0", "@typescript-eslint/parser": "^5.57.0", "chokidar": "^3.6.0", "eslint": "^8.37.0", "glob": "^11.0.3", "handlebars": "^4.7.8", "jest": "^29.7.0", "mustache": "^4.2.0", "prisma": "^5.20.0", "rimraf": "^6.0.1", "supertest": "^7.2.2", "ts-jest": "^29.4.0", "ts-node": "^10.9.0", "typescript": "^5.0.0"}, "dependencies": {"express": "^5.2.1", "inversify": "^7.5.4", "lodash": "^4.17.21", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "uuid": "^9.0.0", "ws": "^8.19.0"}, "keywords": ["framework", "tracking", "governance", "enterprise", "typescript", "architecture"], "author": "E.Z. Consultancy", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ez-consultancy/oa-framework.git"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}