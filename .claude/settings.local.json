{"permissions": {"allow": ["Bash(git add:*)", "Bash(git commit:*)", "Bash(npm run build:*)", "Bash(npm test:*)", "Bash(npx tsc:*)", "Bash(npx eslint:*)", "Bash(wc:*)", "<PERSON><PERSON>(jq:*)", "Bash(xargs:*)", "Bash(ls:*)", "Bash(grep:*)", "Bash(ps:*)", "Bash(find:*)", "Bash(./scripts/tree-index.sh:*)", "Read(//home/<USER>/dev/web-dev/oa-prod/server/**)", "Read(//usersdir/home/<USER>/dev/web-dev/oa-prod/server/**)", "Read(//home/<USER>/dev/web-dev/oa-prod/shared/**)", "Read(//usersdir/home/<USER>/dev/web-dev/oa-prod/shared/**)", "Read(//home/<USER>/dev/web-dev/oa-prod/client/**)", "Read(//usersdir/home/<USER>/dev/web-dev/oa-prod/client/**)", "Read(//home/<USER>/dev/web-dev/oa-prod/docs/prompts/**)", "Read(//usersdir/home/<USER>/dev/web-dev/oa-prod/docs/prompts/**)", "Read(//home/<USER>/dev/web-dev/oa-prod/.claude/**)", "Read(//usersdir/home/<USER>/dev/web-dev/oa-prod/.claude/**)", "Read(//home/<USER>/dev/web-dev/oa-prod/**)", "Read(//usersdir/home/<USER>/dev/web-dev/oa-prod/**)", "Bash(npm install:*)", "<PERSON><PERSON>(npx jest:*)", "Bash(for f in server/src/__tests__/gateway/core/*.test.ts server/src/__tests__/gateway/mocks/*.ts server/src/__tests__/gateway/fixtures/*.ts server/src/__tests__/gateway/utils/*.ts server/src/__tests__/gateway/setup/*.ts)", "Bash(done)", "Bash(tree:*)", "Bash(for f in server/src/__tests__/gateway/integration/m0.3/*.test.ts server/src/__tests__/gateway/integration/m0.3/fixtures/*.ts server/src/__tests__/gateway/integration/m0.3/utils/*.ts)", "Bash(node -e:*)", "Bash(for f in server/src/__tests__/gateway/security/*.test.ts server/src/__tests__/gateway/security/fixtures/*.ts server/src/__tests__/gateway/security/utils/*.ts)", "Bash(for i in 1 2 3)", "Bash(do echo \"=== Run $i ===\")", "<PERSON><PERSON>(tee:*)", "Bash(for f in /usersdir/home/<USER>/dev/web-dev/oa-prod/server/src/gateway/core/*.ts)", "Read(//usersdir/home/<USER>/.claude/projects/-usersdir-home-dv-dev-web-dev-oa-prod/60c193c9-767a-4f8b-80b9-ee33113d2f58/tool-results/**)", "Bash(npx prisma generate:*)", "<PERSON><PERSON>(source:*)", "Bash(export DATABASE_CONFIG_URL=\"postgresql://testuser:testuser%2319@localhost:5432/oaf_config_db\")"], "additionalDirectories": ["/home/<USER>/dev/web-dev/oa-prod/docs/plan", "/usersdir/home/<USER>/dev/web-dev/oa-prod/docs/plan", "/home/<USER>/dev/web-dev/oa-prod/shared/src/gateway/interfaces", "/usersdir/home/<USER>/dev/web-dev/oa-prod/shared/src/gateway/interfaces", "/home/<USER>/dev/web-dev/oa-prod/server/src/__tests__/gateway/performance", "/home/<USER>/dev/web-dev/oa-prod/srv-admin/src/components/governance/compliance/unified-compliance-dashboard", "/home/<USER>/dev/web-dev/oa-prod/srv-admin/src/components/governance/executive/executive-governance-dashboard"]}, "enableAllProjectMcpServers": false}