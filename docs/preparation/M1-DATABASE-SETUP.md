# M1: Core Infrastructure Foundation - Database Setup Guide

**Document Version**: 1.0.0
**Created**: 2026-02-06
**Authority**: President & CEO, <PERSON><PERSON><PERSON><PERSON> Consultancy
**Status**: Ready for Phase 1 Implementation

---

## ✅ Database Configuration Complete

### **PostgreSQL Instance Details**

| Property | Value |
|----------|-------|
| **Host** | localhost |
| **Port** | 5432 (default) |
| **Username** | testuser |
| **Password** | testuser#19 |
| **Docker** | ✅ Running |
| **Connection Status** | ✅ Verified |

### **Databases Created**

| Database Name | Purpose | Status |
|---------------|---------|--------|
| `oa_business_db` | Application/business data | ✅ Ready |
| `oaf_config_db` | OA Framework configuration | ✅ Ready |

---

## 🗄️ Database Architecture

### **Dual-Database Design**

M1 uses **one PostgreSQL instance** with **two separate databases**:

#### **Database 1: `oa_business_db`**
- **Purpose**: Application and business data
- **Owner**: Business application (managed by application code)
- **Tables**: User-defined by applications (not defined in M1 schema)
- **Isolation**: Separate from framework configuration
- **Backup**: Separate backup schedule possible

#### **Database 2: `oaf_config_db`**
- **Purpose**: OA Framework configuration only
- **Owner**: M1 infrastructure components
- **Tables**: Defined in `prisma/schema.prisma`
- **Isolation**: Completely separate from business data
- **Access**: Framework configuration operations only

---

## 📋 Prisma Schema Overview (M1-CORE-DB-01)

**Location**: `prisma/schema.prisma`

### **OA Framework Configuration Tables**

| Table | Purpose | Key Columns |
|-------|---------|-------------|
| `oa_system_config` | Master configuration for M1 infrastructure | configKey, configValue, category, isEncrypted |
| `database_migration_log` | Track Prisma migrations | migrationName, appliedAt, status, duration |
| `connection_pool_metrics` | Monitor connection pool performance | activeConnections, idleConnections, p50/p95/p99 latency |
| `query_performance_log` | Track query execution times | executionTime, queryType, rowsAffected |
| `configuration_cache_state` | Configuration caching state | cacheKey, expiresAt, hitCount |
| `provider_health_status` | Configuration provider health | providerId, isHealthy, lastHealthCheckAt |
| `governance_operation_log` | M0 governance integration tracking | operationType, authorityCheckResult, complianceProfile |
| `database_health_status` | Database connectivity and performance | databaseName, isConnected, responseTime |
| `encryption_key_metadata` | Encryption key metadata | keyId, keyAlgorithm, rotatedAt |
| `m1_operation_audit_trail` | Detailed audit of all M1 operations | operationType, userId, resourceId, result |
| `schema_version` | Schema evolution tracking | version, appliedAt, description |

### **Key Design Principles**

1. **Rule 03 Compliance**: Query performance tracking with <10ms target
2. **M0 Integration**: Governance operation logging for audit trail
3. **Memory Safety**: Bounded tables with appropriate indexes
4. **Performance**: Strategic indexing on frequently queried columns
5. **Audit**: Complete operation audit trail for compliance

---

## 🚀 Prisma Setup Instructions

### **Step 1: Install Prisma Dependencies**

```bash
cd /usersdir/home/<USER>/dev/web-dev/oa-prod
npm install prisma --save-dev
npm install @prisma/client
```

### **Step 2: Generate Prisma Client**

```bash
npx prisma generate
```

Output location: `server/src/platform/infrastructure/database/.prisma/client`

### **Step 3: Create Initial Migration**

```bash
npx prisma migrate dev --name init_oa_framework_config
```

This will:
- Create all 11 tables in `oaf_config_db`
- Generate migration file
- Generate Prisma Client

### **Step 4: Verify Schema**

```bash
npx prisma db push --skip-generate
```

Or verify with psql:

```bash
PGPASSWORD="testuser#19" psql -h localhost -U testuser -d oaf_config_db -c "\dt"
```

---

## 🔒 Environment Configuration

### **Root `.env.local` File**

Location: `/usersdir/home/<USER>/dev/web-dev/oa-prod/.env.local`

**Status**: ✅ Created with your connection information

Key variables:
- `DATABASE_BUSINESS_URL`: Connection string for `oa_business_db`
- `DATABASE_CONFIG_URL`: Connection string for `oaf_config_db`
- Performance targets (Rule 03 compliance)
- Security and logging configuration

### **Important Notes**

⚠️ **Security**:
- `.env.local` contains credentials - **DO NOT commit to git**
- `.env.local` already in `.gitignore`
- `.env.example` provided as template for sharing with team

⚠️ **Password Encoding**:
- Special character `#` in password encoded as `%23` in URL
- Connection string: `postgresql://testuser:testuser%2319@localhost:5432/...`

---

## 📊 Connection Strings

### **For Application Code**

**Business Database**:
```
postgresql://testuser:testuser%2319@localhost:5432/oa_business_db
```

**OA Configuration Database**:
```
postgresql://testuser:testuser%2319@localhost:5432/oaf_config_db
```

### **For Command Line Tools**

```bash
# Business database
PGPASSWORD="testuser#19" psql -h localhost -U testuser -d oa_business_db

# Configuration database
PGPASSWORD="testuser#19" psql -h localhost -U testuser -d oaf_config_db
```

---

## ✅ Verification Checklist

### **Pre-Implementation Checklist**

- [x] PostgreSQL instance running in Docker
- [x] Both databases exist (`oa_business_db`, `oaf_config_db`)
- [x] Connection verified from development machine
- [x] `.env.local` created with correct credentials
- [x] `.env.example` created for team reference
- [x] `prisma/schema.prisma` created with all OA config tables

### **Pre-Phase 1 Checklist** (Before starting component development)

- [ ] Prisma installed: `npm install prisma @prisma/client`
- [ ] Prisma client generated: `npx prisma generate`
- [ ] Initial migration created: `npx prisma migrate dev --name init_oa_framework_config`
- [ ] Schema verified in both databases
- [ ] Prisma Studio accessible: `npx prisma studio`
- [ ] npm build compiles successfully
- [ ] npm test runs without connection errors

---

## 🛠️ Development Commands

### **Prisma Client Generation**

```bash
npx prisma generate
```

### **Database Migrations**

```bash
# Create new migration after schema changes
npx prisma migrate dev --name <migration_name>

# Apply migrations (for production-like environments)
npx prisma migrate deploy

# Reset database (development only!)
npx prisma migrate reset

# View migration status
npx prisma migrate status
```

### **Prisma Studio** (Visual database explorer)

```bash
npx prisma studio
```

Opens: http://localhost:5555

### **Database Shell**

```bash
# Configuration database
PGPASSWORD="testuser#19" psql -h localhost -U testuser -d oaf_config_db

# Business database
PGPASSWORD="testuser#19" psql -h localhost -U testuser -d oa_business_db
```

---

## 📈 Next Steps

### **Phase 1: Database Infrastructure (M1-CORE-DB)**

1. **M1-CORE-DB-01** ✅ Prisma Schema Definition
   - Status: **COMPLETE** (this document)
   - Schema: `prisma/schema.prisma`
   - Tables: 11 OA configuration tables

2. **M1-CORE-DB-02** (In Progress)
   - DatabaseServiceEnhanced (Prisma wrapper)
   - Module: `server/src/platform/infrastructure/database`
   - Implements: IDatabaseService
   - Wraps: PrismaClient with MemorySafeResourceManager

3. **M1-CORE-DB-03** (Pending)
   - DatabaseHealthMonitorEnhanced
   - Monitors: Connection pool, query performance
   - Uses: Prisma metrics and events

4. **M1-CORE-DB-04-06** (Pending)
   - Database initialization and integration components

---

## 🔗 Related Documentation

- **Milestone Plan**: `docs/plan/milestone-01-governance-first.md`
- **Rule 03 (Essential Coding)**: `.claude/rules/03-essential-coding-criteria.md`
- **Rule 04 (Memory Management)**: `.claude/rules/04-memory-management.md`
- **Prisma Docs**: https://www.prisma.io/docs/

---

**Authority**: President & CEO, E.Z. Consultancy
**Status**: ✅ Ready for Phase 1 Implementation
**Last Updated**: 2026-02-06
