# Implementation Prompt: M1-CORE-DB-04 - Database Initializer

**Task ID**: M1-CORE-DB-04  
**Task Name**: Database Initializer  
**Milestone**: M1 - Core Infrastructure Foundation  
**Phase**: Phase 1 - Database Infrastructure Foundation  
**Priority**: P0 - Critical Foundation  
**Authority**: President & CEO, <PERSON><PERSON><PERSON><PERSON> Consultancy  
**Status**: READY FOR IMPLEMENTATION  
**Prerequisite**: M0, M0.1, M0.2, M0.3, M00.2, M0A, M1-CORE-DB-01/02/03 COMPLETE

---

## MANDATORY OA FRAMEWORK GOVERNANCE RULES

### Rule 01: TypeScript Header Standard V2.3 (GOV-AI-HEADER-001)
**MANDATORY** - Apply OA Standard Header V2.3 with all 13 sections.

### Rule 02: Development Standard (DEV-FILE-SIZE-001)
**MANDATORY** - File size management:
- Target: ≤700 LOC (GREEN zone)
- Structure: Initialization, schema setup, data seeding logic

### Rule 04: Memory Management (MEM-SAFE-002)
**MA<PERSON><PERSON>ORY** - Memory-safe patterns:
- MUST extend BaseTrackingService
- MUST implement proper lifecycle management
- Clean resource allocation/deallocation

### Rule 05: Anti-Simplification (CORE-POLICY-001)
**MANDATORY ZERO-EXCEPTION POLICY**:
- IMPLEMENT ALL initialization capabilities
- INCLUDE schema validation, migration checks
- DEFINE seed data loading
- NEVER remove features

### Rule 06: Testing Phase (GOV-AI-TEST-001)
**MANDATORY** - Production value over test metrics

### Rule 07: Code Generation (DEV-CODE-GEN-001)
**MANDATORY FRESH CODE** - Generate from requirements

### Rule 09: Test Generation Type Verification (TEST-TYPE-VER-001)
**MANDATORY TYPE VERIFICATION BEFORE TEST GENERATION** (IF tests generated)

---

## OBJECTIVE

Implement **DatabaseInitializer** - Manages database initialization, schema setup, migration execution, and seed data loading. Integrates Prisma migrations with governance tracking, ensures proper database state before operations, validates schema compliance.

### Strategic Importance

1. **Startup Foundation**: Ensures database ready before M1 operations
2. **Schema Management**: Migration tracking and validation
3. **Seed Data**: Initial data loading for OA configuration
4. **State Validation**: Database state compliance checking
5. **Governance Integration**: Initialization audited and governed

---

## TASK OVERVIEW

| Attribute | Value |
|-----------|-------|
| **Reference ID** | M1-CORE-DB-04 |
| **Component Name** | platform-database-initializer |
| **File Location** | `server/src/platform/infrastructure/database/database-initializer.ts` |
| **Inheritance** | BaseTrackingService |
| **Implements** | IInitializer, ISetupService |
| **File Size Target** | ≤700 LOC (GREEN zone) |
| **Estimated LOC** | 250-350 LOC |
| **Dependencies** | M1-CORE-DB-01/02/03, M0, M0.3, M0A |

### Deliverables

**Primary Implementation**:
1. **DatabaseInitializer Class**:
   - BaseTrackingService extension
   - Prisma migration management
   - Schema validation
   - Seed data loading
   - Initialization state tracking

2. **Interfaces & Types**:
   - IInitializer, ISetupService
   - TInitializerConfig, TInitializationState, TMigrationResult

**Test Suite** (IF generated):
- Initialization sequence tests
- Migration execution tests
- Schema validation tests
- Seed data loading tests
- Error handling tests
- 95%+ coverage (Option B: git commit AFTER achievement)

---

## MANDATORY IMPLEMENTATION REQUIREMENTS

### Class Structure
```typescript
export class DatabaseInitializer extends BaseTrackingService implements IInitializer, ISetupService {
  private _databaseService!: IDatabaseService;
  private _auditLogger!: AuditLoggingConfigurationService;
  private _authorityEngine!: IAuthorityEnforcementEngine;
  private _initializationState!: TInitializationState;
  
  protected async doInitialize(): Promise<void>
  protected async doShutdown(): Promise<void>
  
  // Initialization
  async initializeDatabase(): Promise<void>
  async verifyDatabaseConnection(): Promise<boolean>
  
  // Migrations
  async runMigrations(): Promise<TMigrationResult>
  async validateSchema(): Promise<boolean>
  
  // Seed data
  async loadSeedData(): Promise<void>
  async loadOAConfigurationData(): Promise<void>
  
  // Validation
  async checkDatabaseState(): Promise<TDatabaseStateValidation>
  async reportInitializationStatus(): Promise<void>
}
```

### Core Methods

**Initialization** (≤40 LOC each):
- `initializeDatabase()`: Overall initialization orchestration
- `verifyDatabaseConnection()`: Test database connectivity

**Migrations** (≤50 LOC each):
- `runMigrations()`: Execute Prisma migrations
- `validateSchema()`: Validate schema compliance

**Seed Data** (≤40 LOC each):
- `loadSeedData()`: Load business seed data
- `loadOAConfigurationData()`: Load OA framework configuration

**Validation** (≤35 LOC each):
- `checkDatabaseState()`: Validate database state
- `reportInitializationStatus()`: Report to governance

### Integration Requirements

**Prisma Integration**:
- Execute `prisma migrate deploy` for migrations
- Validate generated Prisma client
- Check schema compliance

**M0.3 Logging**:
- Log initialization steps
- Record migration execution
- Capture errors with context

**M0A Authority Enforcement**:
- Validate initialization authorization
- Report completion to authority engine

**M0 Governance**:
- Track initialization state
- Report completion to governance system

---

## POST-TASK COMPLETION WORKFLOW (IF TESTS GENERATED)

### STEP 1: Create Completion Tracking JSON
**File**: `docs/governance/tracking/status/.oa-m1-core-db-04-completion.json`

### STEP 2: Update Milestone Tracking
**File**: `docs/governance/tracking/status/.oa-m1-milestone-tracking.json`

### STEP 3: Git Commit (AFTER 95%+ Coverage - Option B)

```
git add server/src/platform/infrastructure/database/database-initializer.ts
git add server/src/platform/infrastructure/database/interfaces/IInitializer.ts
git add server/src/platform/infrastructure/database/types/TInitializerConfig.ts
git add server/src/platform/infrastructure/database/seeds/*.ts
git add server/src/platform/infrastructure/database/__tests__/*.test.ts
git add docs/governance/tracking/status/.oa-m1-core-db-04-completion.json

git commit -m "feat(M1-CORE-DB-04): Database Initializer Complete

Database initialization, migration management, and seed data loading.

Deliverables:
- database-initializer.ts: 250-350 LOC
- Migration execution via Prisma
- Schema validation and compliance
- OA configuration seed data
- Governance integration

Capabilities:
- Database connection verification
- Prisma migration deployment
- Schema validation
- Seed data loading
- Initialization state tracking

Quality:
- 95%+ test coverage
- Zero TypeScript errors
- OA Header V2.3 compliance

Next: M1-CORE-DB-05 (Database Governance Tracker)"
```

---

## SUCCESS CRITERIA

**Task complete when**:
- OA Header V2.3 with 13 sections (Rule 01)
- DatabaseInitializer implementation (250-350 LOC) (Rule 02)
- BaseTrackingService inheritance with lifecycle (Rule 04)
- Database connection verification functional
- Prisma migration execution functional
- Schema validation functional
- Seed data loading functional
- OA configuration data loading functional
- M0.3 logging integration functional
- M0A authority validation functional
- Initialization state tracking functional
- If test suite: 95%+ coverage BEFORE git commit (Option B - Rule 09)
- Zero TypeScript errors

---

**AUTHORIZATION**: President & CEO, E.Z. Consultancy  
**ENFORCEMENT**: Mandatory for M1-CORE-DB-04 implementation  
**COMPLIANCE**: Required (Rules 01-07, 09)  
**EFFECTIVE**: Immediate implementation  
**FILE GENERATION POLICY**: Option B - Git commit ONLY if tests generated AND 95%+ coverage achieved
