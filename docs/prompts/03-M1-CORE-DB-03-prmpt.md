# Implementation Prompt: M1-CORE-DB-03 - Database Health Monitor Enhanced

**Task ID**: M1-CORE-DB-03  
**Task Name**: Database Health Monitor Enhanced (Prisma Metrics)  
**Milestone**: M1 - Core Infrastructure Foundation  
**Phase**: Phase 1 - Database Infrastructure Foundation  
**Priority**: P0 - Critical Foundation  
**Authority**: President & CEO, <PERSON><PERSON><PERSON><PERSON> Consultancy  
**Status**: READY FOR IMPLEMENTATION  
**Prerequisite**: M0, M0.1, M0.2, M0.3, M00.2, M0A, M1-CORE-DB-01, M1-CORE-DB-02 all COMPLETE

---

## MANDATORY OA FRAMEWORK GOVERNANCE RULES

### Rule 01: TypeScript Header Standard V2.3 (GOV-AI-HEADER-001)
**MANDATORY** - Apply OA Standard Header V2.3 with ALL 13 sections including library metadata for Prisma metrics integration.

**Format**: Comprehensive header with Prisma $metrics() documentation

**Violations**: Missing header = Code review rejection

### Rule 02: Development Standard (DEV-FILE-SIZE-001)
**MANDATORY** - File size management:
- Target: ≤700 LOC (GREEN zone)
- Structure: Health monitoring logic with sub-10ms response times

### Rule 03: Essential Coding Criteria (ENT-TIMING-001)
**MANDATORY** - Resilient Timing Integration:
- Implement `_resilientTimer` + `_metricsCollector` dual-field pattern
- Monitor health check execution <5ms (Rule 03)
- Track connection pool performance
- Capture Prisma metrics efficiently

### Rule 04: Memory Management (MEM-SAFE-002)
**MANDATORY** - Memory-safe inheritance patterns:
- MUST extend `MemorySafeResourceManager` (Enhanced component)
- MUST implement `doInitialize()` and `doShutdown()` lifecycle methods
- Bounded health event collection with LRU eviction

**Violations**: Manual timer management = Memory leak risk

### Rule 05: Anti-Simplification (CORE-POLICY-001)
**MANDATORY ZERO-EXCEPTION POLICY**:
- IMPLEMENT ALL monitoring capabilities completely
- INCLUDE connection pool metrics, query performance tracking
- DEFINE health assessment logic with detailed reporting
- NEVER remove monitoring features

**Violations**: Feature reduction = Escalation to architecture team

### Rule 06: Testing Phase (GOV-AI-TEST-001)
**MANDATORY** - Production value over test metrics:
- Health monitoring must provide actual operational visibility
- Metrics must enable real governance decisions
- NO mock-only health checks

**Validation**: Can you explain business value without mentioning tests? YES required

### Rule 07: Code Generation (DEV-CODE-GEN-001)
**MANDATORY FRESH CODE** - Generate from requirements, NOT copy:
- Generate implementation from current M1 requirements
- Apply current OA Framework patterns
- Use current naming conventions

### Rule 09: Test Generation Type Verification (TEST-TYPE-VER-001)
**MANDATORY TYPE VERIFICATION BEFORE TEST GENERATION** (IF tests generated):
- Review Prisma $metrics() return types
- Document IHealthMonitor interface
- Create type-compliant test fixtures
- Run TypeScript compiler BEFORE test execution

---

## OBJECTIVE

Implement **DatabaseHealthMonitorEnhanced** - Prisma metrics wrapper monitoring connection pool health, query performance, and system resource utilization. Provides real-time health assessment, alerts, and governance integration with <5ms response time target.

### Strategic Importance

1. **Health Visibility**: Real-time database health status for M1 operations
2. **Performance Tracking**: Query performance monitoring via Prisma metrics
3. **Governance Integration**: Health decisions integrated with M0A Authority Enforcement
4. **Proactive Alerts**: Detect and report health degradation
5. **Resource Optimization**: Monitor and optimize connection usage

---

## TASK OVERVIEW

| Attribute | Value |
|-----------|-------|
| **Reference ID** | M1-CORE-DB-03 |
| **Component Name** | platform-database-health-monitor-enhanced |
| **File Location** | `server/src/platform/infrastructure/database/database-health-monitor-enhanced.ts` |
| **Library Integration** | Prisma Client v5.x ($metrics, events) |
| **Inheritance** | MemorySafeResourceManager (Rule 04) |
| **Implements** | IHealthMonitor, IMonitoringService |
| **File Size Target** | ≤700 LOC (GREEN zone) |
| **Estimated LOC** | 300-400 LOC |
| **Dependencies** | M1-CORE-DB-02, M0.1, M0.3, M0A, M00.2 |

### Deliverables

**Primary Implementation**:
1. **DatabaseHealthMonitorEnhanced Class**:
   - MemorySafeResourceManager extension
   - Dual-field pattern (_resilientTimer, _metricsCollector)
   - Prisma $metrics() integration
   - Health assessment logic
   - Alert management

2. **Interfaces & Types**:
   - IHealthMonitor, IMonitoringService
   - THealthMonitorConfig, TDatabaseHealth, TConnectionPoolMetrics

**Test Suite** (IF generated):
- Health check method tests
- Metrics collection tests
- Alert triggering tests
- M0A integration tests
- Performance baseline tests
- 95%+ coverage (Option B: git commit AFTER achievement)

---

## MANDATORY IMPLEMENTATION REQUIREMENTS

### Class Structure
```typescript
export class DatabaseHealthMonitorEnhanced extends MemorySafeResourceManager implements IHealthMonitor, IMonitoringService {
  private _resilientTimer!: ResilientTimer;
  private _metricsCollector!: ResilientMetricsCollector;
  private _databaseService!: IDatabaseService;
  private _authorityEngine!: IAuthorityEnforcementEngine;
  private _auditLogger!: AuditLoggingConfigurationService;
  private _healthMetrics!: Map<string, THealthMetric>;
  private _alertThresholds!: TAlertThresholds;
  
  protected async doInitialize(): Promise<void>
  protected async doShutdown(): Promise<void>
  
  // Health assessment
  async checkDatabaseHealth(): Promise<TDatabaseHealth>
  async getConnectionPoolMetrics(): Promise<TConnectionPoolMetrics>
  async getQueryPerformanceMetrics(): Promise<TQueryPerformanceMetrics>
  
  // Monitoring
  async monitorConnectionPool(): Promise<void>
  async monitorQueryPerformance(): Promise<void>
  async collectSystemMetrics(): Promise<TSystemMetrics>
  
  // Alerting
  async checkHealthAlerts(): Promise<THealthAlert[]>
  async triggerAlert(alert: THealthAlert): Promise<void>
  
  // Governance
  async reportHealthStatus(): Promise<void>
}
```

### Core Methods

**Health Assessment** (≤50 LOC each):
- `checkDatabaseHealth()`: Overall health status
- `getConnectionPoolMetrics()`: Connection pool state
- `getQueryPerformanceMetrics()`: Query performance data

**Monitoring** (≤40 LOC each):
- `monitorConnectionPool()`: Track connection usage
- `monitorQueryPerformance()`: Track query metrics
- `collectSystemMetrics()`: System resource data

**Alerting** (≤35 LOC each):
- `checkHealthAlerts()`: Evaluate alert conditions
- `triggerAlert()`: Send alert with governance validation

### Integration Requirements

**Prisma Integration**:
- Use `prisma.$metrics()` for connection metrics
- Monitor query execution via event listeners
- Track connection pool size and usage
- Report slow query detection

**M0A Authority Enforcement**:
- Validate health decision authorization
- Report health status to authority engine
- Check compliance before alert actions

**M0.3 AuditLoggingConfigurationService**:
- Log health check events
- Record alert triggers
- Capture metric snapshots

**M0.1 EnterpriseGovernanceTrackingSystem**:
- Report health status
- Track metric trends
- Record governance compliance

### Performance Targets

- Health check response <5ms (Rule 03)
- Metrics collection <10ms per operation
- Alert evaluation <5ms
- No blocking operations in monitoring loop

---

## POST-TASK COMPLETION WORKFLOW (IF TESTS GENERATED)

### STEP 1: Create Completion Tracking JSON
**File**: `docs/governance/tracking/status/.oa-m1-core-db-03-completion.json`

### STEP 2: Update Milestone Tracking
**File**: `docs/governance/tracking/status/.oa-m1-milestone-tracking.json`

### STEP 3: Git Commit (AFTER 95%+ Coverage - Option B)

```
git add server/src/platform/infrastructure/database/database-health-monitor-enhanced.ts
git add server/src/platform/infrastructure/database/interfaces/IHealthMonitor.ts
git add server/src/platform/infrastructure/database/types/TDatabaseHealth.ts
git add server/src/platform/infrastructure/database/constants/health-monitor-constants.ts
git add server/src/platform/infrastructure/database/__tests__/*.test.ts
git add docs/governance/tracking/status/.oa-m1-core-db-03-completion.json

git commit -m "feat(M1-CORE-DB-03): Database Health Monitor Enhanced Complete

Prisma metrics monitoring for M1 database infrastructure.

Deliverables:
- database-health-monitor-enhanced.ts: 300-400 LOC
- Dual-field pattern with <5ms health check response
- Connection pool, query performance, system metrics monitoring
- 95%+ test coverage achieved

Monitoring:
- Prisma $metrics() integration
- Connection pool health tracking
- Query performance monitoring
- System resource utilization
- Alert management and governance integration

Quality:
- Zero TypeScript errors
- OA Header V2.3 compliance
- MemorySafeResourceManager lifecycle

Next: M1-CORE-DB-04 (Database Initializer)"
```

---

## SUCCESS CRITERIA

**Task complete when**:
- OA Header V2.3 with 13 sections (Rule 01)
- DatabaseHealthMonitorEnhanced implementation (300-400 LOC) (Rule 02)
- MemorySafeResourceManager with doInitialize/doShutdown (Rule 04)
- Dual-field pattern (_resilientTimer, _metricsCollector) (Rule 03)
- Prisma $metrics() integration functional
- Health check response <5ms (Rule 03)
- Connection pool metrics collection functional
- Query performance monitoring functional
- System metrics collection functional
- Alert triggering with governance validation
- M0A integration functional
- M0.3 logging integration functional
- If test suite: 95%+ coverage BEFORE git commit (Option B - Rule 09)
- Zero TypeScript errors

---

**AUTHORIZATION**: President & CEO, E.Z. Consultancy  
**ENFORCEMENT**: Mandatory for M1-CORE-DB-03 implementation  
**COMPLIANCE**: Required (Rules 01-07, 09)  
**EFFECTIVE**: Immediate implementation  
**FILE GENERATION POLICY**: Option B - Git commit ONLY if tests generated AND 95%+ coverage achieved
