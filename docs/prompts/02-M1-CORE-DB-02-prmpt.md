# Implementation Prompt: M1-CORE-DB-02 - Database Service Enhanced

**Task ID**: M1-CORE-DB-02  
**Task Name**: Database Service Enhanced (Prisma Wrapper)  
**Milestone**: M1 - Core Infrastructure Foundation  
**Phase**: Phase 1 - Database Infrastructure Foundation  
**Priority**: P0 - Critical Foundation  
**Authority**: President & CEO, E<PERSON><PERSON><PERSON> Consultancy  
**Status**: READY FOR IMPLEMENTATION  
**Prerequisite**: M0, M0.1, M0.2, M0.3, M00.2, M0A all COMPLETE; M1-CORE-DB-01 COMPLETE

---

## MANDATORY OA FRAMEWORK GOVERNANCE RULES

### Rule 01: TypeScript Header Standard V2.3 (GOV-AI-HEADER-001)
**MANDATORY** - Apply OA Standard Header V2.3 with ALL 13 sections:
- AI CONTEXT (component purpose, library integration, complexity)
- OA FRAMEWORK header (file path, milestone, task-id, component)
- AUTHORITY-DRIVEN GOVERNANCE (authority-level, governance, compliance)
- LIBRARY METADATA (library: Prisma Client v5.x, integration patterns)
- CROSS-CONTEXT REFERENCES (dependencies: PrismaClient, ResilientTimer, EnterpriseGovernanceTrackingSystem)
- MEMORY SAFETY & TIMING RESILIENCE (dual-field pattern, performance targets)
- GATEWAY INTEGRATION (M00.2 gateway, GOVERNANCE access pattern)
- SECURITY CLASSIFICATION (database security level, compliance requirements)
- PERFORMANCE REQUIREMENTS (performance target <10ms, query tracking)
- INTEGRATION REQUIREMENTS (M0.3 logging, M0.1 governance, M0A authority)
- ENHANCED METADATA (component-type: service-enhanced, lifecycle-stage)
- ORCHESTRATION METADATA (authority-driven, context-validated, production-ready)

**Violations**: Missing header = Code review rejection

### Rule 02: Development Standard (DEV-FILE-SIZE-001)
**MANDATORY** - File size management:
- Target: ≤700 LOC (GREEN zone)
- Warning: ≤1200 LOC (YELLOW)
- Critical: ≤2200 LOC (RED)
- Structure: 1 main class + 4 helpers max, ≤25 methods per class, ≤100 LOC per method

**AI Context**: Add section headers every 150-200 lines for files >700 LOC

### Rule 03: Essential Coding Criteria (ENT-TIMING-001)
**MANDATORY** - Resilient Timing Integration:
- Implement `_resilientTimer` + `_metricsCollector` dual-field pattern
- Decision rule: "Enhanced" suffix = ADD TIMING (M1-CORE-DB-02 qualifies)
- Target: Database operations <10ms per Rule 03
- Track all Prisma queries via ResilientTimer
- Capture metrics in ResilientMetricsCollector

### Rule 04: Memory Management (MEM-SAFE-002)
**MANDATORY** - Memory-safe inheritance patterns:
- MUST extend `MemorySafeResourceManager` (Rule 04 - Enhanced component)
- MUST implement `doInitialize()` and `doShutdown()` lifecycle methods
- MUST use `createSafeInterval()`, `createSafeTimeout()` for any timers
- NO `setInterval()`, `setTimeout()` direct calls
- Manage PrismaClient lifecycle safely in initialize/shutdown

**Violations**: Manual timer management = Memory leak risk

### Rule 05: Anti-Simplification (CORE-POLICY-001)
**MANDATORY ZERO-EXCEPTION POLICY**:
- IMPLEMENT ALL query execution methods completely
- INCLUDE transaction support (beginTransaction, commit, rollback)
- DEFINE query monitoring and performance tracking
- INCLUDE error handling with governance reporting
- NEVER remove features to fix errors

**Violations**: Feature reduction = Escalation to architecture team

### Rule 06: Testing Phase (GOV-AI-TEST-001)
**MANDATORY** - Production value over test metrics:
- ALL code changes MUST solve real database operation needs
- Query execution must provide measurable performance improvements
- Monitoring must enable actual governance compliance
- NO coverage-driven modifications

**Validation**: Can you explain business value without mentioning tests? YES required

### Rule 07: Code Generation (DEV-CODE-GEN-001)
**MANDATORY FRESH CODE** - Generate from requirements, NOT copy:
- Generate implementation from current M1 requirements
- Apply current OA Framework patterns (MemorySafeResourceManager, ResilientTimer)
- Use current naming conventions (I prefix, T prefix, Enhanced suffix)
- NO copying from templates or previous milestones

### Rule 09: Test Generation Type Verification (TEST-TYPE-VER-001)
**MANDATORY TYPE VERIFICATION BEFORE TEST GENERATION** (IF tests generated):
- Phase 1: Review Prisma generated types
- Phase 2: Document IDatabaseService interface and all method signatures
- Phase 3: Create type-compliant test data/mocks
- Phase 4: Generate tests using verified type structures
- Phase 5: Run TypeScript compiler BEFORE test execution

**Explicitly Prohibited**:
- Generate tests without reviewing Prisma types
- Assume types from method names
- Create mock data without type verification
- Run tests before TypeScript compilation success

**Violations**: TypeScript compilation errors = Code review rejection

---

## OBJECTIVE

Implement **DatabaseServiceEnhanced** - Prisma ORM wrapper for M1 database infrastructure. Wraps PrismaClient with governance tracking, timing metrics, M0.3 logging integration, and M0A authority validation. Provides query execution, transaction management, connection monitoring with enterprise-grade reliability.

### Strategic Importance

1. **M1 Foundation**: Enables all M1 database operations
2. **Library Wrapper Pattern**: Best practice Prisma integration with OA governance
3. **Performance Tracking**: <10ms query baseline with ResilientTimer monitoring
4. **Governance Integration**: All operations tracked by M0.1 governance system
5. **Production Reliability**: Memory-safe lifecycle with proper resource cleanup

---

## TASK OVERVIEW

### Component Details

| Attribute | Value |
|-----------|-------|
| **Reference ID** | M1-CORE-DB-02 |
| **Component Name** | platform-database-service-enhanced |
| **File Location** | `server/src/platform/infrastructure/database/database-service-enhanced.ts` |
| **File Type** | TypeScript service class |
| **Library Integration** | Prisma Client v5.x (@prisma/client) |
| **Service Type** | Database wrapper (Prisma ORM) |
| **Inheritance** | MemorySafeResourceManager (Rule 04) |
| **Implements** | IDatabaseService, IPlatformService (Rule 01) |
| **Header Standard** | OA Header V2.3 (13 sections - Rule 01) |
| **File Size Target** | ≤700 LOC (GREEN zone - Rule 02) |
| **Estimated LOC** | 350-450 LOC |
| **Dependencies** | M0, M0.1, M0.2, M0.3, M00.2, M0A, M1-CORE-DB-01 (schema) |

### Deliverables

**Primary Implementation**:
1. **DatabaseServiceEnhanced Class** (`database-service-enhanced.ts`):
   - MemorySafeResourceManager extension
   - OA Header V2.3 with library metadata
   - PrismaClient wrapper with governance tracking
   - Dual-field pattern (_resilientTimer, _metricsCollector)
   - Lifecycle management (doInitialize, doShutdown)
   - Full JSDoc documentation

2. **Interface Definition**:
   - `IDatabaseService` (implements pattern)
   - `IPlatformService` (cross-cutting interface)
   - Type definitions: `TDatabaseServiceConfig`, `TQueryResult`, `TTransactionContext`

3. **Constants File** (`database-service-constants.ts`):
   - `MAX_QUERY_TIMEOUT_MS` (default: 10000)
   - `DEFAULT_CONNECTION_TIMEOUT` (default: 5000)
   - `QUERY_PERFORMANCE_THRESHOLD` (default: 10 ms)
   - `BATCH_OPERATION_SIZE` (default: 1000)

**Test Suite** (IF generated):
- Unit tests for query execution methods
- Transaction management tests
- Timing and metrics collection tests
- M0.3 logging integration tests
- M0.1 governance reporting tests
- M0A authority validation tests
- Error handling and edge case tests
- Performance baseline validation tests
- 95%+ coverage required (Option B: git commit AFTER achievement)

**Documentation**:
- Implementation summary document
- Prisma wrapper patterns guide
- Query execution examples
- Error handling strategy

---

## MANDATORY IMPLEMENTATION REQUIREMENTS

### Class Structure
```typescript
export class DatabaseServiceEnhanced extends MemorySafeResourceManager implements IDatabaseService, IPlatformService {
  // Dual-field pattern (Rule 03)
  private _resilientTimer!: ResilientTimer;
  private _metricsCollector!: ResilientMetricsCollector;
  
  // Prisma integration
  private _prismaClient!: PrismaClient;
  private _governanceTracker!: EnterpriseGovernanceTrackingSystem;
  private _auditLogger!: AuditLoggingConfigurationService;
  
  // Lifecycle management (Rule 04)
  protected async doInitialize(): Promise<void>
  protected async doShutdown(): Promise<void>
  
  // Core query execution
  async executeQuery<T>(query: string, params?: Record<string, unknown>): Promise<T>
  async executeRawQuery<T>(sql: string, values?: unknown[]): Promise<T>
  async find<T>(model: string, where?: Record<string, unknown>): Promise<T[]>
  async findUnique<T>(model: string, where: Record<string, unknown>): Promise<T | null>
  async create<T>(model: string, data: Record<string, unknown>): Promise<T>
  async update<T>(model: string, where: Record<string, unknown>, data: Record<string, unknown>): Promise<T>
  async delete<T>(model: string, where: Record<string, unknown>): Promise<T>
  
  // Transaction support
  async beginTransaction(): Promise<TTransactionContext>
  async commit(transaction: TTransactionContext): Promise<void>
  async rollback(transaction: TTransactionContext): Promise<void>
  
  // Health and monitoring
  async getConnectionHealth(): Promise<TDatabaseHealth>
  async getQueryPerformanceMetrics(): Promise<TPerformanceMetrics>
}
```

### Core Methods

**Query Execution** (≤50 LOC each):
- `executeQuery<T>()`: Generic query execution with timing
- `executeRawQuery<T>()`: Raw SQL execution
- `find<T>()`: Fetch multiple records
- `findUnique<T>()`: Fetch single record
- `create<T>()`: Create record
- `update<T>()`: Update record
- `delete<T>()`: Delete record

**Transaction Management** (≤30 LOC each):
- `beginTransaction()`: Start transaction context
- `commit()`: Commit with governance validation
- `rollback()`: Rollback with audit logging

**Monitoring** (≤40 LOC each):
- `getConnectionHealth()`: Connection pool health status
- `getQueryPerformanceMetrics()`: Captured metrics summary
- `recordQueryMetric()`: Private helper for metrics

**Lifecycle** (≤50 LOC each):
- `doInitialize()`: PrismaClient connection setup
- `doShutdown()`: Graceful PrismaClient disconnect

### Integration Requirements

**M0.1 EnterpriseGovernanceTrackingSystem**:
- Track query execution start/end
- Report performance metrics
- Record errors with context
- Validate authority for operations

**M0.3 AuditLoggingConfigurationService**:
- Log all database operations
- Capture query details for audit
- Track transaction lifecycle
- Support configurable log levels

**M0A Authority Enforcement Engine**:
- Validate operation authorization
- Check compliance profiles (SOX, GDPR, HIPAA)
- Enforce operational policies
- Report compliance violations

**M00.2 Unified API Gateway**:
- Route external database calls through gateway
- Include GOVERNANCE access pattern
- Propagate compliance profiles
- Track operations for audit compliance

**Prisma Integration**:
- Wrap PrismaClient instance
- Support Prisma event listeners
- Use Prisma $queryRaw for timing accuracy
- Monitor connection pool via $metrics()

### Performance Targets

- Query execution tracking <10ms overhead (Rule 03)
- Metrics collection <5ms per operation
- Transaction setup <5ms
- Health checks <5ms response time
- No query execution without timing

### Error Handling

- Wrap Prisma errors with governance context
- Report errors to M0.1 governance system
- Log detailed error information to M0.3
- Provide actionable error messages
- Clean resource cleanup on error

---

## POST-TASK COMPLETION WORKFLOW (IF TESTS GENERATED)

### STEP 1: Create Task Completion Tracking JSON
**File**: `docs/governance/tracking/status/.oa-m1-core-db-02-completion.json`

**Required sections**:
- taskCompletion: status=COMPLETE, completionPercentage=100
- filesCompleted: List implementation + test + interface + constants files
- governanceRulesCompliance: Rule01-Header, Rule02-FileSize, Rule03-Timing, Rule04-Memory, Rule05-AntiSimplification, Rule06-Testing, Rule07-CodeGen, Rule09-TypeVerification
- coverageMetrics: TypeScript errors=0, test coverage 95%+, timing overhead <10ms
- technicalAchievements: Prisma wrapper, transaction support, governance integration
- dependencies: requires M1-CORE-DB-01, enables M1-CORE-DB-03/04/05/06

### STEP 2: Update M1 Milestone Tracking JSON
**File**: `docs/governance/tracking/status/.oa-m1-milestone-tracking.json`

**Updates**:
- milestoneOverview.completedTasks: increment
- phase1.completedComponents: Add platform-database-service-enhanced
- completedTasks: Add M1-CORE-DB-02 entry

### STEP 3: Git Commit (AFTER 95%+ Coverage Achievement - Option B)

```
git add server/src/platform/infrastructure/database/database-service-enhanced.ts
git add server/src/platform/infrastructure/database/interfaces/IDatabaseService.ts
git add server/src/platform/infrastructure/database/types/TDatabaseServiceConfig.ts
git add server/src/platform/infrastructure/database/constants/database-service-constants.ts
git add server/src/platform/infrastructure/database/__tests__/*.test.ts
git add docs/governance/tracking/status/.oa-m1-core-db-02-completion.json
git add docs/governance/tracking/status/.oa-m1-milestone-tracking.json
git add docs/plan/milestone-01-governance-first.md

git commit -m "feat(M1-CORE-DB-02): Database Service Enhanced Complete

Prisma ORM wrapper for M1 database infrastructure with governance integration.

Deliverables:
- database-service-enhanced.ts: 350-450 LOC with OA Header V2.3
- Query execution, transaction support, health monitoring
- Dual-field pattern (ResilientTimer + ResilientMetricsCollector)
- Test suite: Unit, integration, performance (95%+ coverage)

Prisma Integration:
- PrismaClient wrapper with lifecycle management
- Query execution with timing <10ms target
- Transaction support (begin, commit, rollback)
- Connection pool health monitoring

Governance Integration:
- M0.1: Operations tracked by EnterpriseGovernanceTrackingSystem
- M0.3: All operations logged to AuditLoggingConfigurationService
- M0A: Authority validation for operations
- M00.2: Gateway integration with GOVERNANCE access pattern

Performance:
- <10ms query overhead with timing
- Metrics collection <5ms per operation
- Health checks <5ms response time

Quality:
- 95%+ test coverage achieved
- Zero TypeScript errors (strict mode)
- OA Header V2.3 compliance verified
- MemorySafeResourceManager lifecycle implemented

Next: M1-CORE-DB-03 (Database Health Monitor Enhanced)"
```

**IMPORTANT - Option B Compliance**: Git commit MUST occur AFTER 95%+ test coverage achievement is verified.

---

## SUCCESS CRITERIA

**Task complete when**:
- OA Header V2.3 applied with all 13 sections (Rule 01)
- DatabaseServiceEnhanced implementation complete (350-450 LOC) (Rule 02)
- MemorySafeResourceManager inheritance with doInitialize/doShutdown (Rule 04)
- Dual-field pattern implemented (_resilientTimer, _metricsCollector) (Rule 03)
- PrismaClient wrapping fully functional
- Query execution methods: executeQuery, executeRawQuery, find, findUnique, create, update, delete
- Transaction methods: beginTransaction, commit, rollback
- Monitoring methods: getConnectionHealth, getQueryPerformanceMetrics
- ALL planned functionality implemented completely (Rule 05)
- M0.1 governance tracking integration functional
- M0.3 logging integration functional
- M0A authority validation functional
- M00.2 gateway integration with GOVERNANCE access pattern
- Timing performance <10ms baseline achieved
- Connection lifecycle management (doInitialize, doShutdown) working
- If test suite generated: 95%+ coverage achieved BEFORE git commit (Option B - Rule 09)
- Zero TypeScript errors (strict mode)
- Complete JSDoc/comment documentation
- Prisma types fully verified before tests

---

## REFERENCES

### Related Tasks
- M1-CORE-DB-01: Prisma Schema Definition (prerequisite - COMPLETE)
- M1-CORE-DB-03: Database Health Monitor Enhanced (next)
- M1-CORE-DB-04: Database Initializer (next phase)

### Milestone Documentation
- **Milestone Plan**: milestone-01-governance-first.md
- **M0 Base**: milestone-00-governance-tracking.md
- **M0.3 Logging**: milestone-00_3-configurable-logging-infrastructure.md
- **M00.2 Gateway**: milestone-00_2-unified-api-gateway.md
- **M0A Business Apps**: milestone-00a-business-app-gov-ext.md

### Development Standards
- **Rule 01: TypeScript Header V2.3**: `.claude/rules/01-typescript-header-standard.md`
- **Rule 03: Essential Coding Criteria**: `.claude/rules/03-essential-coding-criteria.md`
- **Rule 04: Memory Management**: `.claude/rules/04-memory-management.md`

### Reference Implementations
- **Prisma Documentation**: https://www.prisma.io/docs/
- **MemorySafeResourceManager**: server/src/platform/governance/base/MemorySafeResourceManager.ts
- **ResilientTimer**: server/src/platform/governance/resilience/ResilientTimer.ts
- **BaseTrackingService**: server/src/platform/governance/base/BaseTrackingService.ts

---

**AUTHORIZATION**: President & CEO, E.Z. Consultancy  
**ENFORCEMENT**: Mandatory for M1-CORE-DB-02 implementation  
**COMPLIANCE**: Required for all implementation activities (Rules 01-07, 09)  
**EFFECTIVE**: Immediate implementation  
**FILE GENERATION POLICY**: Option B - Git commit ONLY if tests generated AND 95%+ coverage achieved
