# Implementation Prompt: M1-CORE-DB-06 - Database Audit Logger

**Task ID**: M1-CORE-DB-06  
**Task Name**: Database Audit Logger  
**Milestone**: M1 - Core Infrastructure Foundation  
**Phase**: Phase 1 - Database Infrastructure Foundation  
**Priority**: P0 - Critical Foundation  
**Authority**: President & CEO, E<PERSON><PERSON>. Consultancy  
**Status**: READY FOR IMPLEMENTATION  
**Prerequisite**: M0, M0.1, M0.2, M0.3, M00.2, M0A, M1-CORE-DB-01/02/03/04/05 COMPLETE

---

## MANDATORY OA FRAMEWORK GOVERNANCE RULES

### Rule 01: TypeScript Header Standard V2.3 (GOV-AI-HEADER-001)
**MANDATORY** - Apply OA Standard Header V2.3 with all 13 sections including M0.3 logging integration.

### Rule 02: Development Standard (DEV-FILE-SIZE-001)
**MANDATORY** - File size management:
- Target: ≤700 LOC (GREEN zone)

### Rule 04: Memory Management (MEM-SAFE-002)
**MANDATORY** - Memory-safe patterns:
- MUST extend BaseTrackingService
- Bounded audit log collection with <PERSON>R<PERSON> eviction

### Rule 05: Anti-Simplification (CORE-POLICY-001)
**MANDATORY ZERO-EXCEPTION POLICY**:
- IMPLEMENT ALL audit logging
- INCLUDE operation logging, error tracking, change tracking
- NEVER remove audit features

### Rule 06: Testing Phase (GOV-AI-TEST-001)
**MANDATORY** - Production value

### Rule 07: Code Generation (DEV-CODE-GEN-001)
**MANDATORY FRESH CODE** - Generate from requirements

### Rule 09: Test Generation Type Verification (TEST-TYPE-VER-001)
**MANDATORY TYPE VERIFICATION BEFORE TEST GENERATION** (IF tests generated)

---

## OBJECTIVE

Implement **DatabaseAuditLogger** - Custom OA logging component for database operations. Logs all database operations to M0.3 AuditLoggingConfigurationService, tracks changes with before/after values, maintains compliance audit trail, supports configurable logging levels and retention policies.

### Strategic Importance

1. **Audit Trail**: Complete record of all database operations
2. **Compliance**: Support regulatory compliance requirements
3. **Change Tracking**: Before/after values for all data modifications
4. **Error Logging**: Detailed error context and investigation support
5. **M0.3 Integration**: Leverage M0.3 audit logging infrastructure

---

## TASK OVERVIEW

| Attribute | Value |
|-----------|-------|
| **Reference ID** | M1-CORE-DB-06 |
| **Component Name** | platform-database-audit-logger |
| **File Location** | `server/src/platform/infrastructure/database/database-audit-logger.ts` |
| **Inheritance** | BaseTrackingService |
| **Implements** | IAuditLogger, IAuditableService |
| **File Size Target** | ≤700 LOC (GREEN zone) |
| **Estimated LOC** | 250-350 LOC |
| **Dependencies** | M0.3, M0A, M1-CORE-DB-02, M00.2 |

### Deliverables

**Primary Implementation**:
1. **DatabaseAuditLogger Class**:
   - BaseTrackingService extension
   - M0.3 logging integration
   - Operation logging
   - Change tracking
   - Error logging
   - Retention management

2. **Interfaces & Types**:
   - IAuditLogger, IAuditableService
   - TAuditLogEntry, TChangeRecord, TAuditContext

**Test Suite** (IF generated):
- Operation logging tests
- Change tracking tests
- Error logging tests
- M0.3 integration tests
- Retention policy tests
- 95%+ coverage (Option B: git commit AFTER achievement)

---

## MANDATORY IMPLEMENTATION REQUIREMENTS

### Class Structure
```typescript
export class DatabaseAuditLogger extends BaseTrackingService implements IAuditLogger, IAuditableService {
  private _auditLogger!: AuditLoggingConfigurationService;
  private _auditBuffer!: TAuditLogEntry[];
  private _maxBufferSize!: number;
  private _retentionPolicy!: TRetentionPolicy;
  
  protected async doInitialize(): Promise<void>
  protected async doShutdown(): Promise<void>
  
  // Audit logging
  async logOperation(operation: TDatabaseOperation): Promise<void>
  async logChange(change: TChangeRecord): Promise<void>
  async logError(error: Error, context: TAuditContext): Promise<void>
  
  // Log management
  async flushAuditLog(): Promise<void>
  async getAuditTrail(filters?: TAuditFilters): Promise<TAuditLogEntry[]>
  
  // Retention
  async enforceRetentionPolicy(): Promise<void>
  async purgeOldLogs(olderThan: Date): Promise<number>
}
```

### Core Methods

**Audit Logging** (≤40 LOC each):
- `logOperation()`: Log database operation with details
- `logChange()`: Log data changes with before/after values
- `logError()`: Log errors with context

**Log Management** (≤35 LOC each):
- `flushAuditLog()`: Write buffered logs
- `getAuditTrail()`: Retrieve audit trail

**Retention** (≤30 LOC each):
- `enforceRetentionPolicy()`: Apply retention policy
- `purgeOldLogs()`: Remove expired logs

### Integration Requirements

**M0.3 AuditLoggingConfigurationService**:
- Write audit events to M0.3
- Use configurable logging contexts
- Support dynamic log level configuration
- Enable hot-reload logging changes

**Operation Logging**:
- Log all query executions
- Log transaction lifecycle (begin, commit, rollback)
- Log connection pool events
- Log schema changes

**Change Tracking**:
- Capture before/after values
- Track change timestamps
- Record change initiator
- Link to authority decisions

**Error Logging**:
- Capture error details
- Record operation context
- Link to governance violations
- Support investigation workflows

---

## POST-TASK COMPLETION WORKFLOW (IF TESTS GENERATED)

### STEP 1: Create Completion Tracking JSON
**File**: `docs/governance/tracking/status/.oa-m1-core-db-06-completion.json`

### STEP 2: Update Milestone Tracking
**File**: `docs/governance/tracking/status/.oa-m1-milestone-tracking.json`

### STEP 3: Git Commit (AFTER 95%+ Coverage - Option B)

```
git add server/src/platform/infrastructure/database/database-audit-logger.ts
git add server/src/platform/infrastructure/database/interfaces/IAuditLogger.ts
git add server/src/platform/infrastructure/database/types/TAuditLogEntry.ts
git add server/src/platform/infrastructure/database/constants/audit-logger-constants.ts
git add server/src/platform/infrastructure/database/__tests__/*.test.ts
git add docs/governance/tracking/status/.oa-m1-core-db-06-completion.json

git commit -m "feat(M1-CORE-DB-06): Database Audit Logger Complete

M0.3 audit logging for M1 database operations.

Deliverables:
- database-audit-logger.ts: 250-350 LOC
- M0.3 logging integration
- Operation logging with details
- Change tracking with before/after
- Error logging with context
- Retention policy management
- 95%+ test coverage

Audit Capabilities:
- Complete operation audit trail
- Change tracking for compliance
- Error logging for investigation
- Configurable log levels
- Retention policy enforcement
- Hot-reload logging configuration

Quality:
- 95%+ test coverage
- Zero TypeScript errors
- OA Header V2.3 compliance

M1-CORE-DB Phase Complete:
- M1-CORE-DB-01: Prisma Schema Definition ✓
- M1-CORE-DB-02: Database Service Enhanced ✓
- M1-CORE-DB-03: Database Health Monitor ✓
- M1-CORE-DB-04: Database Initializer ✓
- M1-CORE-DB-05: Database Governance Tracker ✓
- M1-CORE-DB-06: Database Audit Logger ✓

Next Phase: M1-CONFIG (Configuration Management)"
```

---

## SUCCESS CRITERIA

**Task complete when**:
- OA Header V2.3 with 13 sections (Rule 01)
- DatabaseAuditLogger implementation (250-350 LOC) (Rule 02)
- BaseTrackingService inheritance (Rule 04)
- M0.3 logging integration fully functional
- Operation logging functional
- Change tracking functional
- Error logging functional
- Audit trail retrieval functional
- Retention policy enforcement functional
- M0A authority integration
- M00.2 gateway integration
- If test suite: 95%+ coverage BEFORE git commit (Option B - Rule 09)
- Zero TypeScript errors
- All 6 M1-CORE-DB tasks now complete

---

**AUTHORIZATION**: President & CEO, E.Z. Consultancy  
**ENFORCEMENT**: Mandatory for M1-CORE-DB-06 implementation  
**COMPLIANCE**: Required (Rules 01-07, 09)  
**EFFECTIVE**: Immediate implementation  
**FILE GENERATION POLICY**: Option B - Git commit ONLY if tests generated AND 95%+ coverage achieved
